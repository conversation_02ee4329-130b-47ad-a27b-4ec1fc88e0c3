using NBomber.Contracts;
using NBomber.CSharp;
using SmsContract.Models;
using EmailContract.Models;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace NotifyMasterApi.Tests.Performance;

public class NotificationServicePerformanceTests
{
    private readonly ITestOutputHelper _output;
    private const string BaseUrl = "http://localhost:5000";

    public NotificationServicePerformanceTests(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public void SMS_LoadTest_CanHandle100ConcurrentUsers()
    {
        var smsRequest = new SendSmsRequest
        {
            PhoneNumber = "+1234567890",
            Message = "Load test SMS message"
        };

        var scenario = Scenario.Create("sms_load_test", async context =>
        {
            var httpClient = new HttpClient();
            
            var response = await httpClient.PostAsJsonAsync($"{BaseUrl}/sms/send", smsRequest);
            
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromMinutes(1)),
            Simulation.KeepConstant(copies: 100, during: TimeSpan.FromMinutes(2))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert performance requirements
        var smsStats = stats.AllOkCount;
        var errorRate = stats.AllFailCount / (double)(stats.AllOkCount + stats.AllFailCount) * 100;
        
        Assert.True(errorRate < 1.0, $"Error rate {errorRate:F2}% exceeds 1% threshold");
        Assert.True(stats.ScenarioStats[0].Ok.Response.Mean < 1000, 
            $"Average response time {stats.ScenarioStats[0].Ok.Response.Mean}ms exceeds 1000ms threshold");
    }

    [Fact]
    public void Email_LoadTest_CanHandle50ConcurrentUsers()
    {
        var emailRequest = new SendEmailRequest
        {
            To = "<EMAIL>",
            Subject = "Load Test Email",
            Body = "This is a load test email message",
            From = "<EMAIL>"
        };

        var scenario = Scenario.Create("email_load_test", async context =>
        {
            var httpClient = new HttpClient();
            
            var response = await httpClient.PostAsJsonAsync($"{BaseUrl}/email/send", emailRequest);
            
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 5, during: TimeSpan.FromMinutes(1)),
            Simulation.KeepConstant(copies: 50, during: TimeSpan.FromMinutes(2))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert performance requirements
        var errorRate = stats.AllFailCount / (double)(stats.AllOkCount + stats.AllFailCount) * 100;
        
        Assert.True(errorRate < 2.0, $"Error rate {errorRate:F2}% exceeds 2% threshold");
        Assert.True(stats.ScenarioStats[0].Ok.Response.Mean < 2000, 
            $"Average response time {stats.ScenarioStats[0].Ok.Response.Mean}ms exceeds 2000ms threshold");
    }

    [Fact]
    public void BulkSMS_StressTest_CanHandle1000MessagesPerBatch()
    {
        var bulkSmsRequest = new BulkSmsRequest
        {
            Messages = Enumerable.Range(1, 1000).Select(i => new SmsMessageRequest
            {
                PhoneNumber = $"+123456789{i:D3}",
                Message = $"Stress test message {i}"
            }).ToList()
        };

        var scenario = Scenario.Create("bulk_sms_stress_test", async context =>
        {
            var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromMinutes(5); // Increase timeout for bulk operations
            
            var response = await httpClient.PostAsJsonAsync($"{BaseUrl}/sms/send-bulk", bulkSmsRequest);
            
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.KeepConstant(copies: 5, during: TimeSpan.FromMinutes(3))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert performance requirements for bulk operations
        var errorRate = stats.AllFailCount / (double)(stats.AllOkCount + stats.AllFailCount) * 100;
        
        Assert.True(errorRate < 5.0, $"Error rate {errorRate:F2}% exceeds 5% threshold for bulk operations");
        Assert.True(stats.ScenarioStats[0].Ok.Response.Mean < 30000, 
            $"Average response time {stats.ScenarioStats[0].Ok.Response.Mean}ms exceeds 30000ms threshold for bulk operations");
    }

    [Fact]
    public void PluginManagement_LoadTest_CanHandleFrequentOperations()
    {
        var scenario = Scenario.Create("plugin_management_load_test", async context =>
        {
            var httpClient = new HttpClient();
            
            // Randomly choose between different plugin operations
            var operations = new[]
            {
                () => httpClient.GetAsync($"{BaseUrl}/admin/plugins"),
                () => httpClient.GetAsync($"{BaseUrl}/health/plugins"),
                () => httpClient.PostAsync($"{BaseUrl}/admin/plugins/TestPlugin/enable", null),
                () => httpClient.PostAsync($"{BaseUrl}/admin/plugins/TestPlugin/disable", null)
            };

            var randomOperation = operations[Random.Shared.Next(operations.Length)];
            var response = await randomOperation();
            
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 20, during: TimeSpan.FromMinutes(2))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert performance requirements
        var errorRate = stats.AllFailCount / (double)(stats.AllOkCount + stats.AllFailCount) * 100;
        
        Assert.True(errorRate < 1.0, $"Error rate {errorRate:F2}% exceeds 1% threshold");
        Assert.True(stats.ScenarioStats[0].Ok.Response.Mean < 500, 
            $"Average response time {stats.ScenarioStats[0].Ok.Response.Mean}ms exceeds 500ms threshold");
    }

    [Fact]
    public void MixedWorkload_RealisticTest_SimulatesProductionUsage()
    {
        // SMS scenario (60% of traffic)
        var smsScenario = Scenario.Create("mixed_sms", async context =>
        {
            var httpClient = new HttpClient();
            var request = new SendSmsRequest
            {
                PhoneNumber = $"+123456789{Random.Shared.Next(10, 99)}",
                Message = $"Mixed workload SMS {context.InvocationNumber}"
            };
            
            var response = await httpClient.PostAsJsonAsync($"{BaseUrl}/sms/send", request);
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithWeight(60)
        .WithLoadSimulations(
            Simulation.KeepConstant(copies: 30, during: TimeSpan.FromMinutes(3))
        );

        // Email scenario (30% of traffic)
        var emailScenario = Scenario.Create("mixed_email", async context =>
        {
            var httpClient = new HttpClient();
            var request = new SendEmailRequest
            {
                To = $"user{Random.Shared.Next(1, 100)}@example.com",
                Subject = $"Mixed workload email {context.InvocationNumber}",
                Body = "This is a mixed workload test email",
                From = "<EMAIL>"
            };
            
            var response = await httpClient.PostAsJsonAsync($"{BaseUrl}/email/send", request);
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithWeight(30)
        .WithLoadSimulations(
            Simulation.KeepConstant(copies: 15, during: TimeSpan.FromMinutes(3))
        );

        // Admin operations scenario (10% of traffic)
        var adminScenario = Scenario.Create("mixed_admin", async context =>
        {
            var httpClient = new HttpClient();
            
            var operations = new Func<Task<HttpResponseMessage>>[]
            {
                () => httpClient.GetAsync($"{BaseUrl}/admin/plugins"),
                () => httpClient.GetAsync($"{BaseUrl}/health"),
                () => httpClient.GetAsync($"{BaseUrl}/metrics/sms"),
                () => httpClient.GetAsync($"{BaseUrl}/metrics/email")
            };

            var randomOperation = operations[Random.Shared.Next(operations.Length)];
            var response = await randomOperation();
            
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithWeight(10)
        .WithLoadSimulations(
            Simulation.KeepConstant(copies: 5, during: TimeSpan.FromMinutes(3))
        );

        var stats = NBomberRunner
            .RegisterScenarios(smsScenario, emailScenario, adminScenario)
            .Run();

        // Assert overall system performance under mixed workload
        var overallErrorRate = stats.AllFailCount / (double)(stats.AllOkCount + stats.AllFailCount) * 100;
        
        Assert.True(overallErrorRate < 2.0, $"Overall error rate {overallErrorRate:F2}% exceeds 2% threshold");
        
        // Check individual scenario performance
        foreach (var scenarioStats in stats.ScenarioStats)
        {
            Assert.True(scenarioStats.Ok.Response.Mean < 3000, 
                $"Scenario {scenarioStats.ScenarioName} average response time {scenarioStats.Ok.Response.Mean}ms exceeds 3000ms threshold");
        }
    }

    [Fact]
    public void MemoryUsage_PluginLoadUnload_DoesNotLeak()
    {
        var scenario = Scenario.Create("memory_test", async context =>
        {
            var httpClient = new HttpClient();
            
            // Simulate plugin load/unload cycle
            var loadResponse = await httpClient.PostAsJsonAsync($"{BaseUrl}/admin/plugins/load", 
                new { PluginPath = "/test/memory-test-plugin.dll" });
            
            if (!loadResponse.IsSuccessStatusCode)
                return Response.Fail("Plugin load failed");

            await Task.Delay(100); // Brief pause

            var unloadResponse = await httpClient.PostAsync($"{BaseUrl}/admin/plugins/MemoryTestPlugin/unload", null);
            
            return unloadResponse.IsSuccessStatusCode ? Response.Ok() : Response.Fail("Plugin unload failed");
        })
        .WithLoadSimulations(
            Simulation.KeepConstant(copies: 10, during: TimeSpan.FromMinutes(2))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Memory leak detection would require additional tooling
        // For now, we verify the operations complete successfully
        var errorRate = stats.AllFailCount / (double)(stats.AllOkCount + stats.AllFailCount) * 100;
        Assert.True(errorRate < 5.0, $"Plugin load/unload error rate {errorRate:F2}% exceeds 5% threshold");
    }
}
