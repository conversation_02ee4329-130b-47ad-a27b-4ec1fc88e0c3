using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using NotificationContract.Enums;
using NotificationContract.Models;
using NotifyMasterApi.Events.Models;
using NotifyMasterApi.Events.Services;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Services;
using System.Security.Claims;

namespace NotifyMasterApi.Tests.Helpers;

/// <summary>
/// Helper methods and utilities for controller testing
/// </summary>
public static class TestHelpers
{
    /// <summary>
    /// Creates a mock HTTP context with authentication claims
    /// </summary>
    public static HttpContext CreateMockHttpContext(string userId = "test-user", string traceId = "test-trace-id")
    {
        var context = new DefaultHttpContext();
        context.TraceIdentifier = traceId;
        
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, userId),
            new Claim(ClaimTypes.Name, "Test User"),
            new Claim("scope", "notifications:send")
        };
        
        var identity = new ClaimsIdentity(claims, "Test");
        var principal = new ClaimsPrincipal(identity);
        context.User = principal;
        
        return context;
    }

    /// <summary>
    /// Sets up a controller with mock HTTP context
    /// </summary>
    public static void SetupControllerContext<T>(T controller, string userId = "test-user", string traceId = "test-trace-id") 
        where T : ControllerBase
    {
        controller.ControllerContext = new ControllerContext
        {
            HttpContext = CreateMockHttpContext(userId, traceId)
        };
    }

    /// <summary>
    /// Creates mock services for controller testing
    /// </summary>
    public static class MockServices
    {
        public static Mock<IEmailGateway> CreateEmailGateway()
        {
            var mock = new Mock<IEmailGateway>();
            mock.Setup(x => x.GetCurrentProvider()).Returns("TestEmailProvider");
            mock.Setup(x => x.SendEmailAsync(It.IsAny<EmailMessageRequest>()))
                .ReturnsAsync(new EmailResponse(true, null, "test-email-id"));
            return mock;
        }

        public static Mock<ISmsGateway> CreateSmsGateway()
        {
            var mock = new Mock<ISmsGateway>();
            mock.Setup(x => x.GetCurrentProvider()).Returns("TestSmsProvider");
            mock.Setup(x => x.SendSmsAsync(It.IsAny<SmsMessageRequest>()))
                .ReturnsAsync(new SmsResponse(true, null, "test-sms-id"));
            return mock;
        }

        public static Mock<IPushGateway> CreatePushGateway()
        {
            var mock = new Mock<IPushGateway>();
            mock.Setup(x => x.GetCurrentProvider()).Returns("TestPushProvider");
            mock.Setup(x => x.SendPushAsync(It.IsAny<PushMessageRequest>()))
                .ReturnsAsync(new PushResponse(true, null, "test-push-id"));
            return mock;
        }

        public static Mock<INotificationLoggingService> CreateLoggingService()
        {
            var mock = new Mock<INotificationLoggingService>();
            mock.Setup(x => x.LogNotificationAsync(
                It.IsAny<NotificationType>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
                .ReturnsAsync("test-log-id");
            return mock;
        }

        public static Mock<INotificationQueueService> CreateQueueService()
        {
            var mock = new Mock<INotificationQueueService>();
            mock.Setup(x => x.QueueNotificationAsync(It.IsAny<NotificationQueueItem>()))
                .ReturnsAsync("test-queue-id");
            return mock;
        }

        public static Mock<IEventPublisher> CreateEventPublisher()
        {
            var mock = new Mock<IEventPublisher>();
            mock.Setup(x => x.PublishNotificationEventAsync(It.IsAny<NotificationQueuedEvent>()))
                .Returns(Task.CompletedTask);
            mock.Setup(x => x.PublishNotificationEventToTypeAsync(It.IsAny<string>(), It.IsAny<NotificationQueuedEvent>()))
                .Returns(Task.CompletedTask);
            return mock;
        }

        public static Mock<ILogger<T>> CreateLogger<T>()
        {
            return new Mock<ILogger<T>>();
        }
    }

    /// <summary>
    /// Assertion helpers for API responses
    /// </summary>
    public static class AssertHelpers
    {
        public static void AssertSuccessResponse<T>(IActionResult result, string expectedMessage = null)
        {
            var okResult = Assert.IsType<OkObjectResult>(result);
            var response = Assert.IsType<ApiResponse<T>>(okResult.Value);
            
            Assert.True(response.Success);
            if (expectedMessage != null)
            {
                Assert.Equal(expectedMessage, response.Message);
            }
            Assert.NotNull(response.Data);
        }

        public static void AssertErrorResponse(IActionResult result, int expectedStatusCode, string expectedMessage = null)
        {
            if (expectedStatusCode == 400)
            {
                var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
                var response = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
                Assert.False(response.Success);
                if (expectedMessage != null)
                {
                    Assert.Equal(expectedMessage, response.Message);
                }
            }
            else if (expectedStatusCode == 500)
            {
                var statusResult = Assert.IsType<ObjectResult>(result);
                Assert.Equal(500, statusResult.StatusCode);
                var response = Assert.IsType<ApiResponse<object>>(statusResult.Value);
                Assert.False(response.Success);
                if (expectedMessage != null)
                {
                    Assert.Equal(expectedMessage, response.Message);
                }
            }
            else
            {
                throw new ArgumentException($"Unsupported status code: {expectedStatusCode}");
            }
        }

        public static T ExtractResponseData<T>(IActionResult result)
        {
            var okResult = Assert.IsType<OkObjectResult>(result);
            var response = Assert.IsType<ApiResponse<T>>(okResult.Value);
            return response.Data;
        }
    }

    /// <summary>
    /// Performance testing helpers
    /// </summary>
    public static class PerformanceHelpers
    {
        public static async Task<TimeSpan> MeasureExecutionTime(Func<Task> action)
        {
            var startTime = DateTime.UtcNow;
            await action();
            var endTime = DateTime.UtcNow;
            return endTime - startTime;
        }

        public static async Task<TimeSpan> MeasureExecutionTime<T>(Func<Task<T>> action)
        {
            var startTime = DateTime.UtcNow;
            await action();
            var endTime = DateTime.UtcNow;
            return endTime - startTime;
        }

        public static void AssertPerformance(TimeSpan duration, TimeSpan maxExpected, string operationName = "Operation")
        {
            Assert.True(duration <= maxExpected, 
                $"{operationName} took {duration.TotalMilliseconds}ms, expected <= {maxExpected.TotalMilliseconds}ms");
        }
    }

    /// <summary>
    /// Test data generation helpers
    /// </summary>
    public static class DataGenerators
    {
        private static readonly Random _random = new Random();

        public static string GenerateRandomEmail()
        {
            var domains = new[] { "example.com", "test.org", "sample.net" };
            var username = GenerateRandomString(8);
            var domain = domains[_random.Next(domains.Length)];
            return $"{username}@{domain}";
        }

        public static string GenerateRandomPhoneNumber()
        {
            var countryCode = _random.Next(1, 999);
            var number = _random.Next(1000000, 9999999);
            return $"+{countryCode}{number}";
        }

        public static string GenerateRandomDeviceToken()
        {
            return $"device_token_{GenerateRandomString(32)}";
        }

        public static string GenerateRandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[_random.Next(s.Length)]).ToArray());
        }

        public static List<EmailMessageRequest> GenerateBulkEmailRequests(int count)
        {
            return Enumerable.Range(1, count)
                .Select(i => TestDataBuilders.AnEmailRequest()
                    .WithTo(GenerateRandomEmail())
                    .WithSubject($"Bulk Email {i}")
                    .WithBody($"This is bulk email number {i}")
                    .Build())
                .ToList();
        }

        public static List<SmsMessageRequest> GenerateBulkSmsRequests(int count)
        {
            return Enumerable.Range(1, count)
                .Select(i => TestDataBuilders.AnSmsRequest()
                    .WithTo(GenerateRandomPhoneNumber())
                    .WithMessage($"Bulk SMS {i}: {GenerateRandomString(50)}")
                    .Build())
                .ToList();
        }

        public static List<PushMessageRequest> GenerateBulkPushRequests(int count)
        {
            var platforms = new[] { "android", "ios", "web" };
            return Enumerable.Range(1, count)
                .Select(i => TestDataBuilders.APushRequest()
                    .WithDeviceToken(GenerateRandomDeviceToken())
                    .WithTitle($"Bulk Push {i}")
                    .WithBody($"This is bulk push notification number {i}")
                    .WithPlatform(platforms[_random.Next(platforms.Length)])
                    .Build())
                .ToList();
        }
    }

    /// <summary>
    /// Validation helpers for testing input validation
    /// </summary>
    public static class ValidationHelpers
    {
        public static bool IsValidEmail(string email)
        {
            return !string.IsNullOrEmpty(email) && email.Contains("@") && email.Contains(".");
        }

        public static bool IsValidPhoneNumber(string phoneNumber)
        {
            return !string.IsNullOrEmpty(phoneNumber) && phoneNumber.StartsWith("+") && phoneNumber.Length >= 10;
        }

        public static bool IsValidDeviceToken(string deviceToken)
        {
            return !string.IsNullOrEmpty(deviceToken) && deviceToken.Length >= 10;
        }
    }
}
