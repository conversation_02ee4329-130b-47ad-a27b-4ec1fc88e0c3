# 🔄 RabbitMQ to Redis Migration Guide

This document outlines the complete migration from RabbitMQ to Redis for the NotificationService runtime plugin system.

## 📋 Migration Overview

The NotificationService has been successfully migrated from **RabbitMQ** to **Redis** for all messaging and queuing functionality. This change provides:

- ✅ **Simplified Architecture** - Single Redis instance instead of separate RabbitMQ
- ✅ **Better Performance** - Redis's in-memory operations are faster
- ✅ **Reduced Dependencies** - One less service to manage and maintain
- ✅ **Enhanced Features** - Redis pub/sub for real-time events
- ✅ **Cost Efficiency** - Lower resource usage and operational overhead

## 🔧 Changes Made

### **1. Removed RabbitMQ Dependencies**

**Project Files Updated:**
- `src/Services/NotifyMasterApi/NotifyMasterApi.csproj`
  - Removed `MassTransit` package
  - Removed `MassTransit.AspNetCore` package  
  - Removed `MassTransit.RabbitMQ` package

**Configuration Files Updated:**
- `src/Services/NotifyMasterApi/appsettings.json`
  - Removed `RabbitMqSetting` section
- `Tests/Services/EmailService.Tests/appsettings*.json`
  - Removed `RabbitMqSetting` sections
- `Tests/Services/SmsService.Tests/appsettings*.json`
  - Removed `RabbitMqSetting` sections

### **2. Removed MassTransit Code**

**Files Removed:**
- `src/Services/NotifyMasterApi/Presentation/Consumers/NotificationConsumer.cs`

**Files Updated:**
- `src/Services/NotifyMasterApi/Ioc/Configurator.cs`
  - Removed MassTransit service registration
  - Removed RabbitMQ configuration
- `src/Services/NotifyMasterApi/Ioc/Configuration.cs`
  - Removed MassTransit imports
- `src/Services/NotifyMasterApi/ApplicationCore/Services/NotificationService.cs`
  - Replaced `IPublishEndpoint` with `IQueueService`
  - Updated to use Redis queues instead of MassTransit publishing

### **3. Implemented Redis Queue Service**

**New Files Created:**
- `src/Services/NotifyMasterApi/Services/RedisQueueService.cs`
  - Complete Redis-based queue and pub/sub implementation
  - Replaces all RabbitMQ functionality

**Key Features:**
```csharp
public interface IQueueService
{
    // Queue Operations (replaces RabbitMQ queues)
    Task PublishAsync<T>(string queueName, T message, CancellationToken cancellationToken = default);
    Task<T?> ConsumeAsync<T>(string queueName, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> ConsumeBatchAsync<T>(string queueName, int batchSize = 10, CancellationToken cancellationToken = default);
    Task<long> GetQueueLengthAsync(string queueName);
    Task<bool> DeleteQueueAsync(string queueName);
    
    // Pub/Sub Operations (new functionality)
    Task SubscribeAsync<T>(string channel, Func<T, Task> handler, CancellationToken cancellationToken = default);
    Task PublishEventAsync<T>(string channel, T eventData, CancellationToken cancellationToken = default);
}
```

### **4. Predefined Queue and Channel Names**

**Queue Names:**
```csharp
public static class QueueNames
{
    public const string SmsQueue = "sms-notifications";
    public const string EmailQueue = "email-notifications";
    public const string PushQueue = "push-notifications";
    public const string BulkSmsQueue = "bulk-sms-notifications";
    public const string BulkEmailQueue = "bulk-email-notifications";
    public const string BulkPushQueue = "bulk-push-notifications";
    public const string DeadLetterQueue = "dead-letter-queue";
    public const string RetryQueue = "retry-queue";
}
```

**Channel Names (New Feature):**
```csharp
public static class ChannelNames
{
    public const string NotificationSent = "notification-sent";
    public const string NotificationFailed = "notification-failed";
    public const string PluginLoaded = "plugin-loaded";
    public const string PluginUnloaded = "plugin-unloaded";
    public const string HealthStatusChanged = "health-status-changed";
    public const string MetricsUpdated = "metrics-updated";
}
```

### **5. Enhanced Message Models**

**New Message Types:**
```csharp
// Queue message wrapper
public record QueuedNotification(
    string Id,
    string Type,
    object Payload,
    DateTime QueuedAt,
    int RetryCount = 0,
    DateTime? ScheduledFor = null
);

// Real-time event models
public record NotificationEvent(string NotificationId, string Type, string Status, string? Provider, DateTime Timestamp, string? ErrorMessage = null);
public record PluginEvent(string PluginName, string Action, bool Success, DateTime Timestamp, string? ErrorMessage = null);
public record HealthStatusEvent(string ComponentName, string Status, DateTime Timestamp, Dictionary<string, object>? Details = null);
public record MetricsEvent(string MetricType, Dictionary<string, object> Metrics, DateTime Timestamp);
```

### **6. Updated Service Registration**

**In `Configurator.cs`:**
```csharp
// Add Redis Queue Service (replaces RabbitMQ)
services.AddSingleton<IQueueService, RedisQueueService>();
services.AddSingleton<INotificationQueueService, RedisNotificationQueueService>();
services.AddHostedService<NotificationProcessingService>();
```

### **7. Updated Notification Service**

**Before (RabbitMQ/MassTransit):**
```csharp
public NotificationService(IPublishEndpoint publishEndpoint)
{
    _publishEndpoint = publishEndpoint;
}

public async Task SendAsync(SendNotificationRequest request)
{
    Task sendEmail = _publishEndpoint.Publish<SendEmailRequest>(request);
    // ... more MassTransit publishing
}
```

**After (Redis):**
```csharp
public NotificationService(IQueueService queueService)
{
    _queueService = queueService;
}

public async Task SendAsync(SendNotificationRequest request)
{
    if(request.NotificationTypes.Contains(NotificationType.Email))
    {
        var emailRequest = (SendEmailRequest)request;
        tasks.Add(_queueService.PublishAsync(QueueNames.EmailQueue, emailRequest));
    }
    // ... more Redis queue publishing
}
```

## 🧪 Testing Updates

**New Test Files:**
- `Tests/NotifyMasterApi.Tests/Services/RedisQueueServiceTests.cs`
  - Comprehensive Redis queue service tests
  - Uses Testcontainers for real Redis testing
  - Tests all queue and pub/sub functionality

**Test Coverage:**
- ✅ Queue operations (publish, consume, batch consume)
- ✅ Pub/sub operations (subscribe, publish events)
- ✅ Message serialization/deserialization
- ✅ Predefined queue and channel names
- ✅ Error handling and edge cases
- ✅ Real-time event processing

## 🚀 Benefits of Migration

### **Performance Improvements**
- **Faster Message Processing** - Redis in-memory operations
- **Lower Latency** - Direct Redis operations vs RabbitMQ protocol overhead
- **Better Throughput** - Redis can handle higher message volumes

### **Operational Benefits**
- **Simplified Deployment** - One less service to deploy and manage
- **Reduced Resource Usage** - Lower memory and CPU footprint
- **Easier Monitoring** - Single Redis instance to monitor
- **Cost Savings** - Reduced infrastructure requirements

### **Feature Enhancements**
- **Real-time Events** - Redis pub/sub for instant notifications
- **Better Observability** - Direct access to queue lengths and metrics
- **Flexible Patterns** - Support for both queue and pub/sub patterns
- **Atomic Operations** - Redis transactions for batch operations

## 📊 Architecture Comparison

### **Before (RabbitMQ)**
```
NotificationService → MassTransit → RabbitMQ → Consumer Services
                                 ↓
                            Queue Management
                            Exchange Routing
                            Dead Letter Queues
```

### **After (Redis)**
```
NotificationService → RedisQueueService → Redis → Background Services
                                        ↓
                                   Queue Operations
                                   Pub/Sub Events
                                   Real-time Notifications
```

## 🔧 Configuration Changes

### **Redis Connection**
```json
{
  "ConnectionStrings": {
    "Redis": "localhost:6379"
  }
}
```

### **Queue Configuration**
- **Queue Keys**: `queue:{queueName}`
- **Channel Keys**: `channel:{channelName}`
- **Message Format**: JSON serialization
- **Persistence**: Redis persistence settings apply

## 🎯 Migration Checklist

- ✅ Remove RabbitMQ packages and dependencies
- ✅ Remove MassTransit configuration and services
- ✅ Implement Redis queue service
- ✅ Update notification service to use Redis
- ✅ Create comprehensive test suite
- ✅ Update documentation
- ✅ Verify build and functionality
- ✅ Update deployment scripts (if needed)

## 🔮 Future Enhancements

With Redis as the messaging backbone, future enhancements can include:

- **Message Scheduling** - Redis sorted sets for delayed messages
- **Priority Queues** - Multiple priority levels using Redis lists
- **Message Deduplication** - Redis sets for duplicate detection
- **Rate Limiting** - Redis counters for throttling
- **Circuit Breaker** - Redis-based circuit breaker patterns
- **Distributed Locking** - Redis locks for coordination

## 📝 Notes

- **Backward Compatibility**: The API remains unchanged for consumers
- **Message Format**: JSON serialization maintains compatibility
- **Error Handling**: Improved error handling with Redis operations
- **Monitoring**: Redis monitoring tools can be used for observability
- **Scaling**: Redis Cluster can be used for horizontal scaling

The migration to Redis provides a more efficient, simpler, and feature-rich messaging solution for the NotificationService runtime plugin system! 🚀
