using Microsoft.AspNetCore.Mvc;
using NotifyMasterApi.Services;

namespace NotifyMasterApi.Controllers;

/// <summary>
/// System status controller for monitoring fallback mechanisms
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class SystemStatusController : ControllerBase
{
    private readonly ILogger<SystemStatusController> _logger;
    private readonly RedisConnectionService _redisService;
    private readonly InMemoryDatabaseService _databaseService;

    public SystemStatusController(
        ILogger<SystemStatusController> logger,
        RedisConnectionService redisService,
        InMemoryDatabaseService databaseService)
    {
        _logger = logger;
        _redisService = redisService;
        _databaseService = databaseService;
    }

    /// <summary>
    /// Get overall system status including fallback mechanisms
    /// </summary>
    [HttpGet]
    public IActionResult GetSystemStatus()
    {
        var status = new
        {
            Timestamp = DateTime.UtcNow,
            Application = new
            {
                Name = "NotifyMasterApi",
                Version = "1.0.0",
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development"
            },
            Redis = _redisService.GetStatus(),
            Database = _databaseService.GetDatabaseStatus(),
            Fallbacks = new
            {
                RedisUsingFallback = _redisService.IsUsingFallback,
                DatabaseUsingFallback = _databaseService.IsUsingInMemoryFallback,
                AnyFallbackActive = _redisService.IsUsingFallback || _databaseService.IsUsingInMemoryFallback
            },
            Health = new
            {
                Status = "Healthy",
                Message = GetHealthMessage()
            }
        };

        return Ok(status);
    }

    /// <summary>
    /// Get Redis-specific status
    /// </summary>
    [HttpGet("redis")]
    public IActionResult GetRedisStatus()
    {
        var status = _redisService.GetStatus();
        return Ok(status);
    }

    /// <summary>
    /// Get Database-specific status
    /// </summary>
    [HttpGet("database")]
    public IActionResult GetDatabaseStatus()
    {
        var status = _databaseService.GetDatabaseStatus();
        return Ok(status);
    }

    /// <summary>
    /// Get fallback status summary
    /// </summary>
    [HttpGet("fallbacks")]
    public IActionResult GetFallbackStatus()
    {
        var status = new
        {
            Redis = new
            {
                IsUsingFallback = _redisService.IsUsingFallback,
                Status = _redisService.IsUsingFallback ? "In-Memory Fallback" : "Connected",
                Details = _redisService.GetStatus()
            },
            Database = new
            {
                IsUsingFallback = _databaseService.IsUsingInMemoryFallback,
                Status = _databaseService.IsUsingInMemoryFallback ? "In-Memory Fallback" : "Connected",
                Details = _databaseService.GetDatabaseStatus()
            },
            Summary = new
            {
                AnyFallbackActive = _redisService.IsUsingFallback || _databaseService.IsUsingInMemoryFallback,
                Message = GetFallbackMessage(),
                Recommendations = GetRecommendations()
            }
        };

        return Ok(status);
    }

    /// <summary>
    /// Clear fallback data (for testing purposes)
    /// </summary>
    [HttpPost("fallbacks/clear")]
    public IActionResult ClearFallbackData()
    {
        try
        {
            _redisService.ClearFallbackData();
            _databaseService.ClearData();

            _logger.LogInformation("🗑️ Fallback data cleared by user request");

            return Ok(new
            {
                Message = "Fallback data cleared successfully",
                Timestamp = DateTime.UtcNow,
                ClearedSystems = new[]
                {
                    _redisService.IsUsingFallback ? "Redis (In-Memory)" : null,
                    _databaseService.IsUsingInMemoryFallback ? "Database (In-Memory)" : null
                }.Where(x => x != null)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clear fallback data");
            return StatusCode(500, new { Message = "Failed to clear fallback data", Error = ex.Message });
        }
    }

    /// <summary>
    /// Test Redis connection
    /// </summary>
    [HttpPost("redis/test")]
    public async Task<IActionResult> TestRedisConnection()
    {
        try
        {
            var testKey = $"test_{DateTime.UtcNow.Ticks}";
            var testValue = "test_value";

            var setResult = await _redisService.SetStringAsync(testKey, testValue);
            var getValue = await _redisService.GetStringAsync(testKey);
            var deleteResult = await _redisService.DeleteKeyAsync(testKey);

            return Ok(new
            {
                TestResult = "Success",
                Operations = new
                {
                    Set = setResult,
                    Get = getValue == testValue,
                    Delete = deleteResult
                },
                UsingFallback = _redisService.IsUsingFallback,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis connection test failed");
            return StatusCode(500, new { Message = "Redis test failed", Error = ex.Message });
        }
    }

    private string GetHealthMessage()
    {
        if (!_redisService.IsUsingFallback && !_databaseService.IsUsingInMemoryFallback)
            return "All systems operational";

        if (_redisService.IsUsingFallback && _databaseService.IsUsingInMemoryFallback)
            return "Running on fallback systems (Redis + Database in-memory)";

        if (_redisService.IsUsingFallback)
            return "Running with Redis fallback (in-memory)";

        if (_databaseService.IsUsingInMemoryFallback)
            return "Running with Database fallback (in-memory)";

        return "Operational";
    }

    private string GetFallbackMessage()
    {
        var fallbacks = new List<string>();
        
        if (_redisService.IsUsingFallback)
            fallbacks.Add("Redis using in-memory fallback");
        
        if (_databaseService.IsUsingInMemoryFallback)
            fallbacks.Add("Database using in-memory fallback");

        if (fallbacks.Count == 0)
            return "No fallbacks active - all systems connected";

        return $"Active fallbacks: {string.Join(", ", fallbacks)}";
    }

    private string[] GetRecommendations()
    {
        var recommendations = new List<string>();

        if (_redisService.IsUsingFallback)
        {
            recommendations.Add("Start Redis server and restart application for full Redis functionality");
            recommendations.Add("Current Redis operations are using in-memory storage (data will be lost on restart)");
        }

        if (_databaseService.IsUsingInMemoryFallback)
        {
            recommendations.Add("Configure database connection and restart application for persistent storage");
            recommendations.Add("Current database operations are using in-memory storage (data will be lost on restart)");
        }

        if (recommendations.Count == 0)
        {
            recommendations.Add("All systems are operating normally");
        }

        return recommendations.ToArray();
    }
}
