# 🏗️ NotificationService Architecture Diagrams

## System Overview

This document provides visual representations of the NotificationService architecture, including normal operations, fallback mechanisms, and data flow.

## 🎯 High-Level Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        WEB[Web App]
        MOB[Mobile App]
        API_CLIENT[API Client]
    end

    subgraph "NotifyMaster<PERSON>pi (Core)"
        GATEWAY[API Gateway]
        CONTROLLERS[Controllers]
        SERVICES[Core Services]
        PLUGIN_MGR[Plugin Manager]
    end

    subgraph "Plugin System"
        EMAIL_PLUGIN[Email Plugin<br/>SendGrid]
        SMS_PLUGIN[SMS Plugin<br/>Twilio/BulkSms]
        PUSH_PLUGIN[Push Plugin<br/>FCM/APNS]
    end

    subgraph "Infrastructure"
        REDIS[(Redis<br/>Message Queue)]
        POSTGRES[(PostgreSQL<br/>Database)]
        MONITORING[Health Checks<br/>Metrics]
    end

    subgraph "Fallback Systems"
        REDIS_FALLBACK[In-Memory Redis]
        DB_FALLBACK[In-Memory Database]
        PORT_FALLBACK[Port Auto-Discovery]
    end

    WEB --> GATEWAY
    MOB --> GATEWAY
    API_CLIENT --> GATEWAY

    GATEWAY --> CONTROLLERS
    CONTROLLERS --> SERVICES
    SERVICES --> PLUGIN_MGR

    PLUGIN_MGR --> EMAIL_PLUGIN
    PLUGIN_MGR --> SMS_PLUGIN
    PLUGIN_MGR --> PUSH_PLUGIN

    SERVICES --> REDIS
    SERVICES --> POSTGRES
    SERVICES --> MONITORING

    REDIS -.->|Fallback| REDIS_FALLBACK
    POSTGRES -.->|Fallback| DB_FALLBACK
    GATEWAY -.->|Port Conflict| PORT_FALLBACK

    style FALLBACK_SYSTEMS fill:#ffe6e6
    style REDIS_FALLBACK fill:#ffe6e6
    style DB_FALLBACK fill:#ffe6e6
    style PORT_FALLBACK fill:#ffe6e6
```

## 🔄 Fallback System Architecture

```mermaid
graph TB
    subgraph "Application Startup"
        START[Application Start]
        PORT_CHECK{Port Available?}
        REDIS_CHECK{Redis Available?}
        DB_CHECK{Database Available?}
    end

    subgraph "Port Fallback"
        PORT_5120[Try Port 5120]
        PORT_5121[Try Port 5121]
        PORT_5122[Try Port 5122]
        PORT_RANDOM[Find Random Port]
    end

    subgraph "Redis Fallback"
        REDIS_CONN[Redis Connection]
        REDIS_MOCK[Mock Redis Service]
        REDIS_MEMORY[In-Memory Storage]
    end

    subgraph "Database Fallback"
        PG_CONN[PostgreSQL Connection]
        INMEM_DB[In-Memory Database]
        EF_MEMORY[EF Core InMemory]
    end

    subgraph "Application Ready"
        APP_READY[Application Ready]
        STATUS_API[Status API Available]
        HEALTH_CHECKS[Health Checks Active]
    end

    START --> PORT_CHECK
    PORT_CHECK -->|Yes| REDIS_CHECK
    PORT_CHECK -->|No| PORT_5120
    PORT_5120 -->|Occupied| PORT_5121
    PORT_5121 -->|Occupied| PORT_5122
    PORT_5122 -->|Occupied| PORT_RANDOM
    PORT_RANDOM --> REDIS_CHECK

    REDIS_CHECK -->|Available| REDIS_CONN
    REDIS_CHECK -->|Unavailable| REDIS_MOCK
    REDIS_MOCK --> REDIS_MEMORY
    REDIS_CONN --> DB_CHECK
    REDIS_MEMORY --> DB_CHECK

    DB_CHECK -->|Available| PG_CONN
    DB_CHECK -->|Unavailable| INMEM_DB
    INMEM_DB --> EF_MEMORY
    PG_CONN --> APP_READY
    EF_MEMORY --> APP_READY

    APP_READY --> STATUS_API
    APP_READY --> HEALTH_CHECKS

    style PORT_5120 fill:#ffe6e6
    style PORT_5121 fill:#ffe6e6
    style PORT_5122 fill:#ffe6e6
    style PORT_RANDOM fill:#ffe6e6
    style REDIS_MOCK fill:#ffe6e6
    style REDIS_MEMORY fill:#ffe6e6
    style INMEM_DB fill:#ffe6e6
    style EF_MEMORY fill:#ffe6e6
```

## 📊 Data Flow Architecture

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant PluginMgr as Plugin Manager
    participant Queue as Redis/Fallback
    participant Plugin as Notification Plugin
    participant Provider as External Provider

    Client->>API: Send Notification Request
    API->>API: Validate Request
    API->>Queue: Queue Message
    
    Note over Queue: Redis or In-Memory Fallback
    
    Queue->>PluginMgr: Dequeue Message
    PluginMgr->>PluginMgr: Route to Plugin
    PluginMgr->>Plugin: Execute Notification
    Plugin->>Provider: Send via External API
    Provider-->>Plugin: Response
    Plugin-->>PluginMgr: Result
    PluginMgr->>Queue: Update Status
    Queue->>API: Status Update
    API-->>Client: Response
```

## 🔌 Plugin System Architecture

```mermaid
graph TB
    subgraph "Plugin Core"
        PLUGIN_INTERFACE[INotificationPlugin]
        PLUGIN_MANAGER[RuntimePluginManager]
        PLUGIN_LOADER[PluginAutoLoaderService]
    end

    subgraph "Plugin Implementations"
        EMAIL_SENDGRID[Plugin.Email.SendGrid]
        SMS_TWILIO[Plugin.Sms.Twilio]
        SMS_BULKSMS[Plugin.Sms.BulkSms]
        PUSH_FCM[Plugin.Push.FCM]
    end

    subgraph "Plugin Contracts"
        EMAIL_CONTRACT[EmailContract]
        SMS_CONTRACT[SmsContract]
        PUSH_CONTRACT[PushNotificationContract]
    end

    subgraph "Plugin Metadata"
        MANIFEST[Plugin Manifest]
        CONFIG[Plugin Configuration]
        HEALTH[Plugin Health Checks]
    end

    PLUGIN_MANAGER --> PLUGIN_INTERFACE
    PLUGIN_LOADER --> PLUGIN_MANAGER

    EMAIL_SENDGRID --> EMAIL_CONTRACT
    SMS_TWILIO --> SMS_CONTRACT
    SMS_BULKSMS --> SMS_CONTRACT
    PUSH_FCM --> PUSH_CONTRACT

    EMAIL_CONTRACT --> PLUGIN_INTERFACE
    SMS_CONTRACT --> PLUGIN_INTERFACE
    PUSH_CONTRACT --> PLUGIN_INTERFACE

    EMAIL_SENDGRID --> MANIFEST
    SMS_TWILIO --> MANIFEST
    SMS_BULKSMS --> MANIFEST
    PUSH_FCM --> MANIFEST

    MANIFEST --> CONFIG
    MANIFEST --> HEALTH

    style PLUGIN_INTERFACE fill:#e6f3ff
    style EMAIL_SENDGRID fill:#e6ffe6
    style SMS_TWILIO fill:#e6ffe6
    style SMS_BULKSMS fill:#e6ffe6
    style PUSH_FCM fill:#e6ffe6
```

## 🏥 Health Check & Monitoring Architecture

```mermaid
graph TB
    subgraph "Health Check System"
        HEALTH_SERVICE["Health Check Service"]
        READY_ENDPOINT["/health/ready"]
        LIVE_ENDPOINT["/health/live"]
    end

    subgraph "System Status API"
        STATUS_CONTROLLER["SystemStatusController"]
        REDIS_STATUS["Redis Status"]
        DB_STATUS["Database Status"]
        FALLBACK_STATUS["Fallback Status"]
    end

    subgraph "Monitoring Services"
        REDIS_MONITOR["Redis Connection Monitor"]
        DB_MONITOR["Database Monitor"]
        PLUGIN_MONITOR["Plugin Health Monitor"]
    end

    subgraph "Fallback Detection"
        REDIS_DETECTOR["Redis Fallback Detector"]
        DB_DETECTOR["Database Fallback Detector"]
        PORT_DETECTOR["Port Fallback Detector"]
    end

    subgraph "Status Reporting"
        CONSOLE_LOG["Console Logging"]
        STATUS_API["Status API Endpoints"]
        HEALTH_API["Health API Endpoints"]
    end

    HEALTH_SERVICE --> READY_ENDPOINT
    HEALTH_SERVICE --> LIVE_ENDPOINT

    STATUS_CONTROLLER --> REDIS_STATUS
    STATUS_CONTROLLER --> DB_STATUS
    STATUS_CONTROLLER --> FALLBACK_STATUS

    REDIS_MONITOR --> REDIS_DETECTOR
    DB_MONITOR --> DB_DETECTOR
    PLUGIN_MONITOR --> STATUS_CONTROLLER

    REDIS_DETECTOR --> CONSOLE_LOG
    DB_DETECTOR --> CONSOLE_LOG
    PORT_DETECTOR --> CONSOLE_LOG

    STATUS_CONTROLLER --> STATUS_API
    HEALTH_SERVICE --> HEALTH_API

    style REDIS_DETECTOR fill:#ffe6e6
    style DB_DETECTOR fill:#ffe6e6
    style PORT_DETECTOR fill:#ffe6e6
```


## 🔄 Redis Fallback Data Flow

```mermaid
graph TB
    subgraph "Redis Operations"
        APP[Application]
        REDIS_SERVICE[RedisConnectionService]
        REDIS_CHECK{Redis Available?}
    end

    subgraph "Normal Redis Flow"
        REDIS_CONN[Redis Connection]
        REDIS_DB[Redis Database]
        REDIS_OPS[Redis Operations]
    end

    subgraph "Fallback Redis Flow"
        MEMORY_STORE[In-Memory Store]
        CONCURRENT_DICT[ConcurrentDictionary]
        FALLBACK_OPS[Fallback Operations]
    end

    subgraph "Operations"
        STRING_OPS[String Operations<br/>GET, SET, DELETE]
        LIST_OPS[List Operations<br/>LPUSH, RPOP]
        PUBSUB_OPS[Pub/Sub Operations<br/>PUBLISH, SUBSCRIBE]
    end

    APP --> REDIS_SERVICE
    REDIS_SERVICE --> REDIS_CHECK

    REDIS_CHECK -->|Available| REDIS_CONN
    REDIS_CHECK -->|Unavailable| MEMORY_STORE

    REDIS_CONN --> REDIS_DB
    REDIS_DB --> REDIS_OPS

    MEMORY_STORE --> CONCURRENT_DICT
    CONCURRENT_DICT --> FALLBACK_OPS

    REDIS_OPS --> STRING_OPS
    REDIS_OPS --> LIST_OPS
    REDIS_OPS --> PUBSUB_OPS

    FALLBACK_OPS --> STRING_OPS
    FALLBACK_OPS --> LIST_OPS
    FALLBACK_OPS --> PUBSUB_OPS

    style MEMORY_STORE fill:#ffe6e6
    style CONCURRENT_DICT fill:#ffe6e6
    style FALLBACK_OPS fill:#ffe6e6
```

## 🗄️ Database Fallback Architecture

```mermaid
graph TB
    subgraph "Database Configuration"
        APP_START[Application Startup]
        DB_CONFIG[Database Configuration]
        CONNECTION_TEST{Connection Test}
    end

    subgraph "Normal Database Flow"
        POSTGRES[PostgreSQL]
        EF_POSTGRES[EF Core PostgreSQL]
        PERSISTENT_DATA[Persistent Data]
    end

    subgraph "Fallback Database Flow"
        INMEMORY_DB[In-Memory Database]
        EF_INMEMORY[EF Core InMemory]
        TEMP_DATA[Temporary Data]
    end

    subgraph "Database Operations"
        NOTIFICATION_LOG[Notification Logging]
        PLUGIN_LOG[Plugin Logging]
        SYSTEM_LOG[System Logging]
        CRUD_OPS[CRUD Operations]
    end

    subgraph "Fallback Services"
        INMEMORY_SERVICE[InMemoryDatabaseService]
        FALLBACK_ACTIVATION[DatabaseFallbackActivationService]
        STATUS_TRACKING[Status Tracking]
    end

    APP_START --> DB_CONFIG
    DB_CONFIG --> CONNECTION_TEST

    CONNECTION_TEST -->|Success| POSTGRES
    CONNECTION_TEST -->|Failure| INMEMORY_DB

    POSTGRES --> EF_POSTGRES
    EF_POSTGRES --> PERSISTENT_DATA

    INMEMORY_DB --> EF_INMEMORY
    EF_INMEMORY --> TEMP_DATA
    INMEMORY_DB --> INMEMORY_SERVICE

    PERSISTENT_DATA --> NOTIFICATION_LOG
    PERSISTENT_DATA --> PLUGIN_LOG
    PERSISTENT_DATA --> SYSTEM_LOG
    PERSISTENT_DATA --> CRUD_OPS

    TEMP_DATA --> NOTIFICATION_LOG
    TEMP_DATA --> PLUGIN_LOG
    TEMP_DATA --> SYSTEM_LOG
    TEMP_DATA --> CRUD_OPS

    INMEMORY_SERVICE --> FALLBACK_ACTIVATION
    FALLBACK_ACTIVATION --> STATUS_TRACKING

    style INMEMORY_DB fill:#ffe6e6
    style EF_INMEMORY fill:#ffe6e6
    style TEMP_DATA fill:#ffe6e6
    style INMEMORY_SERVICE fill:#ffe6e6
    style FALLBACK_ACTIVATION fill:#ffe6e6
```

## 🚀 Deployment Architecture

```mermaid
graph TB
    subgraph "Development Environment"
        DEV_APP[NotifyMasterApi]
        DEV_FALLBACKS[All Fallbacks Active]
        DEV_INMEMORY[In-Memory Everything]
    end

    subgraph "Testing Environment"
        TEST_APP[NotifyMasterApi]
        TEST_REDIS[Redis Container]
        TEST_DB[PostgreSQL Container]
        TEST_PARTIAL[Partial Fallbacks]
    end

    subgraph "Production Environment"
        PROD_APP[NotifyMasterApi]
        PROD_REDIS[Redis Cluster]
        PROD_DB[PostgreSQL Cluster]
        PROD_MONITORING[Full Monitoring]
        PROD_BACKUP[Backup Systems]
    end

    subgraph "Fallback Monitoring"
        ALERTS[Fallback Alerts]
        DASHBOARD[Status Dashboard]
        RECOVERY[Recovery Procedures]
    end

    DEV_APP --> DEV_FALLBACKS
    DEV_FALLBACKS --> DEV_INMEMORY

    TEST_APP --> TEST_REDIS
    TEST_APP --> TEST_DB
    TEST_APP --> TEST_PARTIAL

    PROD_APP --> PROD_REDIS
    PROD_APP --> PROD_DB
    PROD_APP --> PROD_MONITORING
    PROD_MONITORING --> PROD_BACKUP

    PROD_MONITORING --> ALERTS
    ALERTS --> DASHBOARD
    DASHBOARD --> RECOVERY

    style DEV_FALLBACKS fill:#ffe6e6
    style DEV_INMEMORY fill:#ffe6e6
    style TEST_PARTIAL fill:#fff3cd
    style ALERTS fill:#f8d7da
```

## 📈 Performance Impact Diagram

```mermaid
graph TB
    subgraph "Performance Comparison"
        NORMAL[Normal Operations]
        FALLBACK[Fallback Operations]
    end

    subgraph "Redis Performance"
        REDIS_NORMAL[Network I/O<br/>~50ms latency]
        REDIS_FALLBACK[In-Memory<br/>~0.1ms latency]
        REDIS_IMPROVEMENT[95% Faster]
    end

    subgraph "Database Performance"
        DB_NORMAL[Disk I/O<br/>~10ms latency]
        DB_FALLBACK[Memory I/O<br/>~0.5ms latency]
        DB_IMPROVEMENT[80% Faster]
    end

    subgraph "Trade-offs"
        SPEED[⚡ Speed Increase]
        PERSISTENCE[❌ No Persistence]
        MEMORY[📈 Memory Usage]
        RELIABILITY[✅ High Availability]
    end

    NORMAL --> REDIS_NORMAL
    NORMAL --> DB_NORMAL

    FALLBACK --> REDIS_FALLBACK
    FALLBACK --> DB_FALLBACK

    REDIS_NORMAL --> REDIS_IMPROVEMENT
    REDIS_FALLBACK --> REDIS_IMPROVEMENT

    DB_NORMAL --> DB_IMPROVEMENT
    DB_FALLBACK --> DB_IMPROVEMENT

    FALLBACK --> SPEED
    FALLBACK --> PERSISTENCE
    FALLBACK --> MEMORY
    FALLBACK --> RELIABILITY

    style REDIS_FALLBACK fill:#e6ffe6
    style DB_FALLBACK fill:#e6ffe6
    style SPEED fill:#e6ffe6
    style RELIABILITY fill:#e6ffe6
    style PERSISTENCE fill:#ffe6e6
    style MEMORY fill:#fff3cd
```
