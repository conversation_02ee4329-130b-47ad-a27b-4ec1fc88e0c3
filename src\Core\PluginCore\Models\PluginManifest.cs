using System.Text.Json.Serialization;

namespace PluginCore.Models;

public class PluginManifest
{
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("version")]
    public string Version { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("author")]
    public string Author { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty; // Email, Sms, Push

    [JsonPropertyName("provider")]
    public string Provider { get; set; } = string.Empty; // Sendgrid, Twilio, etc.

    [JsonPropertyName("assemblyName")]
    public string AssemblyName { get; set; } = string.Empty;

    [JsonPropertyName("entryPoint")]
    public string EntryPoint { get; set; } = string.Empty; // Main class implementing the interface

    [JsonPropertyName("dependencies")]
    public List<PluginDependency> Dependencies { get; set; } = new();

    [JsonPropertyName("configuration")]
    public Dictionary<string, PluginConfigurationItem> Configuration { get; set; } = new();

    [JsonPropertyName("supportedFeatures")]
    public List<string> SupportedFeatures { get; set; } = new();

    [JsonPropertyName("minimumFrameworkVersion")]
    public string MinimumFrameworkVersion { get; set; } = "net9.0";

    [JsonPropertyName("isEnabled")]
    public bool IsEnabled { get; set; } = true;

    [JsonPropertyName("priority")]
    public int Priority { get; set; } = 100; // Lower number = higher priority

    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class PluginDependency
{
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("version")]
    public string Version { get; set; } = string.Empty;

    [JsonPropertyName("isRequired")]
    public bool IsRequired { get; set; } = true;
}

public class PluginConfigurationItem
{
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty; // string, int, bool, secret

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("defaultValue")]
    public object? DefaultValue { get; set; }

    [JsonPropertyName("isRequired")]
    public bool IsRequired { get; set; } = true;

    [JsonPropertyName("isSecret")]
    public bool IsSecret { get; set; } = false;

    [JsonPropertyName("validationRules")]
    public List<string> ValidationRules { get; set; } = new();
}
