using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace PluginCore.Models;

/// <summary>
/// Represents a plugin manifest containing metadata and configuration for a notification service plugin.
/// This manifest is embedded within plugin assemblies and defines how the plugin integrates with the system.
/// </summary>
/// <remarks>
/// The manifest supports various plugin types including Email, SMS, and Push notification providers.
/// Each plugin follows the naming convention: Plugin.{Type}.{Provider} (e.g., Plugin.Email.SendGrid).
/// </remarks>
public sealed class PluginManifest
{
    /// <summary>
    /// Gets or sets the unique name of the plugin.
    /// </summary>
    /// <example>Plugin.Email.SendGrid</example>
    [JsonPropertyName("name")]
    [JsonRequired]
    [Required(ErrorMessage = "Plugin name is required")]
    [StringLength(100, MinimumLength = 3, ErrorMessage = "Plugin name must be between 3 and 100 characters")]
    [RegularExpression(@"^Plugin\.[A-Za-z]+\.[A-Za-z]+$", ErrorMessage = "Plugin name must follow the pattern 'Plugin.{Type}.{Provider}'")]
    public required string Name { get; init; }

    /// <summary>
    /// Gets or sets the semantic version of the plugin.
    /// </summary>
    /// <example>1.0.0</example>
    [JsonPropertyName("version")]
    [JsonRequired]
    [Required(ErrorMessage = "Plugin version is required")]
    [RegularExpression(@"^\d+\.\d+\.\d+(?:-[a-zA-Z0-9\-\.]+)?$", ErrorMessage = "Version must follow semantic versioning (e.g., 1.0.0 or 1.0.0-beta)")]
    public required string Version { get; init; }

    /// <summary>
    /// Gets or sets a human-readable description of the plugin's functionality.
    /// </summary>
    [JsonPropertyName("description")]
    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the author or organization that created the plugin.
    /// </summary>
    [JsonPropertyName("author")]
    [StringLength(100, ErrorMessage = "Author name cannot exceed 100 characters")]
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the type of notification service this plugin provides.
    /// </summary>
    /// <remarks>
    /// Supported types: Email, Sms, Push
    /// </remarks>
    [JsonPropertyName("type")]
    [JsonRequired]
    [Required(ErrorMessage = "Plugin type is required")]
    [RegularExpression(@"^(Email|Sms|Push)$", ErrorMessage = "Type must be one of: Email, Sms, Push")]
    public required string Type { get; init; }

    /// <summary>
    /// Gets or sets the specific provider implementation (e.g., SendGrid, Twilio, Clickatel).
    /// </summary>
    [JsonPropertyName("provider")]
    [JsonRequired]
    [Required(ErrorMessage = "Provider name is required")]
    [StringLength(50, MinimumLength = 2, ErrorMessage = "Provider name must be between 2 and 50 characters")]
    public required string Provider { get; init; }

public class PluginDependency
{
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("version")]
    public string Version { get; set; } = string.Empty;

    [JsonPropertyName("isRequired")]
    public bool IsRequired { get; set; } = true;
}

public class PluginConfigurationItem
{
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty; // string, int, bool, secret

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("defaultValue")]
    public object? DefaultValue { get; set; }

    [JsonPropertyName("isRequired")]
    public bool IsRequired { get; set; } = true;

    [JsonPropertyName("isSecret")]
    public bool IsSecret { get; set; } = false;

    [JsonPropertyName("validationRules")]
    public List<string> ValidationRules { get; set; } = new();
}
