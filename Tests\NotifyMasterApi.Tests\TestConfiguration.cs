using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using NotifyMasterApi.Data;
using NotifyMasterApi.Interfaces;
using Moq;
using Testcontainers.PostgreSql;
using Testcontainers.Redis;

namespace NotifyMasterApi.Tests;

/// <summary>
/// Provides comprehensive test configuration and setup for integration and end-to-end tests.
/// </summary>
public class TestConfiguration
{
    /// <summary>
    /// Creates a test configuration with in-memory services and mocked dependencies.
    /// </summary>
    /// <returns>A configured IConfiguration instance for testing</returns>
    public static IConfiguration CreateTestConfiguration()
    {
        var configurationBuilder = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                // Database configuration
                ["ConnectionStrings:DefaultConnection"] = "Host=localhost;Database=NotifyMasterApi_Test;Username=test;Password=test",
                ["ConnectionStrings:RedisConnection"] = "localhost:6379",
                
                // Plugin configuration
                ["Plugins:Directory"] = Path.Combine(Path.GetTempPath(), "test-plugins"),
                ["Plugins:ConfigDirectory"] = Path.Combine(Path.GetTempPath(), "test-plugin-configs"),
                ["Plugins:EnableHotReload"] = "true",
                ["Plugins:LoadOnStartup"] = "false",
                
                // Notification processing configuration
                ["NotificationProcessing:MaxConcurrentProcessing"] = "2",
                ["NotificationProcessing:ProcessingIntervalMs"] = "100",
                ["NotificationProcessing:MaxRetryAttempts"] = "2",
                ["NotificationProcessing:RetryDelayMs"] = "1000",
                
                // Runtime notification configuration
                ["RuntimeNotification:ProviderCooldownMinutes"] = "1",
                ["RuntimeNotification:DefaultTimeoutSeconds"] = "10",
                
                // Logging configuration
                ["Logging:LogLevel:Default"] = "Information",
                ["Logging:LogLevel:Microsoft"] = "Warning",
                ["Logging:LogLevel:NotifyMasterApi"] = "Debug",
                
                // Email service configuration
                ["EmailService:DefaultProvider"] = "TestProvider",
                ["EmailService:MaxRetries"] = "2",
                ["EmailService:TimeoutSeconds"] = "10",
                
                // SMS service configuration
                ["SmsService:DefaultProvider"] = "TestProvider",
                ["SmsService:MaxRetries"] = "2",
                ["SmsService:TimeoutSeconds"] = "10",
                
                // Push notification service configuration
                ["PushService:DefaultProvider"] = "TestProvider",
                ["PushService:MaxRetries"] = "2",
                ["PushService:TimeoutSeconds"] = "10",
                
                // Health check configuration
                ["HealthChecks:Enabled"] = "true",
                ["HealthChecks:DetailedErrors"] = "true",
                
                // Metrics configuration
                ["Metrics:Enabled"] = "true",
                ["Metrics:ExportIntervalSeconds"] = "5"
            });

        return configurationBuilder.Build();
    }

    /// <summary>
    /// Creates a test service collection with mocked dependencies.
    /// </summary>
    /// <returns>A configured IServiceCollection for testing</returns>
    public static IServiceCollection CreateTestServices()
    {
        var services = new ServiceCollection();
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // Add configuration
        services.AddSingleton(CreateTestConfiguration());
        
        // Add mocked services
        services.AddScoped(_ => Mock.Of<INotificationQueueService>());
        services.AddScoped(_ => Mock.Of<INotificationLoggingService>());
        services.AddScoped(_ => Mock.Of<IPluginManager>());
        
        // Add in-memory database
        services.AddDbContext<NotificationDbContext>(options =>
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));
        
        return services;
    }
}

/// <summary>
/// Custom WebApplicationFactory for integration tests with test-specific configuration.
/// </summary>
/// <typeparam name="TStartup">The startup class type</typeparam>
public class TestWebApplicationFactory<TStartup> : WebApplicationFactory<TStartup> where TStartup : class
{
    private PostgreSqlContainer? _postgresContainer;
    private RedisContainer? _redisContainer;
    private readonly bool _useContainers;

    /// <summary>
    /// Initializes a new instance of the TestWebApplicationFactory.
    /// </summary>
    /// <param name="useContainers">Whether to use test containers for external dependencies</param>
    public TestWebApplicationFactory(bool useContainers = false)
    {
        _useContainers = useContainers;
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            // Remove the existing DbContext registration
            var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<NotificationDbContext>));
            if (descriptor != null)
            {
                services.Remove(descriptor);
            }

            // Add test database
            if (_useContainers && _postgresContainer != null)
            {
                services.AddDbContext<NotificationDbContext>(options =>
                    options.UseNpgsql(_postgresContainer.GetConnectionString()));
            }
            else
            {
                services.AddDbContext<NotificationDbContext>(options =>
                    options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));
            }

            // Replace services with test implementations
            ReplaceService<INotificationQueueService>(services, Mock.Of<INotificationQueueService>());
            ReplaceService<INotificationLoggingService>(services, Mock.Of<INotificationLoggingService>());
        });

        builder.UseEnvironment("Testing");
        builder.UseConfiguration(TestConfiguration.CreateTestConfiguration());
    }

    /// <summary>
    /// Starts the test containers if configured to use them.
    /// </summary>
    public async Task StartContainersAsync()
    {
        if (!_useContainers) return;

        _postgresContainer = new PostgreSqlBuilder()
            .WithDatabase("NotifyMasterApi_Test")
            .WithUsername("test")
            .WithPassword("test")
            .Build();

        _redisContainer = new RedisBuilder()
            .Build();

        await _postgresContainer.StartAsync();
        await _redisContainer.StartAsync();
    }

    /// <summary>
    /// Stops the test containers.
    /// </summary>
    public async Task StopContainersAsync()
    {
        if (_postgresContainer != null)
        {
            await _postgresContainer.StopAsync();
            await _postgresContainer.DisposeAsync();
        }

        if (_redisContainer != null)
        {
            await _redisContainer.StopAsync();
            await _redisContainer.DisposeAsync();
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            StopContainersAsync().GetAwaiter().GetResult();
        }
        base.Dispose(disposing);
    }

    /// <summary>
    /// Replaces a service registration with a test implementation.
    /// </summary>
    /// <typeparam name="TService">The service type to replace</typeparam>
    /// <param name="services">The service collection</param>
    /// <param name="implementation">The test implementation</param>
    private static void ReplaceService<TService>(IServiceCollection services, TService implementation) where TService : class
    {
        var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(TService));
        if (descriptor != null)
        {
            services.Remove(descriptor);
        }
        services.AddScoped(_ => implementation);
    }
}

/// <summary>
/// Base class for integration tests with common setup and teardown.
/// </summary>
public abstract class IntegrationTestBase : IAsyncLifetime
{
    protected TestWebApplicationFactory<Program> Factory { get; private set; } = null!;
    protected HttpClient Client { get; private set; } = null!;

    /// <summary>
    /// Initializes the test environment.
    /// </summary>
    public virtual async Task InitializeAsync()
    {
        Factory = new TestWebApplicationFactory<Program>(useContainers: false);
        await Factory.StartContainersAsync();
        Client = Factory.CreateClient();
    }

    /// <summary>
    /// Cleans up the test environment.
    /// </summary>
    public virtual async Task DisposeAsync()
    {
        Client?.Dispose();
        if (Factory != null)
        {
            await Factory.StopContainersAsync();
            Factory.Dispose();
        }
    }
}

/// <summary>
/// Test data factory for creating test objects with realistic data.
/// </summary>
public static class TestDataFactory
{
    private static readonly Random Random = new();

    /// <summary>
    /// Creates a random email address for testing.
    /// </summary>
    /// <returns>A valid test email address</returns>
    public static string CreateTestEmail()
    {
        var domains = new[] { "example.com", "test.org", "sample.net" };
        var username = $"test{Random.Next(1000, 9999)}";
        var domain = domains[Random.Next(domains.Length)];
        return $"{username}@{domain}";
    }

    /// <summary>
    /// Creates a random phone number for testing.
    /// </summary>
    /// <returns>A valid test phone number</returns>
    public static string CreateTestPhoneNumber()
    {
        return $"+1{Random.Next(200, 999)}{Random.Next(200, 999)}{Random.Next(1000, 9999)}";
    }

    /// <summary>
    /// Creates random text content for testing.
    /// </summary>
    /// <param name="minLength">Minimum length of the text</param>
    /// <param name="maxLength">Maximum length of the text</param>
    /// <returns>Random text content</returns>
    public static string CreateTestText(int minLength = 10, int maxLength = 100)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 ";
        var length = Random.Next(minLength, maxLength + 1);
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[Random.Next(s.Length)]).ToArray());
    }
}
