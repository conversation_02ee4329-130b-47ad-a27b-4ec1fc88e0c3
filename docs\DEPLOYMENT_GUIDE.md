# 🚀 Deployment Guide

Complete deployment guide for the NotificationService across different environments and platforms.

## 🎯 Deployment Options Overview

| Environment | Complexity | Infrastructure | Fallbacks | Best For |
|-------------|------------|----------------|-----------|----------|
| **Development** | ⭐ | None required | All active | Local development |
| **Docker Local** | ⭐⭐ | Docker only | Optional | Testing with containers |
| **Docker Compose** | ⭐⭐⭐ | Docker + services | None | Full local stack |
| **Cloud (Azure)** | ⭐⭐⭐⭐ | Managed services | None | Production |
| **Kubernetes** | ⭐⭐⭐⭐⭐ | K8s cluster | Configurable | Enterprise scale |

## 🏠 Local Development Deployment

### Zero Infrastructure (Fallback Mode)
```bash
# Clone and run immediately
git clone https://github.com/bloodchild8906/NotificationService-master.git
cd NotificationService-master
dotnet run --project src/Services/NotifyMasterApi

# ✅ Runs with all fallback systems active
# ✅ No external dependencies required
# ✅ Perfect for development and testing
```

**What you get:**
- ✅ Full API functionality
- ✅ In-memory Redis operations
- ✅ In-memory database
- ✅ Automatic port fallback
- ✅ Complete plugin system

## 🐳 Docker Deployment

### Single Container Deployment

#### 1. Create Dockerfile
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 5120

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["src/Services/NotifyMasterApi/NotifyMasterApi.csproj", "src/Services/NotifyMasterApi/"]
COPY ["src/Core/PluginCore/PluginCore.csproj", "src/Core/PluginCore/"]
COPY ["src/Contracts/", "src/Contracts/"]
COPY ["src/Libraries/", "src/Libraries/"]
RUN dotnet restore "src/Services/NotifyMasterApi/NotifyMasterApi.csproj"
COPY . .
WORKDIR "/src/src/Services/NotifyMasterApi"
RUN dotnet build "NotifyMasterApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "NotifyMasterApi.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "NotifyMasterApi.dll"]
```

#### 2. Build and Run
```bash
# Build the image
docker build -t notifymaster-api .

# Run with fallbacks (no external dependencies)
docker run -d \
  --name notifymaster-api \
  -p 5120:5120 \
  -e ASPNETCORE_ENVIRONMENT=Production \
  -e CONNECTIONSTRINGS__REDIS="localhost:9999" \
  -e CONNECTIONSTRINGS__DEFAULTCONNECTION="" \
  notifymaster-api

# Verify it's running
curl http://localhost:5120/health/ready
```

### Docker Compose Deployment (Full Stack)

#### 1. Create docker-compose.yml
```yaml
version: '3.8'

services:
  notifymaster-api:
    build: .
    ports:
      - "5120:5120"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - CONNECTIONSTRINGS__REDIS=redis:6379
      - CONNECTIONSTRINGS__DEFAULTCONNECTION=Host=postgres;Database=NotifyMaster;Username=postgres;Password=NotifyMaster123!
      - PLUGINSETTINGS__PLUGINDIRECTORY=/app/plugins
    volumes:
      - ./plugins:/app/plugins
      - ./logs:/app/logs
    depends_on:
      - redis
      - postgres
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=NotifyMaster
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=NotifyMaster123!
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  # Optional: Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis

  # Optional: pgAdmin for PostgreSQL management
  pgadmin:
    image: dpage/pgadmin4
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
    ports:
      - "8080:80"
    depends_on:
      - postgres

volumes:
  redis_data:
  postgres_data:
```

#### 2. Deploy the Stack
```bash
# Start the entire stack
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f notifymaster-api

# Verify deployment
curl http://localhost:5120/health/ready
curl http://localhost:5120/api/systemstatus
```

#### 3. Access Management Tools
- **API**: http://localhost:5120
- **Swagger**: http://localhost:5120/swagger
- **Redis Commander**: http://localhost:8081
- **pgAdmin**: http://localhost:8080

## ☁️ Cloud Deployment (Azure)

### Azure Container Instances (Simple)

#### 1. Create Resource Group
```bash
# Login to Azure
az login

# Create resource group
az group create --name NotifyMasterRG --location eastus
```

#### 2. Deploy Container
```bash
# Deploy with fallbacks (no external dependencies)
az container create \
  --resource-group NotifyMasterRG \
  --name notifymaster-api \
  --image your-registry/notifymaster-api:latest \
  --dns-name-label notifymaster-unique \
  --ports 5120 \
  --environment-variables \
    ASPNETCORE_ENVIRONMENT=Production \
    CONNECTIONSTRINGS__REDIS=localhost:9999 \
    CONNECTIONSTRINGS__DEFAULTCONNECTION="" \
  --cpu 1 \
  --memory 2

# Get the FQDN
az container show --resource-group NotifyMasterRG --name notifymaster-api --query ipAddress.fqdn
```

### Azure App Service (Recommended)

#### 1. Create App Service Plan
```bash
# Create App Service Plan
az appservice plan create \
  --name NotifyMasterPlan \
  --resource-group NotifyMasterRG \
  --sku B1 \
  --is-linux

# Create Web App
az webapp create \
  --resource-group NotifyMasterRG \
  --plan NotifyMasterPlan \
  --name notifymaster-api-unique \
  --deployment-container-image-name your-registry/notifymaster-api:latest
```

#### 2. Configure App Settings
```bash
# Configure application settings
az webapp config appsettings set \
  --resource-group NotifyMasterRG \
  --name notifymaster-api-unique \
  --settings \
    ASPNETCORE_ENVIRONMENT=Production \
    CONNECTIONSTRINGS__REDIS="your-redis-connection" \
    CONNECTIONSTRINGS__DEFAULTCONNECTION="your-db-connection"
```

#### 3. Add Managed Services
```bash
# Create Azure Cache for Redis
az redis create \
  --resource-group NotifyMasterRG \
  --name notifymaster-redis \
  --location eastus \
  --sku Basic \
  --vm-size c0

# Create Azure Database for PostgreSQL
az postgres server create \
  --resource-group NotifyMasterRG \
  --name notifymaster-postgres \
  --admin-user postgres \
  --admin-password NotifyMaster123! \
  --sku-name B_Gen5_1 \
  --version 13
```

## ⚙️ Kubernetes Deployment

### 1. Create Namespace
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: notifymaster
```

### 2. Create ConfigMap
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: notifymaster-config
  namespace: notifymaster
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  PLUGINSETTINGS__PLUGINDIRECTORY: "/app/plugins"
  LOGGING__LOGLEVEL__DEFAULT: "Information"
```

### 3. Create Secret
```yaml
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: notifymaster-secrets
  namespace: notifymaster
type: Opaque
stringData:
  CONNECTIONSTRINGS__REDIS: "redis:6379"
  CONNECTIONSTRINGS__DEFAULTCONNECTION: "Host=postgres;Database=NotifyMaster;Username=postgres;Password=NotifyMaster123!"
```

### 4. Create Deployment
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notifymaster-api
  namespace: notifymaster
spec:
  replicas: 3
  selector:
    matchLabels:
      app: notifymaster-api
  template:
    metadata:
      labels:
        app: notifymaster-api
    spec:
      containers:
      - name: notifymaster-api
        image: your-registry/notifymaster-api:latest
        ports:
        - containerPort: 5120
        envFrom:
        - configMapRef:
            name: notifymaster-config
        - secretRef:
            name: notifymaster-secrets
        livenessProbe:
          httpGet:
            path: /health/live
            port: 5120
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 5120
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

### 5. Create Service
```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: notifymaster-api-service
  namespace: notifymaster
spec:
  selector:
    app: notifymaster-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 5120
  type: LoadBalancer
```

### 6. Deploy to Kubernetes
```bash
# Apply all configurations
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f secret.yaml
kubectl apply -f deployment.yaml
kubectl apply -f service.yaml

# Check deployment status
kubectl get pods -n notifymaster
kubectl get services -n notifymaster

# Get external IP
kubectl get service notifymaster-api-service -n notifymaster
```

## 🔧 Production Configuration

### Environment Variables for Production
```bash
# Core settings
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=http://+:5120

# Database (use managed service)
CONNECTIONSTRINGS__DEFAULTCONNECTION="Host=your-postgres-server;Database=NotifyMaster;Username=postgres;Password=secure_password"

# Redis (use managed service)
CONNECTIONSTRINGS__REDIS="your-redis-server:6379,password=secure_password"

# Plugin settings
PLUGINSETTINGS__PLUGINDIRECTORY="/app/plugins"
PLUGINSETTINGS__ENABLEHOTRELOAD=false
PLUGINSETTINGS__AUTOLOADPLUGINS=true

# Performance settings
NOTIFICATIONSETTINGS__MAXCONCURRENTNOTIFICATIONS=1000
NOTIFICATIONSETTINGS__ENABLEBATCHING=true
NOTIFICATIONSETTINGS__BATCHSIZE=100

# Security
KESTREL__CERTIFICATES__DEFAULT__PATH="/app/certificates/certificate.pfx"
KESTREL__CERTIFICATES__DEFAULT__PASSWORD="certificate_password"
```

## 📊 Monitoring & Health Checks

### Health Check Endpoints
```bash
# Application health
curl https://your-domain.com/health/live
curl https://your-domain.com/health/ready

# System status
curl https://your-domain.com/api/systemstatus
curl https://your-domain.com/api/systemstatus/fallbacks
```

### Monitoring Setup
```yaml
# monitoring.yaml (Kubernetes)
apiVersion: v1
kind: Service
metadata:
  name: notifymaster-monitoring
  namespace: notifymaster
  labels:
    app: notifymaster-api
spec:
  ports:
  - name: metrics
    port: 5120
    targetPort: 5120
  selector:
    app: notifymaster-api
```

## 🔒 Security Considerations

### Production Security Checklist
- [ ] HTTPS enabled with valid certificates
- [ ] Authentication configured
- [ ] CORS properly configured
- [ ] Secrets stored securely (not in config files)
- [ ] Network security groups configured
- [ ] Database access restricted
- [ ] Redis access secured
- [ ] Plugin directory secured
- [ ] Logging configured appropriately
- [ ] Health check endpoints secured

### SSL/TLS Configuration
```json
{
  "Kestrel": {
    "Endpoints": {
      "Https": {
        "Url": "https://+:5121",
        "Certificate": {
          "Path": "/app/certificates/certificate.pfx",
          "Password": "certificate_password"
        }
      }
    }
  }
}
```

## 🚀 Deployment Verification

### Post-Deployment Checklist
```bash
# 1. Health checks
curl https://your-domain.com/health/ready
curl https://your-domain.com/health/live

# 2. API functionality
curl https://your-domain.com/api/systemstatus

# 3. Swagger documentation
curl https://your-domain.com/swagger/index.html

# 4. Plugin system
curl https://your-domain.com/api/admin/plugins

# 5. Fallback status (should show no fallbacks in production)
curl https://your-domain.com/api/systemstatus/fallbacks

# 6. Performance test
time curl https://your-domain.com/api/systemstatus
```

### Expected Production Output
```json
{
  "Application": {
    "Name": "NotifyMasterApi",
    "Version": "1.0.0",
    "Environment": "Production"
  },
  "Redis": {
    "IsConnected": true,
    "IsUsingFallback": false,
    "ConnectionStatus": "Connected"
  },
  "Database": {
    "IsUsingInMemoryFallback": false
  },
  "Fallbacks": {
    "AnyFallbackActive": false
  },
  "Health": {
    "Status": "Healthy",
    "Message": "All systems operational"
  }
}
```

## 🔄 Rollback Procedures

### Quick Rollback
```bash
# Docker
docker stop notifymaster-api
docker run -d --name notifymaster-api-old your-registry/notifymaster-api:previous-tag

# Kubernetes
kubectl rollout undo deployment/notifymaster-api -n notifymaster

# Azure App Service
az webapp deployment slot swap --resource-group NotifyMasterRG --name notifymaster-api --slot staging --target-slot production
```

## 📈 Scaling Considerations

### Horizontal Scaling
- Use load balancer for multiple instances
- Redis handles shared state
- Database supports concurrent connections
- Plugin system is stateless

### Vertical Scaling
- Increase CPU/memory based on load
- Monitor performance metrics
- Adjust batch sizes and concurrency limits

**🎉 Your NotificationService is now deployed and ready for production!**
