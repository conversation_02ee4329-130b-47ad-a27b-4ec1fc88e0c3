
using EmailContract.Models;
using NotificationContract.Enums;
using PushNotificationContract.Models;
using SmsContract.Models;

namespace NotificationContract.Models;
/// <summary>
///  Model for representing a request to send a notification
/// </summary>
/// <param name="Id"> Id of the notification </param>
/// <param name="NotificationTypes"> Types of the notification </param>
/// <param name="Title"> Title of the notification </param>
/// <param name="ReceptorName"> Name of the recipient </param>
/// <param name="Message"> Message of the notification </param>
/// <param name="Email"> Email address of the recipient </param>
/// <param name="PhoneNumber"> Phone number of the recipient </param>
/// <param name="DeviceToken"> Device token of the recipient </param>
public sealed record SendNotificationRequest
{
    public required string Id { get; set; }
    public required NotificationType[] NotificationTypes { get; set; }
    public string? Title { get;  set; }
    public string? ReceptorName { get;  set; }
    public required string Message { get;  set;}
    public string? Email { get; set;}
    public string? PhoneNumber { get; set; }
    public string? DeviceToken { get; set;}
    /// <summary>
    ///  Converts the notification request to an email request
    /// </summary>
    /// <param name="notification"> Notification request to convert </param>
    public static explicit operator SendEmailRequest(SendNotificationRequest notification)
    {
        if (!notification.NotificationTypes.Contains(NotificationType.Email))
            throw new Exception("This type can't be converted to emailRequest");
        
        if (string.IsNullOrWhiteSpace(notification.Email))
            throw new ArgumentNullException(nameof(notification.Email),message:"For sending an email, email field shouldn't be null");
        
        if (string.IsNullOrWhiteSpace(notification.Message))
            throw new ArgumentNullException(nameof(notification.Message),message:"For sending an email, message field shouldn't be null");
        
        if (string.IsNullOrWhiteSpace(notification.ReceptorName))
            throw new ArgumentNullException(nameof(notification.ReceptorName),message:"For sending an email, receptorName field shouldn't be null");
        
        if (string.IsNullOrWhiteSpace(notification.Title))
            throw new ArgumentNullException(nameof(notification.Title),message:"For sending an email, title field shouldn't be null");

        SendEmailRequest emailRequest = new(notification.Email,notification.ReceptorName, notification.Title, notification.Message);
        return emailRequest;
    }
    /// <summary>
    ///  Converts the notification request to an sms request
    /// </summary>
    /// <param name="notification"> Notification request to convert  </param>
    public static explicit operator SendSmsRequest(SendNotificationRequest notification)
    {
        if (!notification.NotificationTypes.Contains(NotificationType.Sms))
            throw new Exception("This type can't be converted to smsRequest");

        if (string.IsNullOrWhiteSpace(notification.PhoneNumber))
            throw new ArgumentNullException(nameof(notification.PhoneNumber), message: "For sending a sms, phoneNumber field shouldn't be null");

        if (string.IsNullOrWhiteSpace(notification.Message))
            throw new ArgumentNullException(nameof(notification.Message), message: "For sending a sms, message field shouldn't be null");

        SendSmsRequest smsRequest = new(notification.PhoneNumber, notification.Message);
        return smsRequest;
    }
    /// <summary>
    ///  Converts the notification request to a push message request
    /// </summary>
    /// <param name="notification"> Notification request to convert </param>
    public static explicit operator SendPushMessageRequest(SendNotificationRequest notification)
    {
        if (!notification.NotificationTypes.Contains(NotificationType.PushMessage))
            throw new Exception("This type can't be converted to pushMessageRequest");

        if (string.IsNullOrWhiteSpace(notification.DeviceToken))
            throw new ArgumentNullException(nameof(notification), message: "For sending a push message, deviceToken field is required");

        if (string.IsNullOrWhiteSpace(notification.Title))
            throw new ArgumentNullException(nameof(notification), message: "For sending a push message, title field is required");

        if (string.IsNullOrWhiteSpace(notification.Message))
            throw new ArgumentNullException(nameof(notification), message: "For sending a push message, message field is required");

        SendPushMessageRequest pushMessageRequestRequest = new(notification.DeviceToken, notification.Title, notification.Message);
        return pushMessageRequestRequest;
    }
    
}

