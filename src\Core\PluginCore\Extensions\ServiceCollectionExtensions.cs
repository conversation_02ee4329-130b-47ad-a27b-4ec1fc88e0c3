using Microsoft.Extensions.DependencyInjection;
using PluginContract.Interfaces;
using PluginCore.Services;

namespace PluginCore.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddPluginSystem(this IServiceCollection services)
    {
        services.AddSingleton<IPluginManager, PluginManager>();
        services.AddScoped<NotificationService>();
        
        return services;
    }
}
