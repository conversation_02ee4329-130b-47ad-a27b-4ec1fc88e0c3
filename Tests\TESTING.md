# Testing Documentation

This document provides comprehensive information about the testing infrastructure and practices for the NotificationService project.

## Overview

The testing strategy follows a multi-layered approach with unit tests, integration tests, and end-to-end tests to ensure comprehensive coverage and reliability.

### Testing Layers

1. **Unit Tests** - Test individual components in isolation
2. **Integration Tests** - Test component interactions and API endpoints
3. **End-to-End Tests** - Test complete user workflows
4. **Performance Tests** - Test system performance under load

## Test Structure

```
tests/
├── NotifyMasterApi.Tests/           # Main API tests
│   ├── Services/                    # Service layer unit tests
│   ├── Controllers/                 # Controller unit tests
│   ├── Integration/                 # Integration tests
│   ├── EndToEnd/                   # End-to-end tests
│   ├── Performance/                # Performance tests
│   ├── Helpers/                    # Test utilities and helpers
│   └── TestData/                   # Test data files
├── Contracts/                      # Contract model tests
├── Libraries/                      # Library tests
├── Services/                       # Individual service tests
└── README.md                       # This file
```

## Testing Technologies

- **xUnit** - Primary testing framework
- **Moq** - Mocking framework for dependencies
- **FluentAssertions** - Assertion library for readable tests
- **AutoFixture** - Test data generation
- **Microsoft.AspNetCore.Mvc.Testing** - Integration testing for ASP.NET Core
- **Testcontainers** - Docker containers for integration tests
- **WireMock.Net** - HTTP service mocking
- **NBomber** - Performance and load testing

## Running Tests

### Prerequisites

- .NET 9.0 SDK
- Docker (for integration tests with containers)
- PostgreSQL (optional, for full integration tests)
- Redis (optional, for full integration tests)

### Command Line

```bash
# Run all tests
dotnet test

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test project
dotnet test tests/NotifyMasterApi.Tests/

# Run tests by category
dotnet test --filter Category=Unit
dotnet test --filter Category=Integration
dotnet test --filter Category=Performance

# Run tests with detailed output
dotnet test --logger "console;verbosity=detailed"
```

### PowerShell Script

Use the provided PowerShell script for comprehensive testing:

```powershell
# Run all tests with coverage and reporting
.\tests\run-tests.ps1

# Run only unit tests
.\tests\run-tests.ps1 -TestType Unit

# Run with containers
.\tests\run-tests.ps1 -UseContainers
```

## Test Categories

### Unit Tests

Unit tests focus on testing individual components in isolation with mocked dependencies.

**Characteristics:**
- Fast execution (< 100ms per test)
- No external dependencies
- High code coverage
- Deterministic results

**Examples:**
- `NotificationProcessingServiceTests` - Tests the notification processing service
- `RuntimeNotificationServiceTests` - Tests the runtime notification service
- `PluginManagerTests` - Tests the plugin management functionality

### Integration Tests

Integration tests verify that components work correctly together and test API endpoints.

**Characteristics:**
- Test component interactions
- Use test databases (in-memory or containers)
- Validate API contracts
- Test error handling and edge cases

**Examples:**
- `EmailControllerIntegrationTests` - Tests email API endpoints
- `SmsControllerIntegrationTests` - Tests SMS API endpoints
- `PluginManagementIntegrationTests` - Tests plugin management APIs

### End-to-End Tests

End-to-end tests validate complete user workflows from API to data persistence.

**Characteristics:**
- Test complete user scenarios
- Use realistic test data
- Validate business logic
- Test cross-cutting concerns

### Performance Tests

Performance tests ensure the system meets performance requirements under various load conditions.

**Characteristics:**
- Load testing with NBomber
- Stress testing
- Scalability testing
- Resource utilization monitoring

## Test Data Management

### AutoFixture

AutoFixture is used for generating test data automatically:

```csharp
[Theory]
[AutoData]
public async Task SendEmail_WithValidRequest_ShouldReturnSuccess(SendEmailRequest request)
{
    // Test implementation
}
```

### Test Data Factory

The `TestDataFactory` class provides methods for creating realistic test data:

```csharp
var email = TestDataFactory.CreateTestEmail();
var phoneNumber = TestDataFactory.CreateTestPhoneNumber();
var text = TestDataFactory.CreateTestText(50, 200);
```

### Test Data Files

Static test data is stored in the `TestData` directory and automatically copied to the output directory.

## Mocking Strategy

### Service Mocking

Services are mocked using Moq to isolate units under test:

```csharp
var mockEmailService = new Mock<IEmailService>();
mockEmailService.Setup(s => s.SendEmailAsync(It.IsAny<SendEmailRequest>()))
           .ReturnsAsync(new EmailResponse { IsSuccess = true });
```

### HTTP Mocking

External HTTP services are mocked using WireMock.Net:

```csharp
var server = WireMockServer.Start();
server.Given(Request.Create().WithPath("/api/send"))
      .RespondWith(Response.Create().WithStatusCode(200));
```

## Test Configuration

### Test Settings

Test-specific configuration is provided through `TestConfiguration.cs`:

- In-memory databases
- Mocked external services
- Test-specific timeouts and limits
- Logging configuration

### Environment Variables

Tests can be configured using environment variables:

- `USE_CONTAINERS` - Use Docker containers for integration tests
- `TEST_LOG_LEVEL` - Set logging level for tests
- `TEST_TIMEOUT` - Set test timeout in seconds

## Continuous Integration

### GitHub Actions

The project includes GitHub Actions workflows for:

- Running tests on pull requests
- Code coverage reporting
- Performance regression testing
- Security scanning

### Test Reports

Test results and coverage reports are generated and published:

- Test results in JUnit format
- Code coverage in Cobertura format
- Performance test reports
- Security scan results

## Best Practices

### Test Naming

Use descriptive test names that clearly indicate:
- The method being tested
- The scenario being tested
- The expected outcome

```csharp
[Fact]
public async Task SendEmail_WithInvalidEmailAddress_ShouldReturnBadRequest()
```

### Test Organization

- Group related tests in the same test class
- Use nested classes for complex scenarios
- Keep tests focused and independent
- Use setup and teardown methods appropriately

### Assertions

Use FluentAssertions for readable and maintainable assertions:

```csharp
result.Should().NotBeNull();
result.IsSuccess.Should().BeTrue();
result.ErrorMessage.Should().BeNullOrEmpty();
```

### Test Data

- Use AutoFixture for simple test data generation
- Create specific test data for edge cases
- Avoid hardcoded values when possible
- Use realistic data that represents actual usage

## Troubleshooting

### Common Issues

1. **Test Timeouts**
   - Increase timeout values in test configuration
   - Check for deadlocks or infinite loops
   - Verify external service availability

2. **Flaky Tests**
   - Identify timing-dependent code
   - Use proper synchronization
   - Mock time-dependent operations

3. **Container Issues**
   - Ensure Docker is running
   - Check container resource limits
   - Verify network connectivity

### Debugging Tests

- Use Visual Studio Test Explorer for debugging
- Add logging to understand test execution
- Use breakpoints in test code
- Check test output for error details

## Coverage Goals

- **Unit Tests**: > 90% code coverage
- **Integration Tests**: > 80% API coverage
- **End-to-End Tests**: > 95% critical path coverage
- **Performance Tests**: All critical endpoints tested

## Contributing

When adding new features:

1. Write unit tests for new components
2. Add integration tests for new APIs
3. Update end-to-end tests for new workflows
4. Consider performance implications
5. Update this documentation as needed

For more information, see the main project README and contributing guidelines.
