# 🔔 NotificationService API Endpoints Guide

This document provides a comprehensive overview of all API endpoints available in the NotificationService with runtime plugin system.

## 📋 Table of Contents

1. [Base URL & Authentication](#base-url--authentication)
2. [Email Endpoints](#email-endpoints)
3. [SMS Endpoints](#sms-endpoints)
4. [Push Notification Endpoints](#push-notification-endpoints)
5. [Plugin Management Endpoints](#plugin-management-endpoints)
6. [Health & Monitoring Endpoints](#health--monitoring-endpoints)
7. [Admin Endpoints](#admin-endpoints)
8. [Metrics Endpoints](#metrics-endpoints)

## 🌐 Base URL & Authentication

### Base URLs
- **Production**: `https://api.notificationservice.com`
- **Staging**: `https://staging-api.notificationservice.com`
- **Development**: `http://localhost:5000`

### Authentication
```http
Authorization: Bearer YOUR_API_TOKEN
Content-Type: application/json
```

## 📧 Email Endpoints

### Send Single Email
Send a single email notification through available email plugins.

```http
POST /email/send
```

**Purpose**: Send transactional or promotional emails via runtime-loaded email plugins (SendGrid, SMTP).

**Request Body**:
```json
{
  "to": "<EMAIL>",
  "subject": "Welcome to Our Service",
  "body": "Thank you for signing up!",
  "from": "<EMAIL>",
  "isHtml": false,
  "attachments": [
    {
      "fileName": "welcome.pdf",
      "content": "base64-encoded-content",
      "contentType": "application/pdf"
    }
  ]
}
```

**Query Parameters**:
- `provider` (optional): Preferred email provider (`SendGrid`, `SMTP`)

**Response**:
```json
{
  "isSuccess": true,
  "messageId": "msg_123456789",
  "provider": "SendGrid",
  "timestamp": "2025-06-27T10:30:00Z"
}
```

### Send Bulk Email
Send multiple emails in a single request.

```http
POST /email/send-bulk
```

**Purpose**: Efficiently send multiple emails (newsletters, notifications) with batch processing.

**Request Body**:
```json
{
  "emails": [
    {
      "to": "<EMAIL>",
      "subject": "Newsletter #1",
      "body": "Content for user 1"
    },
    {
      "to": "<EMAIL>", 
      "subject": "Newsletter #2",
      "body": "Content for user 2"
    }
  ],
  "from": "<EMAIL>"
}
```

### Get Email Status
Check the delivery status of a sent email.

```http
GET /email/status/{messageId}
```

**Purpose**: Track email delivery, opens, clicks, and bounces.

**Response**:
```json
{
  "messageId": "msg_123456789",
  "status": "delivered",
  "deliveredAt": "2025-06-27T10:31:15Z",
  "openedAt": "2025-06-27T10:45:30Z",
  "provider": "SendGrid"
}
```

## 📱 SMS Endpoints

### Send Single SMS
Send a single SMS notification through available SMS plugins.

```http
POST /sms/send
```

**Purpose**: Send verification codes, alerts, and notifications via SMS through multiple providers (Twilio, BulkSMS, Clickatel).

**Request Body**:
```json
{
  "phoneNumber": "+**********",
  "message": "Your verification code is: 123456"
}
```

**Query Parameters**:
- `provider` (optional): Preferred SMS provider (`Twilio`, `BulkSMS`, `Clickatel`)

**Response**:
```json
{
  "isSuccess": true,
  "messageId": "sms_987654321",
  "provider": "Twilio",
  "timestamp": "2025-06-27T10:30:00Z"
}
```

### Send Bulk SMS
Send multiple SMS messages in a single request.

```http
POST /sms/send-bulk
```

**Purpose**: Send mass SMS campaigns, alerts to multiple recipients.

**Request Body**:
```json
{
  "messages": [
    {
      "phoneNumber": "+**********",
      "message": "Alert: System maintenance at 2 PM"
    },
    {
      "phoneNumber": "+**********",
      "message": "Alert: System maintenance at 2 PM"
    }
  ]
}
```

### Get SMS Status
Check the delivery status of a sent SMS.

```http
GET /sms/status/{messageId}
```

**Purpose**: Track SMS delivery status and delivery confirmations.

**Response**:
```json
{
  "messageId": "sms_987654321",
  "status": "delivered",
  "deliveredAt": "2025-06-27T10:31:00Z",
  "provider": "Twilio",
  "cost": 0.0075
}
```

### Get SMS History
Retrieve SMS history for a specific phone number.

```http
GET /sms/history/{phoneNumber}
```

**Purpose**: View all SMS messages sent to a specific phone number for audit and support.

## 🔔 Push Notification Endpoints

### Send Push Notification
Send a push notification through available push plugins.

```http
POST /push/send
```

**Purpose**: Send real-time notifications to mobile and web applications via Firebase FCM, APNS.

**Request Body**:
```json
{
  "deviceToken": "device_token_123",
  "title": "New Message",
  "message": "You have a new message from John",
  "data": {
    "messageId": "msg_456",
    "senderId": "user_789"
  },
  "imageUrl": "https://example.com/notification-image.png"
}
```

**Query Parameters**:
- `provider` (optional): Preferred push provider (`Firebase`, `APNS`)

**Response**:
```json
{
  "isSuccess": true,
  "messageId": "push_111222333",
  "provider": "Firebase",
  "timestamp": "2025-06-27T10:30:00Z"
}
```

### Send Bulk Push Notifications
Send push notifications to multiple devices.

```http
POST /push/send-bulk
```

**Purpose**: Send notifications to multiple users (announcements, updates).

### Send Topic-Based Push
Send push notifications to topic subscribers.

```http
POST /push/send-topic
```

**Purpose**: Send notifications to all users subscribed to a specific topic.

**Request Body**:
```json
{
  "topic": "breaking-news",
  "title": "Breaking News",
  "message": "Important update available",
  "data": {
    "category": "news",
    "priority": "high"
  }
}
```

## 🔌 Plugin Management Endpoints

### List All Plugins
Get all loaded plugins and their current status.

```http
GET /admin/plugins
```

**Purpose**: View all available notification plugins, their status, and capabilities.

**Response**:
```json
[
  {
    "name": "Twilio SMS Plugin",
    "version": "1.0.0",
    "type": "SMS",
    "provider": "Twilio",
    "isEnabled": true,
    "isHealthy": true,
    "supportedFeatures": ["SendSMS", "BulkSMS", "DeliveryStatus"]
  },
  {
    "name": "SendGrid Email Plugin",
    "version": "1.0.0", 
    "type": "Email",
    "provider": "SendGrid",
    "isEnabled": true,
    "isHealthy": true,
    "supportedFeatures": ["SendEmail", "BulkEmail", "Templates"]
  }
]
```

### Load Plugin
Load a plugin at runtime without service restart.

```http
POST /admin/plugins/load
```

**Purpose**: Dynamically load new notification providers without downtime.

**Request Body**:
```json
{
  "pluginPath": "/plugins/Plugin.Sms.NewProvider.dll"
}
```

### Unload Plugin
Unload a plugin at runtime.

```http
POST /admin/plugins/{pluginName}/unload
```

**Purpose**: Remove a plugin from memory to free resources or for maintenance.

### Enable/Disable Plugin
Enable or disable a plugin without unloading it.

```http
POST /admin/plugins/{pluginName}/enable
POST /admin/plugins/{pluginName}/disable
```

**Purpose**: Temporarily enable/disable providers for maintenance or testing.

### Upload Plugin
Upload a new plugin package to the server.

```http
POST /admin/plugins/upload
Content-Type: multipart/form-data
```

**Purpose**: Deploy new notification providers by uploading plugin DLL files.

## 💚 Health & Monitoring Endpoints

### Service Health Check
Get overall service health including plugin status.

```http
GET /health
```

**Purpose**: Monitor service availability and plugin health for load balancers and monitoring systems.

**Response**:
```json
{
  "status": "Healthy",
  "timestamp": "2025-06-27T10:30:00Z",
  "plugins": {
    "Twilio SMS Plugin": true,
    "SendGrid Email Plugin": true,
    "Firebase Push Plugin": false
  },
  "services": {
    "database": "Connected",
    "redis": "Connected",
    "queue": "Processing"
  }
}
```

### Detailed Health Check
Get detailed health information for all components.

```http
GET /health/detailed
```

**Purpose**: Comprehensive health check for debugging and detailed monitoring.

### Plugin Health Check
Check health of a specific plugin.

```http
GET /health/plugins/{pluginName}
```

**Purpose**: Monitor individual plugin health and connectivity to external services.

## 🛡️ Admin Endpoints

### Get System Configuration
Retrieve current system configuration.

```http
GET /admin/config
```

**Purpose**: View current system settings, plugin configurations, and feature flags.

### Update System Configuration
Update system-wide configuration settings.

```http
PUT /admin/config
```

**Purpose**: Modify system settings, enable/disable features, update rate limits.

### Get Plugin Configuration
Retrieve configuration for a specific plugin.

```http
GET /admin/plugins/{pluginName}/config
```

**Purpose**: View plugin-specific settings (API keys, endpoints, timeouts).

### Update Plugin Configuration
Update configuration for a specific plugin.

```http
PUT /admin/plugins/{pluginName}/config
```

**Purpose**: Modify plugin settings without reloading (API keys, rate limits, etc.).

**Request Body**:
```json
{
  "apiKey": "new_api_key_value",
  "timeout": 30,
  "retryAttempts": 3,
  "rateLimit": 1000
}
```

## 📊 Metrics Endpoints

### Email Metrics
Get email delivery metrics and analytics.

```http
GET /metrics/email
```

**Purpose**: Monitor email performance, delivery rates, and provider statistics.

**Query Parameters**:
- `startDate`: Start date for metrics (ISO 8601)
- `endDate`: End date for metrics (ISO 8601)
- `provider`: Filter by specific provider

**Response**:
```json
{
  "totalSent": 15420,
  "delivered": 15180,
  "bounced": 120,
  "opened": 8950,
  "clicked": 2340,
  "deliveryRate": 98.4,
  "openRate": 58.9,
  "clickRate": 15.4,
  "providerBreakdown": {
    "SendGrid": {
      "sent": 12000,
      "delivered": 11800,
      "deliveryRate": 98.3
    },
    "SMTP": {
      "sent": 3420,
      "delivered": 3380,
      "deliveryRate": 98.8
    }
  }
}
```

### SMS Metrics
Get SMS delivery metrics and analytics.

```http
GET /metrics/sms
```

**Purpose**: Monitor SMS performance, delivery rates, costs, and provider comparison.

### Push Notification Metrics
Get push notification metrics and analytics.

```http
GET /metrics/push
```

**Purpose**: Monitor push notification delivery, open rates, and device platform statistics.

### Overall System Metrics
Get comprehensive system metrics across all channels.

```http
GET /metrics/system
```

**Purpose**: High-level overview of notification system performance and usage.

## 🔍 Usage Examples

### Complete Email Workflow
```bash
# 1. Send email
curl -X POST "https://api.notificationservice.com/email/send" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Welcome!",
    "body": "Welcome to our service!",
    "from": "<EMAIL>"
  }'

# 2. Check status
curl -X GET "https://api.notificationservice.com/email/status/msg_123456789" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. View metrics
curl -X GET "https://api.notificationservice.com/metrics/email?startDate=2025-06-01&endDate=2025-06-27" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Plugin Management Workflow
```bash
# 1. List current plugins
curl -X GET "https://api.notificationservice.com/admin/plugins" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. Load new plugin
curl -X POST "https://api.notificationservice.com/admin/plugins/load" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"pluginPath": "/plugins/Plugin.Sms.NewProvider.dll"}'

# 3. Check plugin health
curl -X GET "https://api.notificationservice.com/health/plugins/NewProvider" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🚨 Error Handling

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "The phone number format is invalid",
    "details": {
      "field": "phoneNumber",
      "expectedFormat": "E.164 format (+**********)"
    }
  },
  "timestamp": "2025-06-27T10:30:00Z",
  "requestId": "req_123456789"
}
```

### Common Error Codes
- `INVALID_REQUEST` - Malformed request data
- `PLUGIN_NOT_FOUND` - Requested plugin not available
- `PLUGIN_UNHEALTHY` - Plugin is loaded but not responding
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `PROVIDER_ERROR` - External provider API error
- `CONFIGURATION_ERROR` - Plugin configuration issue

## 📞 Support

For API questions and support:
- **Documentation**: https://docs.notificationservice.com
- **GitHub Issues**: https://github.com/bloodchild8906/NotificationService-master/issues
- **Email**: <EMAIL>
