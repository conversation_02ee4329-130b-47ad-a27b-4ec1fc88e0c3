using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PluginContract.Interfaces;
using PluginContract.Models;

namespace PluginCore.Base;

public abstract class BaseNotificationPlugin : INotificationPlugin, IDisposable
{
    protected ILogger? Logger { get; private set; }
    protected IConfiguration? Configuration { get; private set; }

    public abstract PluginInfo PluginInfo { get; }

    public virtual void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // Default implementation - plugins can override
    }

    public virtual Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        // Default implementation - plugins should override for specific validation
        return Task.FromResult(true);
    }

    public virtual Task InitializeAsync(IConfiguration configuration)
    {
        Configuration = configuration;
        
        // Create logger if available
        var serviceProvider = new ServiceCollection()
            .AddLogging()
            .BuildServiceProvider();
        
        Logger = serviceProvider.GetService<ILoggerFactory>()?.CreateLogger(GetType());
        
        return Task.CompletedTask;
    }

    public abstract Task<NotificationResponse> SendAsync(NotificationRequest request, CancellationToken cancellationToken = default);

    public virtual Task<bool> HealthCheckAsync()
    {
        // Default implementation - plugins can override for specific health checks
        return Task.FromResult(true);
    }

    protected virtual string GetConfigurationSection()
    {
        return $"Plugins:{PluginInfo.Name}";
    }

    protected virtual T? GetConfiguration<T>() where T : class
    {
        if (Configuration == null) return null;

        var section = Configuration.GetSection(GetConfigurationSection());
        return section.Get<T>();
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        // Base implementation - plugins can override for cleanup
    }
}
