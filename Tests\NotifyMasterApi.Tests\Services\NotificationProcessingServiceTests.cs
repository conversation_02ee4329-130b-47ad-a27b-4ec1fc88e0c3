using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Moq;
using NotifyMasterApi.Services;
using NotifyMasterApi.Interfaces;
using NotifyMasterApi.Data.Entities;
using NotificationContract.Models;
using Xunit;
using FluentAssertions;
using AutoFixture;
using AutoFixture.Xunit2;

namespace NotifyMasterApi.Tests.Services;

/// <summary>
/// Comprehensive unit tests for NotificationProcessingService with enhanced error handling and monitoring.
/// </summary>
public class NotificationProcessingServiceTests : IDisposable
{
    private readonly Mock<ILogger<NotificationProcessingService>> _mockLogger;
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<IServiceScope> _mockServiceScope;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<INotificationQueueService> _mockQueueService;
    private readonly Mock<INotificationLoggingService> _mockLoggingService;
    private readonly Mock<EmailService.Library.Interfaces.IEmailService> _mockEmailService;
    private readonly Mock<SmsService.Library.Interfaces.ISmsService> _mockSmsService;
    private readonly Mock<PushNotificationService.Library.Interfaces.IPushNotificationService> _mockPushService;
    private readonly IFixture _fixture;
    private readonly NotificationProcessingService _service;

    public NotificationProcessingServiceTests()
    {
        _mockLogger = new Mock<ILogger<NotificationProcessingService>>();
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockServiceScope = new Mock<IServiceScope>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockQueueService = new Mock<INotificationQueueService>();
        _mockLoggingService = new Mock<INotificationLoggingService>();
        _mockEmailService = new Mock<EmailService.Library.Interfaces.IEmailService>();
        _mockSmsService = new Mock<SmsService.Library.Interfaces.ISmsService>();
        _mockPushService = new Mock<PushNotificationService.Library.Interfaces.IPushNotificationService>();
        _fixture = new Fixture();

        // Setup configuration defaults
        _mockConfiguration.Setup(c => c["NotificationProcessing:MaxConcurrentProcessing"])
            .Returns("5");
        _mockConfiguration.Setup(c => c["NotificationProcessing:ProcessingIntervalMs"])
            .Returns("1000");
        _mockConfiguration.Setup(c => c["NotificationProcessing:MaxRetryAttempts"])
            .Returns("3");

        // Setup service provider and scope
        _mockServiceProvider.Setup(sp => sp.CreateScope())
            .Returns(_mockServiceScope.Object);
        _mockServiceScope.Setup(s => s.ServiceProvider)
            .Returns(_mockServiceProvider.Object);

        // Setup service resolution
        _mockServiceProvider.Setup(sp => sp.GetRequiredService<INotificationQueueService>())
            .Returns(_mockQueueService.Object);
        _mockServiceProvider.Setup(sp => sp.GetRequiredService<INotificationLoggingService>())
            .Returns(_mockLoggingService.Object);
        _mockServiceProvider.Setup(sp => sp.GetRequiredService<EmailService.Library.Interfaces.IEmailService>())
            .Returns(_mockEmailService.Object);
        _mockServiceProvider.Setup(sp => sp.GetRequiredService<SmsService.Library.Interfaces.ISmsService>())
            .Returns(_mockSmsService.Object);
        _mockServiceProvider.Setup(sp => sp.GetRequiredService<PushNotificationService.Library.Interfaces.IPushNotificationService>())
            .Returns(_mockPushService.Object);

        _service = new NotificationProcessingService(
            _mockLogger.Object,
            _mockServiceProvider.Object,
            _mockConfiguration.Object);
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeSuccessfully()
    {
        // Arrange & Act
        var service = new NotificationProcessingService(
            _mockLogger.Object,
            _mockServiceProvider.Object,
            _mockConfiguration.Object);

        // Assert
        service.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        Assert.Throws<ArgumentNullException>(() => new NotificationProcessingService(
            null!,
            _mockServiceProvider.Object,
            _mockConfiguration.Object));
    }

    [Fact]
    public void Constructor_WithNullServiceProvider_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        Assert.Throws<ArgumentNullException>(() => new NotificationProcessingService(
            _mockLogger.Object,
            null!,
            _mockConfiguration.Object));
    }

    [Fact]
    public void Constructor_WithNullConfiguration_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        Assert.Throws<ArgumentNullException>(() => new NotificationProcessingService(
            _mockLogger.Object,
            _mockServiceProvider.Object,
            null!));
    }

    [Theory]
    [AutoData]
    public async Task ProcessNotification_EmailNotification_ShouldProcessSuccessfully(NotificationQueueItem notification)
    {
        // Arrange
        notification.Type = NotificationType.Email;
        _mockQueueService.Setup(q => q.DequeueNotificationAsync())
            .ReturnsAsync(notification);
        _mockQueueService.Setup(q => q.MarkNotificationProcessedAsync(notification.QueueId))
            .Returns(Task.CompletedTask);

        // Act
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
        var executeTask = _service.StartAsync(cts.Token);
        
        // Allow some processing time
        await Task.Delay(100, cts.Token);
        cts.Cancel();

        try
        {
            await executeTask;
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }

        // Assert
        _mockQueueService.Verify(q => q.DequeueNotificationAsync(), Times.AtLeastOnce);
        _mockLoggingService.Verify(l => l.UpdateNotificationStatusAsync(
            notification.MessageId,
            NotificationStatus.Processing,
            It.IsAny<string>()), Times.AtLeastOnce);
    }

    [Theory]
    [AutoData]
    public async Task ProcessNotification_SmsNotification_ShouldProcessSuccessfully(NotificationQueueItem notification)
    {
        // Arrange
        notification.Type = NotificationType.Sms;
        _mockQueueService.Setup(q => q.DequeueNotificationAsync())
            .ReturnsAsync(notification);
        _mockQueueService.Setup(q => q.MarkNotificationProcessedAsync(notification.QueueId))
            .Returns(Task.CompletedTask);

        // Act
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
        var executeTask = _service.StartAsync(cts.Token);
        
        // Allow some processing time
        await Task.Delay(100, cts.Token);
        cts.Cancel();

        try
        {
            await executeTask;
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }

        // Assert
        _mockQueueService.Verify(q => q.DequeueNotificationAsync(), Times.AtLeastOnce);
        _mockLoggingService.Verify(l => l.UpdateNotificationStatusAsync(
            notification.MessageId,
            NotificationStatus.Processing,
            It.IsAny<string>()), Times.AtLeastOnce);
    }

    [Theory]
    [AutoData]
    public async Task ProcessNotification_PushNotification_ShouldProcessSuccessfully(NotificationQueueItem notification)
    {
        // Arrange
        notification.Type = NotificationType.PushMessage;
        _mockQueueService.Setup(q => q.DequeueNotificationAsync())
            .ReturnsAsync(notification);
        _mockQueueService.Setup(q => q.MarkNotificationProcessedAsync(notification.QueueId))
            .Returns(Task.CompletedTask);

        // Act
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
        var executeTask = _service.StartAsync(cts.Token);
        
        // Allow some processing time
        await Task.Delay(100, cts.Token);
        cts.Cancel();

        try
        {
            await executeTask;
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }

        // Assert
        _mockQueueService.Verify(q => q.DequeueNotificationAsync(), Times.AtLeastOnce);
        _mockLoggingService.Verify(l => l.UpdateNotificationStatusAsync(
            notification.MessageId,
            NotificationStatus.Processing,
            It.IsAny<string>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task ProcessNotification_NoNotificationsInQueue_ShouldWaitAndRetry()
    {
        // Arrange
        _mockQueueService.Setup(q => q.DequeueNotificationAsync())
            .ReturnsAsync((NotificationQueueItem?)null);

        // Act
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(2));
        var executeTask = _service.StartAsync(cts.Token);
        
        // Allow some processing time
        await Task.Delay(1500, cts.Token);
        cts.Cancel();

        try
        {
            await executeTask;
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }

        // Assert
        _mockQueueService.Verify(q => q.DequeueNotificationAsync(), Times.AtLeast(2));
    }

    [Theory]
    [AutoData]
    public async Task ProcessNotification_ServiceThrowsException_ShouldHandleGracefully(NotificationQueueItem notification)
    {
        // Arrange
        notification.Type = NotificationType.Email;
        _mockQueueService.Setup(q => q.DequeueNotificationAsync())
            .ReturnsAsync(notification);
        _mockEmailService.Setup(e => e.SendEmailAsync(It.IsAny<object>()))
            .ThrowsAsync(new InvalidOperationException("Test exception"));

        // Act
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
        var executeTask = _service.StartAsync(cts.Token);
        
        // Allow some processing time
        await Task.Delay(100, cts.Token);
        cts.Cancel();

        try
        {
            await executeTask;
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }

        // Assert
        _mockQueueService.Verify(q => q.DequeueNotificationAsync(), Times.AtLeastOnce);
        // Should handle the exception gracefully and continue processing
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
