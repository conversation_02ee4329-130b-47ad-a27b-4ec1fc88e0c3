using NotificationContract.Models;
using SmsService.Library.Interfaces;

namespace NotifyMasterApi.Gateways;

public class SmsGateway : ISmsGateway
{
    private readonly ISmsService _smsService;

    public SmsGateway(ISmsService smsService)
    {
        _smsService = smsService;
    }

    public async Task<SmsResponse> SendAsync(SmsMessageRequest request)
    {
        return await _smsService.SendAsync(request);
    }

    public async Task<BulkSmsResponse> SendBulkAsync(BulkSmsRequest request)
    {
        return await _smsService.SendBulkAsync(request);
    }

    public async Task<SmsResponse> ResendMessageAsync(string messageId)
    {
        return await _smsService.ResendMessageAsync(messageId);
    }

    public async Task<object> GetProvidersAsync()
    {
        return await _smsService.GetProvidersAsync();
    }

    public async Task<ServiceResult> ConfigureProviderAsync(string provider, object configuration)
    {
        return await _smsService.ConfigureProviderAsync(provider, configuration);
    }

    public async Task<object> TestProviderAsync(string provider, string? testPhoneNumber = null)
    {
        return await _smsService.TestProviderAsync(provider, testPhoneNumber);
    }

    public async Task<ServiceResult> UpdateProviderStatusAsync(string provider, bool enabled)
    {
        return await _smsService.UpdateProviderStatusAsync(provider, enabled);
    }

    public async Task<object> GetDeliveryMetricsAsync(string? provider = null)
    {
        return await _smsService.GetDeliveryMetricsAsync(provider);
    }

    public async Task<object> GetProviderMetricsAsync(string provider)
    {
        return await _smsService.GetProviderMetricsAsync(provider);
    }

    // Additional methods expected by controllers
    public async Task<SmsResponse> SendSmsAsync(SmsMessageRequest request)
    {
        return await SendAsync(request);
    }

    public async Task<BulkSmsResponse> SendBulkSmsAsync(BulkSmsRequest request)
    {
        return await SendBulkAsync(request);
    }

    public async Task<object> GetMessageStatusAsync(string messageId)
    {
        // Mock implementation - would need to be implemented in service
        return await Task.FromResult(new { MessageId = messageId, Status = "Delivered", Timestamp = DateTime.UtcNow });
    }

    public async Task<object> GetMessageHistoryAsync(string? userId = null, int page = 1, int pageSize = 50)
    {
        // Mock implementation - would need to be implemented in service
        return await Task.FromResult(new { Messages = new List<object>(), Page = page, PageSize = pageSize, Total = 0 });
    }

    public async Task<object> GetAvailableProviders()
    {
        return await GetProvidersAsync();
    }

    public async Task<ServiceResult> SwitchProvider(string provider)
    {
        // Mock implementation - would need to be implemented in service
        return await Task.FromResult(new ServiceResult { Success = true, Data = $"Switched to provider: {provider}" });
    }

    public async Task<object> SendTestMessageAsync(string provider, string? testPhoneNumber = null)
    {
        return await TestProviderAsync(provider, testPhoneNumber);
    }

    public async Task<ServiceResult> ReloadProviders()
    {
        // Mock implementation - would need to be implemented in service
        return await Task.FromResult(new ServiceResult { Success = true, Data = "Providers reloaded" });
    }

    public async Task<ServiceResult> UpdateProviderConfiguration(string provider, object configuration)
    {
        return await ConfigureProviderAsync(provider, configuration);
    }

    public async Task<object> GetSummaryMetricsAsync()
    {
        return await GetDeliveryMetricsAsync();
    }

    public async Task<object> GetDetailedMetricsAsync(DateTime? from = null, DateTime? to = null)
    {
        // Mock implementation - would need to be implemented in service
        return await Task.FromResult(new { From = from, To = to, TotalSent = 0, TotalDelivered = 0, TotalFailed = 0 });
    }

    public async Task<object> GetErrorMetricsAsync()
    {
        // Mock implementation - would need to be implemented in service
        return await Task.FromResult(new { TotalErrors = 0, ErrorRate = 0.0, CommonErrors = new List<object>() });
    }

    public async Task<object> GetMonthlyStatisticsAsync(int year, int month)
    {
        // Mock implementation - would need to be implemented in service
        return await Task.FromResult(new { Year = year, Month = month, TotalSent = 0, TotalDelivered = 0, TotalFailed = 0 });
    }
}
