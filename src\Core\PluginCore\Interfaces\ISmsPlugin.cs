using SmsContract.Models;
using PluginContract.Models;
using PluginContract.Interfaces;

namespace PluginCore.Interfaces;

/// <summary>
///  Interface for SMS plugins
/// </summary>
public interface ISmsPlugin : INotificationPlugin
{
    /// <summary>
    ///  Sends an SMS
    /// </summary>
    /// <param name="request"> SMS request </param>
    /// <returns> SMS response </returns>
    Task<NotificationResponse> SendAsync(SendSmsRequest request);
    /// <summary>
    ///  Sends multiple SMS messages
    /// </summary>
    /// <param name="requests"> SMS requests </param>
    /// <returns> SMS response </returns>
    Task<NotificationResponse> SendBulkAsync(IEnumerable<SendSmsRequest> requests);
    /// <summary>
    ///  Gets the status of an SMS message
    /// </summary>
    /// <param name="messageId"> Message ID </param>
    /// <returns> SMS response </returns>
    Task<NotificationResponse> GetMessageStatusAsync(string messageId);
    /// <summary>
    ///  Gets the history of SMS messages for a given phone number
    /// </summary>
    /// <param name="phoneNumber"> Phone number </param>
    /// <returns> SMS response </returns>
    Task<NotificationResponse> GetMessageHistoryAsync(string phoneNumber);
    /// <summary>
    ///  Resends an SMS message
    /// </summary>
    /// <param name="messageId"> Message ID </param>
    /// <returns> SMS response </returns>
    Task<NotificationResponse> ResendMessageAsync(string messageId);
    /// <summary>
    ///  Validates the configuration for the plugin
    /// </summary>
    /// <returns> True if the configuration is valid, false otherwise </returns>
    Task<bool> ValidateConfigurationAsync();
}
