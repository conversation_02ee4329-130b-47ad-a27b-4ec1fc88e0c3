using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.Hosting;
using NotifyMasterApi.Services;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace NotifyMasterApi.Tests;

/// <summary>
/// Comprehensive tests for fallback mechanisms
/// </summary>
public class FallbackSystemTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly ITestOutputHelper _output;

    public FallbackSystemTests(WebApplicationFactory<Program> factory, ITestOutputHelper output)
    {
        _factory = factory;
        _output = output;
    }

    [Fact]
    public async Task RedisConnectionService_ShouldFallbackToInMemory_WhenRedisUnavailable()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddXUnit(_output));
        
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["ConnectionStrings:Redis"] = "localhost:9999" // Invalid Redis port
            })
            .Build();

        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<RedisConnectionService>>();

        // Act
        var redisService = new RedisConnectionService(logger, configuration);

        // Assert
        Assert.True(redisService.IsUsingFallback);
        Assert.False(redisService.IsConnected);

        // Test fallback operations
        var testKey = "test_key";
        var testValue = "test_value";

        var setResult = await redisService.SetStringAsync(testKey, testValue);
        Assert.True(setResult);

        var getValue = await redisService.GetStringAsync(testKey);
        Assert.Equal(testValue, getValue);

        var deleteResult = await redisService.DeleteKeyAsync(testKey);
        Assert.True(deleteResult);

        var getAfterDelete = await redisService.GetStringAsync(testKey);
        Assert.Null(getAfterDelete);
    }

    [Fact]
    public async Task RedisConnectionService_ShouldHandleListOperations_InFallbackMode()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddXUnit(_output));
        
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["ConnectionStrings:Redis"] = "localhost:9999"
            })
            .Build();

        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<RedisConnectionService>>();
        var redisService = new RedisConnectionService(logger, configuration);

        // Act & Assert
        var listKey = "test_list";
        
        // Test list push operations
        var pushResult1 = await redisService.ListPushAsync(listKey, "item1");
        Assert.Equal(1, pushResult1);

        var pushResult2 = await redisService.ListPushAsync(listKey, "item2");
        Assert.Equal(2, pushResult2);

        // Test list pop operations
        var popResult1 = await redisService.ListPopAsync(listKey);
        Assert.Equal("item2", popResult1);

        var popResult2 = await redisService.ListPopAsync(listKey);
        Assert.Equal("item1", popResult2);

        var popResult3 = await redisService.ListPopAsync(listKey);
        Assert.Null(popResult3);
    }

    [Fact]
    public async Task RedisConnectionService_ShouldHandlePublishOperations_InFallbackMode()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddXUnit(_output));
        
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["ConnectionStrings:Redis"] = "localhost:9999"
            })
            .Build();

        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<RedisConnectionService>>();
        var redisService = new RedisConnectionService(logger, configuration);

        // Act & Assert
        var channel = "test_channel";
        var message = "test_message";

        var publishResult = await redisService.PublishAsync(channel, message);
        Assert.True(publishResult); // Should succeed in fallback mode
    }

    [Fact]
    public void InMemoryDatabaseService_ShouldStoreAndRetrieveData()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddXUnit(_output));
        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<InMemoryDatabaseService>>();

        var databaseService = new InMemoryDatabaseService(logger);

        // Act
        databaseService.EnableInMemoryFallback();

        // Test notification logging
        databaseService.StoreNotificationLog("notif1", "email", "<EMAIL>", "sent");
        databaseService.StoreNotificationLog("notif2", "sms", "+**********", "failed", "Network error");

        // Test plugin logging
        databaseService.StorePluginLog("plugin1", "load", "success", "Plugin loaded successfully");
        databaseService.StorePluginLog("plugin2", "unload", "failed", "Plugin not found");

        // Test data storage
        databaseService.StoreData("custom_key", new { Id = 1, Name = "Test" });

        // Assert
        Assert.True(databaseService.IsUsingInMemoryFallback);

        var notificationLogs = databaseService.GetNotificationLogs().ToList();
        Assert.Equal(2, notificationLogs.Count);

        var pluginLogs = databaseService.GetPluginLogs().ToList();
        Assert.Equal(2, pluginLogs.Count);

        var systemLogs = databaseService.GetSystemLogs().ToList();
        Assert.NotEmpty(systemLogs);

        var status = databaseService.GetDatabaseStatus();
        Assert.True((bool)status["IsUsingInMemoryFallback"]);
        Assert.Equal(2, status["NotificationLogs"]);
        Assert.Equal(2, status["PluginLogs"]);

        // Test data retrieval
        var retrievedData = databaseService.TryGetData<object>("custom_key", out var data);
        Assert.True(retrievedData);
        Assert.NotNull(data);
    }

    [Fact]
    public void InMemoryDatabaseService_ShouldClearData()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddXUnit(_output));
        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<InMemoryDatabaseService>>();

        var databaseService = new InMemoryDatabaseService(logger);
        databaseService.EnableInMemoryFallback();

        // Add some data
        databaseService.StoreNotificationLog("notif1", "email", "<EMAIL>", "sent");
        databaseService.StoreData("test_key", "test_value");

        // Act
        databaseService.ClearData();

        // Assert
        var status = databaseService.GetDatabaseStatus();
        Assert.Equal(0, status["TotalStoredItems"]);
        Assert.Equal(0, status["NotificationLogs"]);

        var retrievedData = databaseService.TryGetData<string>("test_key", out var data);
        Assert.False(retrievedData);
        Assert.Null(data);
    }

    [Fact]
    public async Task SystemStatusController_ShouldReturnFallbackStatus()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999", // Force Redis fallback
                    ["ConnectionStrings:DefaultConnection"] = "" // Force DB fallback
                });
            });
        }).CreateClient();

        // Act
        var response = await client.GetAsync("/api/systemstatus");
        var content = await response.Content.ReadAsStringAsync();

        // Assert
        response.EnsureSuccessStatusCode();
        Assert.Contains("fallback", content.ToLower());

        var jsonDoc = JsonDocument.Parse(content);
        var fallbacks = jsonDoc.RootElement.GetProperty("Fallbacks");
        
        // Should have at least one fallback active
        var anyFallbackActive = fallbacks.GetProperty("AnyFallbackActive").GetBoolean();
        Assert.True(anyFallbackActive);
    }

    [Fact]
    public async Task SystemStatusController_ShouldReturnRedisStatus()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999"
                });
            });
        }).CreateClient();

        // Act
        var response = await client.GetAsync("/api/systemstatus/redis");
        var content = await response.Content.ReadAsStringAsync();

        // Assert
        response.EnsureSuccessStatusCode();
        
        var jsonDoc = JsonDocument.Parse(content);
        var isUsingFallback = jsonDoc.RootElement.GetProperty("IsUsingFallback").GetBoolean();
        Assert.True(isUsingFallback);
    }

    [Fact]
    public async Task SystemStatusController_ShouldTestRedisConnection()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999"
                });
            });
        }).CreateClient();

        // Act
        var response = await client.PostAsync("/api/systemstatus/redis/test", null);
        var content = await response.Content.ReadAsStringAsync();

        // Assert
        response.EnsureSuccessStatusCode();
        
        var jsonDoc = JsonDocument.Parse(content);
        var testResult = jsonDoc.RootElement.GetProperty("TestResult").GetString();
        var usingFallback = jsonDoc.RootElement.GetProperty("UsingFallback").GetBoolean();
        
        Assert.Equal("Success", testResult);
        Assert.True(usingFallback);
    }

    [Fact]
    public async Task SystemStatusController_ShouldClearFallbackData()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999",
                    ["ConnectionStrings:DefaultConnection"] = ""
                });
            });
        }).CreateClient();

        // Act
        var response = await client.PostAsync("/api/systemstatus/fallbacks/clear", null);
        var content = await response.Content.ReadAsStringAsync();

        // Assert
        response.EnsureSuccessStatusCode();
        
        var jsonDoc = JsonDocument.Parse(content);
        var message = jsonDoc.RootElement.GetProperty("Message").GetString();
        Assert.Contains("cleared successfully", message);
    }

    [Fact]
    public void RedisConnectionService_ShouldProvideStatusInformation()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddXUnit(_output));
        
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["ConnectionStrings:Redis"] = "localhost:9999"
            })
            .Build();

        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<RedisConnectionService>>();
        var redisService = new RedisConnectionService(logger, configuration);

        // Act
        var status = redisService.GetStatus();

        // Assert
        Assert.False((bool)status["IsConnected"]);
        Assert.True((bool)status["IsUsingFallback"]);
        Assert.Equal("Fallback (In-Memory)", status["ConnectionStatus"]);
        Assert.Equal(0, status["FallbackItemCount"]);
        Assert.IsType<string[]>(status["RedisEndpoints"]);
    }
}
