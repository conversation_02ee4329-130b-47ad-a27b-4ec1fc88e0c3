using Microsoft.AspNetCore.Mvc;
using NotifyMasterApi.Interfaces;

namespace NotifyMasterApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<HealthController> _logger;

    public HealthController(IPluginManager pluginManager, ILogger<HealthController> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    [HttpGet]
    public IActionResult GetHealth()
    {
        try
        {
            var health = new
            {
                status = "healthy",
                timestamp = DateTime.UtcNow,
                version = "1.0.0",
                services = new
                {
                    email = "available",
                    sms = "available",
                    push = "available"
                }
            };

            return Ok(health);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
            return StatusCode(500, new { status = "unhealthy", error = ex.Message });
        }
    }

    [HttpGet("plugins")]
    public async Task<IActionResult> GetPluginHealth()
    {
        try
        {
            var pluginHealth = await _pluginManager.GetAllPluginsHealthStatusAsync();
            return Ok(pluginHealth);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Plugin health check failed");
            return StatusCode(500, new { status = "unhealthy", error = ex.Message });
        }
    }

    [HttpGet("plugins/{name}")]
    public async Task<IActionResult> GetPluginHealth(string name)
    {
        try
        {
            var pluginHealth = await _pluginManager.GetPluginHealthStatusAsync(name);
            return Ok(pluginHealth);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Plugin health check failed for {PluginName}", name);
            return StatusCode(500, new { status = "unhealthy", error = ex.Message });
        }
    }
}
