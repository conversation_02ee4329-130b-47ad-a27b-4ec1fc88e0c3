using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using NotifyMasterApi.Services;
using System.Reflection;
using Xunit;

namespace NotifyMasterApi.Tests.Services;

public class RuntimePluginInstanceTests : IDisposable
{
    private readonly Mock<ILogger> _loggerMock;
    private readonly Mock<IConfiguration> _configurationMock;
    private readonly PluginLoadContext _loadContext;
    private readonly TestPlugin _testPlugin;
    private readonly Type _testPluginType;
    private readonly PluginManifest _testManifest;
    private readonly Assembly _testAssembly;

    public RuntimePluginInstanceTests()
    {
        _loggerMock = new Mock<ILogger>();
        _configurationMock = new Mock<IConfiguration>();
        
        // Create test plugin instance
        _testPlugin = new TestPlugin(_loggerMock.Object);
        _testPluginType = typeof(TestPlugin);
        _testAssembly = Assembly.GetExecutingAssembly();
        
        // Create test manifest
        _testManifest = new PluginManifest
        {
            Name = "Test Plugin",
            Version = "1.0.0",
            Description = "Test plugin for unit testing",
            Type = "SMS",
            Provider = "Test",
            EntryPoint = "NotifyMasterApi.Tests.TestPlugin",
            IsEnabled = true,
            SupportedFeatures = new[] { "Send", "BulkSend", "HealthCheck" }
        };
        
        // Create load context (we'll use a mock since we're not actually loading from file)
        _loadContext = new PluginLoadContext(Assembly.GetExecutingAssembly().Location);
    }

    [Fact]
    public void Constructor_WithValidParameters_CreatesInstance()
    {
        // Act
        var instance = new RuntimePluginInstance(
            _loadContext,
            _testPlugin,
            _testPluginType,
            _testManifest,
            _testAssembly);

        // Assert
        instance.Name.Should().Be("Test Plugin");
        instance.Version.Should().Be("1.0.0");
        instance.Type.Should().Be("SMS");
        instance.Provider.Should().Be("Test");
        instance.IsEnabled.Should().BeTrue();
        instance.Assembly.Should().Be(_testAssembly);
        instance.Manifest.Should().Be(_testManifest);
    }

    [Fact]
    public async Task InitializeAsync_WithValidConfiguration_ReturnsTrue()
    {
        // Arrange
        var instance = CreateRuntimePluginInstance();

        // Act
        var result = await instance.InitializeAsync(_configurationMock.Object);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ValidateConfigurationAsync_WithValidConfiguration_ReturnsTrue()
    {
        // Arrange
        var instance = CreateRuntimePluginInstance();

        // Act
        var result = await instance.ValidateConfigurationAsync(_configurationMock.Object);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task SendNotificationAsync_WithValidRequest_ReturnsResult()
    {
        // Arrange
        var instance = CreateRuntimePluginInstance();
        var request = new { Message = "Test message", PhoneNumber = "+**********" };

        // Act
        var result = await instance.SendNotificationAsync(request);

        // Assert
        result.Should().NotBeNull();
        var resultDict = result as dynamic;
        resultDict.Should().NotBeNull();
    }

    [Fact]
    public async Task HealthCheckAsync_WhenPluginIsHealthy_ReturnsTrue()
    {
        // Arrange
        var instance = CreateRuntimePluginInstance();

        // Act
        var result = await instance.HealthCheckAsync();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void GetPluginInfo_ReturnsCorrectPluginInfo()
    {
        // Arrange
        var instance = CreateRuntimePluginInstance();

        // Act
        var pluginInfo = instance.GetPluginInfo();

        // Assert
        pluginInfo.Should().NotBeNull();
        pluginInfo.Name.Should().Be("Test Plugin");
        pluginInfo.Version.Should().Be("1.0.0");
        pluginInfo.Description.Should().Be("Test plugin for unit testing");
        pluginInfo.Type.Should().Be(PluginContract.Enums.PluginType.Sms);
        pluginInfo.Author.Should().Be("");
        pluginInfo.IsEnabled.Should().BeTrue();
    }

    [Fact]
    public async Task InvokeMethodAsync_WithExistingMethod_ReturnsResult()
    {
        // Arrange
        var instance = CreateRuntimePluginInstance();

        // Act
        var result = await instance.InvokeMethodAsync("ValidateConfigurationAsync", _configurationMock.Object);

        // Assert
        result.Should().NotBeNull();
        result.Should().Be(true);
    }

    [Fact]
    public async Task InvokeMethodAsync_WithNonExistingMethod_ReturnsNull()
    {
        // Arrange
        var instance = CreateRuntimePluginInstance();

        // Act
        var result = await instance.InvokeMethodAsync("NonExistentMethod");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void IsEnabled_CanBeSetAndRetrieved()
    {
        // Arrange
        var instance = CreateRuntimePluginInstance();

        // Act
        instance.IsEnabled = false;

        // Assert
        instance.IsEnabled.Should().BeFalse();
        
        // Act
        instance.IsEnabled = true;

        // Assert
        instance.IsEnabled.Should().BeTrue();
    }

    [Fact]
    public void Dispose_DisposesPluginAndLoadContext()
    {
        // Arrange
        var disposablePlugin = new Mock<IDisposable>();
        var instance = new RuntimePluginInstance(
            _loadContext,
            disposablePlugin.Object,
            typeof(IDisposable),
            _testManifest,
            _testAssembly);

        // Act
        instance.Dispose();

        // Assert
        disposablePlugin.Verify(x => x.Dispose(), Times.Once);
    }

    [Theory]
    [InlineData("sms", PluginContract.Enums.PluginType.Sms)]
    [InlineData("email", PluginContract.Enums.PluginType.Email)]
    [InlineData("push", PluginContract.Enums.PluginType.PushNotification)]
    [InlineData("unknown", PluginContract.Enums.PluginType.Sms)]
    public void GetPluginInfo_ConvertsTypeStringToEnum_Correctly(string typeString, PluginContract.Enums.PluginType expectedType)
    {
        // Arrange
        var manifest = new PluginManifest
        {
            Name = "Test Plugin",
            Version = "1.0.0",
            Type = typeString,
            Provider = "Test"
        };
        
        var instance = new RuntimePluginInstance(
            _loadContext,
            _testPlugin,
            _testPluginType,
            manifest,
            _testAssembly);

        // Act
        var pluginInfo = instance.GetPluginInfo();

        // Assert
        pluginInfo.Type.Should().Be(expectedType);
    }

    [Fact]
    public async Task SendNotificationAsync_WithCancellationToken_PassesTokenToMethod()
    {
        // Arrange
        var instance = CreateRuntimePluginInstance();
        var request = new { Message = "Test message" };
        var cancellationToken = new CancellationToken();

        // Act
        var result = await instance.SendNotificationAsync(request, cancellationToken);

        // Assert
        result.Should().NotBeNull();
    }

    [Fact]
    public async Task InitializeAsync_WhenMethodThrowsException_ReturnsFalse()
    {
        // Arrange
        var faultyPlugin = new FaultyTestPlugin();
        var instance = new RuntimePluginInstance(
            _loadContext,
            faultyPlugin,
            typeof(FaultyTestPlugin),
            _testManifest,
            _testAssembly);

        // Act
        var result = await instance.InitializeAsync(_configurationMock.Object);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task HealthCheckAsync_WhenMethodThrowsException_ReturnsFalse()
    {
        // Arrange
        var faultyPlugin = new FaultyTestPlugin();
        var instance = new RuntimePluginInstance(
            _loadContext,
            faultyPlugin,
            typeof(FaultyTestPlugin),
            _testManifest,
            _testAssembly);

        // Act
        var result = await instance.HealthCheckAsync();

        // Assert
        result.Should().BeFalse();
    }

    private RuntimePluginInstance CreateRuntimePluginInstance()
    {
        return new RuntimePluginInstance(
            _loadContext,
            _testPlugin,
            _testPluginType,
            _testManifest,
            _testAssembly);
    }

    public void Dispose()
    {
        _loadContext?.Unload();
    }
}

// Test plugin that throws exceptions for testing error handling
public class FaultyTestPlugin
{
    public async Task InitializeAsync(IConfiguration configuration)
    {
        await Task.Delay(1);
        throw new InvalidOperationException("Initialization failed");
    }

    public async Task<bool> HealthCheckAsync()
    {
        await Task.Delay(1);
        throw new InvalidOperationException("Health check failed");
    }
}

// Additional test plugin for testing different scenarios
public class TestPlugin
{
    private readonly ILogger _logger;

    public TestPlugin(ILogger logger)
    {
        _logger = logger;
    }

    public async Task InitializeAsync(IConfiguration configuration)
    {
        await Task.CompletedTask;
    }

    public async Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        return await Task.FromResult(true);
    }

    public async Task<object> SendAsync(object request)
    {
        return await Task.FromResult(new { IsSuccess = true, MessageId = "test_123" });
    }

    public async Task<object> SendSmsAsync(object request, CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new { IsSuccess = true, MessageId = "sms_123" });
    }

    public async Task<bool> HealthCheckAsync()
    {
        return await Task.FromResult(true);
    }
}
