using System.Net;
using System.Net.Sockets;
using Xunit;
using Xunit.Abstractions;

namespace NotifyMasterApi.Tests;

/// <summary>
/// Tests for port fallback mechanisms
/// </summary>
public class PortFallbackTests
{
    private readonly ITestOutputHelper _output;

    public PortFallbackTests(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public void IsPortAvailable_ShouldReturnTrue_WhenPortIsAvailable()
    {
        // Arrange
        var availablePort = GetAvailablePort();

        // Act
        var isAvailable = IsPortAvailable(availablePort);

        // Assert
        Assert.True(isAvailable);
    }

    [Fact]
    public void IsPortAvailable_ShouldReturnFalse_WhenPortIsInUse()
    {
        // Arrange
        var listener = new TcpListener(IPAddress.Loopback, 0);
        listener.Start();
        var port = ((IPEndPoint)listener.LocalEndpoint).Port;

        try
        {
            // Act
            var isAvailable = IsPortAvailable(port);

            // Assert
            Assert.False(isAvailable);
        }
        finally
        {
            listener.Stop();
        }
    }

    [Fact]
    public void FindAvailablePort_ShouldReturnFirstAvailablePort_FromPreferredList()
    {
        // Arrange
        var preferredPorts = new[] { GetAvailablePort(), GetAvailablePort() + 1, GetAvailablePort() + 2 };

        // Act
        var availablePort = FindAvailablePort(preferredPorts);

        // Assert
        Assert.Contains(availablePort, preferredPorts);
        Assert.True(IsPortAvailable(availablePort));
    }

    [Fact]
    public void FindAvailablePort_ShouldReturnRandomPort_WhenAllPreferredPortsUnavailable()
    {
        // Arrange
        var listeners = new List<TcpListener>();
        var preferredPorts = new[] { 0, 0, 0 }; // Use 0 to get system-assigned ports
        
        try
        {
            // Occupy some ports
            for (int i = 0; i < 3; i++)
            {
                var listener = new TcpListener(IPAddress.Loopback, 0);
                listener.Start();
                listeners.Add(listener);
                preferredPorts[i] = ((IPEndPoint)listener.LocalEndpoint).Port;
            }

            // Act
            var availablePort = FindAvailablePort(preferredPorts);

            // Assert
            Assert.NotEqual(0, availablePort);
            Assert.DoesNotContain(availablePort, preferredPorts);
            Assert.True(IsPortAvailable(availablePort));
        }
        finally
        {
            foreach (var listener in listeners)
            {
                listener.Stop();
            }
        }
    }

    [Theory]
    [InlineData(5120, 5121, 5122, 5123)]
    [InlineData(8080, 8081, 8082, 8083)]
    [InlineData(3000, 3001, 3002, 3003)]
    public void FindAvailablePort_ShouldHandleDifferentPortRanges(params int[] preferredPorts)
    {
        // Act
        var availablePort = FindAvailablePort(preferredPorts);

        // Assert
        Assert.True(availablePort > 0);
        Assert.True(availablePort <= 65535);
        
        // Should either be one of the preferred ports or a random available port
        var isPreferredPort = preferredPorts.Contains(availablePort);
        var isAvailable = IsPortAvailable(availablePort);
        
        Assert.True(isPreferredPort || isAvailable);
    }

    [Fact]
    public void PortFallback_ShouldProvideUserFriendlyMessages()
    {
        // Arrange
        var preferredPorts = new[] { GetAvailablePort() };
        var messages = new List<string>();

        // Act
        var availablePort = FindAvailablePortWithMessages(preferredPorts, messages);

        // Assert
        Assert.NotEqual(0, availablePort);
        
        if (availablePort == preferredPorts[0])
        {
            Assert.Contains(messages, m => m.Contains("Using preferred port"));
        }
        else
        {
            Assert.Contains(messages, m => m.Contains("Falling back to port") || m.Contains("Using random port"));
        }
    }

    [Fact]
    public void PortFallback_ShouldHandleEmptyPreferredPorts()
    {
        // Arrange
        var preferredPorts = Array.Empty<int>();

        // Act
        var availablePort = FindAvailablePort(preferredPorts);

        // Assert
        Assert.True(availablePort > 0);
        Assert.True(availablePort <= 65535);
        Assert.True(IsPortAvailable(availablePort));
    }

    [Fact]
    public void PortFallback_ShouldHandleInvalidPorts()
    {
        // Arrange
        var preferredPorts = new[] { -1, 0, 65536, 70000 }; // Invalid ports

        // Act
        var availablePort = FindAvailablePort(preferredPorts);

        // Assert
        Assert.True(availablePort > 0);
        Assert.True(availablePort <= 65535);
        Assert.True(IsPortAvailable(availablePort));
    }

    [Fact]
    public void PortFallback_ShouldBeThreadSafe()
    {
        // Arrange
        var preferredPorts = new[] { GetAvailablePort(), GetAvailablePort() + 1, GetAvailablePort() + 2 };
        var tasks = new List<Task<int>>();

        // Act
        for (int i = 0; i < 10; i++)
        {
            tasks.Add(Task.Run(() => FindAvailablePort(preferredPorts)));
        }

        var results = Task.WhenAll(tasks).Result;

        // Assert
        foreach (var port in results)
        {
            Assert.True(port > 0);
            Assert.True(port <= 65535);
        }

        // All results should be valid ports (though they might be the same)
        Assert.All(results, port => Assert.True(port > 0 && port <= 65535));
    }

    // Helper methods (simulating the actual implementation)
    private static bool IsPortAvailable(int port)
    {
        if (port <= 0 || port > 65535) return false;
        
        try
        {
            var listener = new TcpListener(IPAddress.Loopback, port);
            listener.Start();
            listener.Stop();
            return true;
        }
        catch (SocketException)
        {
            return false;
        }
    }

    private static int FindAvailablePort(int[] preferredPorts)
    {
        foreach (var port in preferredPorts)
        {
            if (IsPortAvailable(port))
                return port;
        }

        // If none of the preferred ports are available, find any available port
        var listener = new TcpListener(IPAddress.Loopback, 0);
        listener.Start();
        var availablePort = ((IPEndPoint)listener.LocalEndpoint).Port;
        listener.Stop();

        return availablePort;
    }

    private static int FindAvailablePortWithMessages(int[] preferredPorts, List<string> messages)
    {
        foreach (var port in preferredPorts)
        {
            if (IsPortAvailable(port))
            {
                messages.Add($"Using preferred port {port}");
                return port;
            }
        }

        var listener = new TcpListener(IPAddress.Loopback, 0);
        listener.Start();
        var availablePort = ((IPEndPoint)listener.LocalEndpoint).Port;
        listener.Stop();

        messages.Add($"Using random port {availablePort}");
        return availablePort;
    }

    private static int GetAvailablePort()
    {
        var listener = new TcpListener(IPAddress.Loopback, 0);
        listener.Start();
        var port = ((IPEndPoint)listener.LocalEndpoint).Port;
        listener.Stop();
        return port;
    }
}
