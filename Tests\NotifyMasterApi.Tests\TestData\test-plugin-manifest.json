{"name": "Test Plugin", "version": "1.0.0", "description": "Test plugin for unit testing", "author": "Test Team", "type": "SMS", "provider": "Test", "assemblyName": "TestPlugin.dll", "entryPoint": "NotifyMasterApi.Tests.TestPlugin", "isEnabled": true, "priority": 50, "supportedFeatures": ["SendSMS", "BulkSMS", "DeliveryStatus", "HealthCheck"], "configuration": {"apiKey": {"type": "string", "description": "API key for the test service", "isRequired": true, "isSecret": true}, "baseUrl": {"type": "string", "description": "Base URL for the test service", "defaultValue": "https://api.test.com", "isRequired": false, "isSecret": false}, "timeout": {"type": "int", "description": "Request timeout in seconds", "defaultValue": 30, "isRequired": false, "isSecret": false}, "retryAttempts": {"type": "int", "description": "Number of retry attempts", "defaultValue": 3, "isRequired": false, "isSecret": false}, "enableLogging": {"type": "bool", "description": "Enable detailed logging", "defaultValue": true, "isRequired": false, "isSecret": false}}, "minimumFrameworkVersion": "net9.0", "metadata": {"website": "https://test-plugin.com", "documentation": "https://docs.test-plugin.com", "support": "<EMAIL>", "license": "MIT", "tags": ["sms", "testing", "mock"]}}