# 📚 NotificationService - Comprehensive Documentation

## 🏗️ Architecture Overview

The NotificationService is a modern, plugin-based notification system built with .NET 9.0 that supports Email, SMS, and Push notifications through a unified API. The system uses a runtime plugin architecture allowing for dynamic loading and unloading of notification providers.

### 🎯 Key Features

- **Plugin Architecture**: Runtime loading/unloading of notification providers
- **Multi-Provider Support**: Email (SendGrid, SMTP), SMS (Twilio, BulkSMS), Push (Firebase)
- **Fallback Systems**: Redis and PostgreSQL with in-memory fallbacks
- **RESTful API**: Comprehensive REST endpoints with OpenAPI documentation
- **Health Monitoring**: Built-in health checks and metrics
- **Admin Interface**: Plugin management and system administration
- **Queue Management**: Redis-based message queuing with fallback
- **Logging & Metrics**: Comprehensive logging and performance metrics

## 📁 Solution Structure

```
NotificationService/
├── src/
│   ├── Contracts/                    # Service contracts and models
│   │   ├── EmailContract/           # Email service contracts
│   │   ├── SmsContract/             # SMS service contracts
│   │   ├── PushNotificationContract/ # Push notification contracts
│   │   ├── NotificationContract/    # Common notification models
│   │   └── PluginContract/          # Plugin system contracts
│   ├── Core/
│   │   └── PluginCore/              # Core plugin management system
│   ├── Libraries/                   # Service implementation libraries
│   │   ├── EmailService.Library/    # Email service implementation
│   │   ├── SmsService.Library/      # SMS service implementation
│   │   └── PushNotificationService.Library/ # Push service implementation
│   ├── Services/
│   │   └── NotifyMasterApi/         # Main API service
│   └── Plugins/                     # Plugin implementations (future)
├── Tests/                           # Comprehensive test suite
├── docs/                           # Documentation
└── scripts/                        # Build and deployment scripts
```

## 🔧 Core Components

### 1. NotifyMasterApi (Main Service)

The central API service that orchestrates all notification operations.

**Key Controllers:**
- `EmailController` - Email notification endpoints
- `SmsController` - SMS notification endpoints  
- `PushController` - Push notification endpoints
- `HealthController` - System health monitoring
- `AdminController` - System administration
- `*MetricsController` - Service-specific metrics

**Key Services:**
- `RuntimeNotificationService` - Routes notifications to plugins
- `RedisConnectionService` - Redis operations with fallback
- `DatabaseService` - PostgreSQL operations with fallback
- `NotificationLoggingService` - Centralized logging
- `NotificationQueueService` - Message queue management

### 2. Plugin System

**Core Interfaces:**
- `INotificationPlugin` - Base plugin interface
- `IEmailPlugin` - Email plugin contract
- `ISmsPlugin` - SMS plugin contract  
- `IPushPlugin` - Push notification plugin contract

**Plugin Manager:**
- `RuntimePluginManager` - Manages plugin lifecycle
- `PluginAutoLoaderService` - Automatic plugin discovery and loading

### 3. Gateway Pattern

Each notification type uses a gateway pattern for abstraction:

- `IEmailGateway` / `EmailGateway`
- `ISmsGateway` / `SmsGateway`
- `IPushGateway` / `PushGateway`

### 4. Service Libraries

**EmailService.Library:**
- Email sending functionality
- SMTP and API-based providers
- HTML/text support, attachments, CC/BCC

**SmsService.Library:**
- SMS sending functionality
- Multiple provider support
- Bulk messaging capabilities

**PushNotificationService.Library:**
- Push notification functionality
- Firebase Cloud Messaging integration
- Custom data and targeting

## 🔌 Plugin Architecture

### Plugin Development

1. **Create Plugin Project**
   ```csharp
   // Reference PluginContract and PluginCore
   public class MyEmailPlugin : BaseNotificationPlugin, IEmailPlugin
   {
       [NotificationPlugin("MyEmail", "1.0.0", PluginType.Email)]
       public PluginInfo PluginInfo { get; }
       
       public async Task<NotificationResponse> SendAsync(EmailRequest request)
       {
           // Implementation
       }
   }
   ```

2. **Plugin Manifest**
   ```json
   {
       "name": "MyEmailPlugin",
       "version": "1.0.0",
       "type": "Email",
       "provider": "MyProvider",
       "configuration": {
           "apiKey": "required",
           "endpoint": "optional"
       }
   }
   ```

### Plugin Loading

Plugins are loaded at runtime through:
- Assembly scanning in `plugins/` directory
- Automatic discovery via `PluginAutoLoaderService`
- Manual loading via Admin API
- Hot-swapping without service restart

## 🌐 API Endpoints

### Base URL
```
https://localhost:7001/api
```

### Email Endpoints
```
POST   /email/send              # Send single email
POST   /email/send/bulk         # Send bulk emails
GET    /email/status/{id}       # Get message status
GET    /email/history           # Get message history
GET    /email/providers         # Get available providers
POST   /email/providers/switch  # Switch provider
POST   /email/test/{provider}   # Test provider
POST   /email/providers/reload  # Reload providers
PUT    /email/providers/{name}/config # Update provider config
```

### SMS Endpoints
```
POST   /sms/send               # Send single SMS
POST   /sms/send/bulk          # Send bulk SMS
GET    /sms/status/{id}        # Get message status
GET    /sms/history            # Get message history
GET    /sms/providers          # Get available providers
POST   /sms/providers/switch   # Switch provider
POST   /sms/test/{provider}    # Test provider
```

### Push Notification Endpoints
```
POST   /push/send              # Send single push
POST   /push/send/bulk         # Send bulk push
GET    /push/status/{id}       # Get message status
GET    /push/history           # Get message history
GET    /push/providers         # Get available providers
POST   /push/providers/switch  # Switch provider
POST   /push/test/{provider}   # Test provider
```

### Admin Endpoints
```
GET    /admin/plugins          # List all plugins
POST   /admin/plugins/upload   # Upload new plugin
DELETE /admin/plugins/{name}   # Remove plugin
POST   /admin/plugins/{name}/enable   # Enable plugin
POST   /admin/plugins/{name}/disable  # Disable plugin
GET    /admin/system/status    # System status
POST   /admin/system/restart   # Restart services
```

### Health & Monitoring
```
GET    /health                 # Basic health check
GET    /health/plugins         # Plugin health status
GET    /health/plugins/{name}  # Specific plugin health
GET    /system/status          # Detailed system status
```

### Metrics Endpoints
```
GET    /email/metrics/summary     # Email metrics summary
GET    /email/metrics/detailed    # Detailed email metrics
GET    /sms/metrics/summary       # SMS metrics summary
GET    /sms/metrics/detailed      # Detailed SMS metrics
GET    /push/metrics/summary      # Push metrics summary
GET    /push/metrics/detailed     # Detailed push metrics
```

## 🗄️ Data Models

### Core Request Models

**SendNotificationRequest:**
```csharp
public class SendNotificationRequest
{
    public required string Message { get; set; }
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
    public string? DeviceToken { get; set; }
    public string? Subject { get; set; }
    public Dictionary<string, string>? Data { get; set; }
}
```

**EmailMessageRequest:**
```csharp
public class EmailMessageRequest
{
    public string To { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public bool IsHtml { get; set; } = true;
    public string? From { get; set; }
    public List<string>? Cc { get; set; }
    public List<string>? Bcc { get; set; }
    public List<EmailAttachment>? Attachments { get; set; }
}
```

**SmsMessageRequest:**
```csharp
public class SmsMessageRequest
{
    public string PhoneNumber { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
}
```

**PushMessageRequest:**
```csharp
public class PushMessageRequest
{
    public string DeviceToken { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public Dictionary<string, string>? Data { get; set; }
}
```

### Response Models

**NotificationResponse:**
```csharp
public class NotificationResponse
{
    public bool IsSuccess { get; set; }
    public string? MessageId { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}
```

## 🔧 Configuration

### appsettings.json Structure
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=notifications;Username=user;Password=****",
    "Redis": "localhost:6379"
  },
  "EmailSettings": {
    "DefaultProvider": "SendGrid",
    "SendGrid": {
      "ApiKey": "your-api-key",
      "FromEmail": "<EMAIL>"
    }
  },
  "SmsSettings": {
    "DefaultProvider": "Twilio",
    "Twilio": {
      "AccountSid": "your-account-sid",
      "AuthToken": "your-auth-token",
      "FromNumber": "+**********"
    }
  },
  "PushSettings": {
    "DefaultProvider": "Firebase",
    "Firebase": {
      "ProjectId": "your-project-id",
      "CredentialsPath": "path/to/credentials.json"
    }
  }
}
```

## 🚀 Getting Started

### Prerequisites
- .NET 9.0 SDK
- PostgreSQL (optional - has in-memory fallback)
- Redis (optional - has in-memory fallback)

### Quick Start
1. Clone the repository
2. Navigate to `src/` directory
3. Run `dotnet restore`
4. Run `dotnet build`
5. Configure `appsettings.json`
6. Run `dotnet run --project Services/NotifyMasterApi`

### Docker Deployment
```bash
docker-compose up -d
```

## 🧪 Testing

### Test Structure
- **Unit Tests**: Individual component testing
- **Integration Tests**: Service integration testing
- **Contract Tests**: API contract validation
- **Plugin Tests**: Plugin functionality testing

### Running Tests
```bash
# Run all tests
dotnet test

# Run specific test project
dotnet test Tests/Contracts/NotificationContract.Tests

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

## 📊 Monitoring & Metrics

### Health Checks
- Application health status
- Database connectivity
- Redis connectivity
- Plugin health status
- External service availability

### Metrics Collection
- Message send rates
- Success/failure rates
- Response times
- Provider performance
- Queue depths
- Error rates by type

### Logging
- Structured logging with Serilog
- Elasticsearch integration
- Request/response logging
- Performance metrics
- Error tracking

## 🔒 Security

### Authentication & Authorization
- API key authentication
- Role-based access control
- Plugin security validation
- Configuration encryption

### Data Protection
- Sensitive data encryption
- PII handling compliance
- Audit logging
- Secure configuration management

## 🚀 Deployment

### Environment Configuration
- Development: Local development with fallbacks
- Staging: Full infrastructure testing
- Production: High availability setup

### CI/CD Pipeline
- Automated testing
- Code quality checks
- Security scanning
- Automated deployment
- Health check validation

## 📈 Performance

### Optimization Features
- Connection pooling
- Message batching
- Async processing
- Caching strategies
- Queue management
- Load balancing

### Scalability
- Horizontal scaling support
- Plugin isolation
- Resource management
- Performance monitoring
- Auto-scaling capabilities

## 🔧 Troubleshooting

### Common Issues
1. **Plugin Loading Failures**: Check plugin manifest and dependencies
2. **Database Connection Issues**: Verify connection string and fallback activation
3. **Redis Connection Issues**: Check Redis availability and fallback mode
4. **Provider API Failures**: Verify API keys and provider status
5. **Queue Processing Issues**: Monitor queue depths and processing rates

### Debug Mode
Enable detailed logging in `appsettings.json`:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

## 📚 Additional Resources

- [API Endpoints Guide](API_ENDPOINTS.md)
- [Architecture Diagrams](ARCHITECTURE_DIAGRAMS.md)
- [Setup Guide](SETUP_GUIDE.md)
- [Plugin Development Guide](PLUGIN_DEVELOPMENT.md)
- [Deployment Guide](DEPLOYMENT_GUIDE.md)
- [Configuration Reference](CONFIGURATION_REFERENCE.md)

---

*This documentation is maintained alongside the codebase. For the latest updates, please refer to the repository documentation.*
