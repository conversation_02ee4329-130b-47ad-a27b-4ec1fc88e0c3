# 🔌 Plugin Development Guide

## 📋 Overview

This guide provides comprehensive instructions for developing plugins for the NotificationService. Plugins allow you to extend the system with new notification providers and custom functionality.

## 🏗️ Plugin Architecture

### Plugin Types

The system supports three types of notification plugins:

1. **Email Plugins** (`IEmailPlugin`) - Email service providers
2. **SMS Plugins** (`ISmsPlugin`) - SMS service providers  
3. **Push Plugins** (`IPushPlugin`) - Push notification providers

### Base Interfaces

All plugins must implement the base `INotificationPlugin` interface and one of the specific plugin interfaces.

## 🚀 Getting Started

### 1. Create Plugin Project

Create a new .NET class library project:

```bash
dotnet new classlib -n Plugin.Email.MyProvider
cd Plugin.Email.MyProvider
```

### 2. Add Required References

Add references to the plugin contracts:

```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Contracts\PluginContract\PluginContract.csproj" />
    <ProjectReference Include="..\..\src\Contracts\EmailContract\EmailContract.csproj" />
    <ProjectReference Include="..\..\src\Core\PluginCore\PluginCore.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
  </ItemGroup>
</Project>
```

### 3. Implement Plugin Interface

Create your plugin class:

```csharp
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PluginContract.Interfaces;
using PluginContract.Models;
using PluginContract.Attributes;
using PluginCore.Interfaces;
using EmailContract.Models;

namespace Plugin.Email.MyProvider;

[NotificationPlugin("MyProvider", "1.0.0", PluginType.Email)]
public class MyEmailPlugin : BaseNotificationPlugin, IEmailPlugin
{
    private readonly ILogger<MyEmailPlugin> _logger;
    private readonly IConfiguration _configuration;
    private MyProviderClient? _client;

    public MyEmailPlugin(ILogger<MyEmailPlugin> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public override PluginInfo PluginInfo => new()
    {
        Name = "MyProvider Email Plugin",
        Version = "1.0.0",
        Type = PluginType.Email,
        Provider = "MyProvider",
        Description = "Email plugin for MyProvider service",
        Author = "Your Name",
        SupportedFeatures = new[] { "HTML", "Attachments", "BulkSend" }
    };

    public override void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // Register plugin-specific services
        services.Configure<MyProviderSettings>(configuration.GetSection("MyProvider"));
        services.AddHttpClient<MyProviderClient>();
        services.AddScoped<IMyProviderService, MyProviderService>();
    }

    public override async Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        var settings = configuration.GetSection("MyProvider").Get<MyProviderSettings>();
        
        if (string.IsNullOrEmpty(settings?.ApiKey))
        {
            _logger.LogError("MyProvider API key is not configured");
            return false;
        }

        if (string.IsNullOrEmpty(settings.BaseUrl))
        {
            _logger.LogError("MyProvider base URL is not configured");
            return false;
        }

        return true;
    }

    public override async Task InitializeAsync(IConfiguration configuration)
    {
        var settings = configuration.GetSection("MyProvider").Get<MyProviderSettings>();
        _client = new MyProviderClient(settings!);
        
        // Test connection
        var isConnected = await _client.TestConnectionAsync();
        if (!isConnected)
        {
            throw new InvalidOperationException("Failed to connect to MyProvider API");
        }

        _logger.LogInformation("MyProvider email plugin initialized successfully");
    }

    public async Task<NotificationResponse> SendAsync(SendEmailRequest request)
    {
        try
        {
            _logger.LogInformation("Sending email via MyProvider to {To}", request.To);

            var result = await _client!.SendEmailAsync(new MyProviderEmailRequest
            {
                To = request.To,
                Subject = request.Subject,
                Body = request.Body,
                IsHtml = request.IsHtml,
                From = request.From,
                Cc = request.Cc,
                Bcc = request.Bcc,
                Attachments = request.Attachments?.Select(a => new MyProviderAttachment
                {
                    Name = a.FileName,
                    Content = a.Content,
                    ContentType = a.ContentType
                }).ToList()
            });

            if (result.Success)
            {
                _logger.LogInformation("Email sent successfully via MyProvider. MessageId: {MessageId}", result.MessageId);
                return new NotificationResponse(true, result.MessageId);
            }

            _logger.LogWarning("Failed to send email via MyProvider: {Error}", result.ErrorMessage);
            return new NotificationResponse(false, ErrorMessage: result.ErrorMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending email via MyProvider");
            return new NotificationResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<NotificationResponse> SendBulkAsync(IEnumerable<SendEmailRequest> requests)
    {
        var results = new List<NotificationResponse>();
        
        foreach (var request in requests)
        {
            var result = await SendAsync(request);
            results.Add(result);
        }

        var successCount = results.Count(r => r.IsSuccess);
        var totalCount = results.Count;

        return new NotificationResponse(
            successCount == totalCount,
            ErrorMessage: successCount < totalCount ? $"Only {successCount}/{totalCount} emails sent successfully" : null,
            Metadata: new Dictionary<string, object>
            {
                ["TotalCount"] = totalCount,
                ["SuccessCount"] = successCount,
                ["FailureCount"] = totalCount - successCount,
                ["Results"] = results
            }
        );
    }

    public async Task<NotificationResponse> GetMessageStatusAsync(string messageId)
    {
        try
        {
            var status = await _client!.GetMessageStatusAsync(messageId);
            
            return new NotificationResponse(true, messageId, Metadata: new Dictionary<string, object>
            {
                ["Status"] = status.Status,
                ["SentAt"] = status.SentAt,
                ["DeliveredAt"] = status.DeliveredAt,
                ["ErrorMessage"] = status.ErrorMessage
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message status for {MessageId}", messageId);
            return new NotificationResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<NotificationResponse> GetMessageHistoryAsync(string emailAddress)
    {
        try
        {
            var history = await _client!.GetMessageHistoryAsync(emailAddress);
            
            return new NotificationResponse(true, Metadata: new Dictionary<string, object>
            {
                ["Messages"] = history.Messages,
                ["TotalCount"] = history.TotalCount
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message history for {EmailAddress}", emailAddress);
            return new NotificationResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<NotificationResponse> ResendMessageAsync(string messageId)
    {
        try
        {
            var result = await _client!.ResendMessageAsync(messageId);
            
            return new NotificationResponse(result.Success, result.MessageId, result.ErrorMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resending message {MessageId}", messageId);
            return new NotificationResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<bool> ValidateConfigurationAsync()
    {
        return await ValidateConfigurationAsync(_configuration);
    }

    public override async Task<HealthCheckResult> CheckHealthAsync()
    {
        try
        {
            if (_client == null)
            {
                return new HealthCheckResult(false, "Plugin not initialized");
            }

            var isHealthy = await _client.HealthCheckAsync();
            return new HealthCheckResult(isHealthy, isHealthy ? "Healthy" : "Service unavailable");
        }
        catch (Exception ex)
        {
            return new HealthCheckResult(false, ex.Message);
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _client?.Dispose();
        }
        base.Dispose(disposing);
    }
}
```

## 📝 Configuration Models

Create configuration models for your plugin:

```csharp
public class MyProviderSettings
{
    public string ApiKey { get; set; } = string.Empty;
    public string BaseUrl { get; set; } = "https://api.myprovider.com";
    public string FromEmail { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; } = 30;
    public bool EnableRetry { get; set; } = true;
    public int MaxRetryAttempts { get; set; } = 3;
}
```

## 🔧 Plugin Client Implementation

Implement the actual service client:

```csharp
public class MyProviderClient : IDisposable
{
    private readonly HttpClient _httpClient;
    private readonly MyProviderSettings _settings;
    private readonly ILogger<MyProviderClient> _logger;

    public MyProviderClient(MyProviderSettings settings, HttpClient httpClient, ILogger<MyProviderClient> logger)
    {
        _settings = settings;
        _httpClient = httpClient;
        _logger = logger;
        
        _httpClient.BaseAddress = new Uri(_settings.BaseUrl);
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_settings.ApiKey}");
        _httpClient.Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds);
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("/api/health");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to test connection to MyProvider");
            return false;
        }
    }

    public async Task<MyProviderEmailResult> SendEmailAsync(MyProviderEmailRequest request)
    {
        try
        {
            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("/api/email/send", content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<MyProviderEmailResult>(responseContent);
                return result ?? new MyProviderEmailResult { Success = false, ErrorMessage = "Invalid response" };
            }
            
            return new MyProviderEmailResult 
            { 
                Success = false, 
                ErrorMessage = $"HTTP {response.StatusCode}: {responseContent}" 
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending email via MyProvider");
            return new MyProviderEmailResult { Success = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<bool> HealthCheckAsync()
    {
        return await TestConnectionAsync();
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}
```

## 📦 Plugin Manifest

Create a plugin manifest file (`plugin.json`):

```json
{
  "name": "MyProvider Email Plugin",
  "version": "1.0.0",
  "type": "Email",
  "provider": "MyProvider",
  "description": "Email plugin for MyProvider service",
  "author": "Your Name",
  "website": "https://myprovider.com",
  "supportedFeatures": [
    "HTML",
    "Attachments",
    "BulkSend",
    "MessageStatus",
    "MessageHistory"
  ],
  "configuration": {
    "required": [
      "ApiKey",
      "FromEmail"
    ],
    "optional": [
      "BaseUrl",
      "TimeoutSeconds",
      "EnableRetry",
      "MaxRetryAttempts"
    ],
    "schema": {
      "ApiKey": {
        "type": "string",
        "description": "API key for MyProvider service",
        "sensitive": true
      },
      "FromEmail": {
        "type": "string",
        "description": "Default sender email address",
        "format": "email"
      },
      "BaseUrl": {
        "type": "string",
        "description": "Base URL for MyProvider API",
        "default": "https://api.myprovider.com"
      },
      "TimeoutSeconds": {
        "type": "integer",
        "description": "Request timeout in seconds",
        "default": 30,
        "minimum": 5,
        "maximum": 300
      }
    }
  },
  "dependencies": {
    "PluginContract": "1.0.0",
    "EmailContract": "1.0.0",
    "PluginCore": "1.0.0"
  },
  "compatibility": {
    "minimumApiVersion": "1.0.0",
    "maximumApiVersion": "2.0.0"
  }
}
```

## 🧪 Testing Your Plugin

Create unit tests for your plugin:

```csharp
[TestClass]
public class MyEmailPluginTests
{
    private MyEmailPlugin _plugin;
    private Mock<ILogger<MyEmailPlugin>> _mockLogger;
    private IConfiguration _configuration;

    [TestInitialize]
    public void Setup()
    {
        _mockLogger = new Mock<ILogger<MyEmailPlugin>>();
        
        var configBuilder = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string>
            {
                ["MyProvider:ApiKey"] = "test-api-key",
                ["MyProvider:BaseUrl"] = "https://api.test.com",
                ["MyProvider:FromEmail"] = "<EMAIL>"
            });
        
        _configuration = configBuilder.Build();
        _plugin = new MyEmailPlugin(_mockLogger.Object, _configuration);
    }

    [TestMethod]
    public async Task ValidateConfigurationAsync_ValidConfig_ReturnsTrue()
    {
        // Act
        var result = await _plugin.ValidateConfigurationAsync(_configuration);

        // Assert
        Assert.IsTrue(result);
    }

    [TestMethod]
    public async Task ValidateConfigurationAsync_MissingApiKey_ReturnsFalse()
    {
        // Arrange
        var configBuilder = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string>
            {
                ["MyProvider:BaseUrl"] = "https://api.test.com"
            });
        var config = configBuilder.Build();

        // Act
        var result = await _plugin.ValidateConfigurationAsync(config);

        // Assert
        Assert.IsFalse(result);
    }

    [TestMethod]
    public async Task SendAsync_ValidRequest_ReturnsSuccess()
    {
        // Arrange
        await _plugin.InitializeAsync(_configuration);
        var request = new SendEmailRequest
        {
            To = "<EMAIL>",
            Subject = "Test",
            Body = "Test message",
            IsHtml = false
        };

        // Act
        var result = await _plugin.SendAsync(request);

        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.IsNotNull(result.MessageId);
    }
}
```

## 📦 Building and Packaging

### Build the Plugin

```bash
dotnet build --configuration Release
```

### Create Plugin Package

Create a deployment package with all necessary files:

```
MyProviderPlugin/
├── Plugin.Email.MyProvider.dll
├── Plugin.Email.MyProvider.pdb
├── plugin.json
├── README.md
└── dependencies/
    ├── Newtonsoft.Json.dll
    └── (other dependencies)
```

### Package as ZIP

```bash
# Create deployment package
mkdir MyProviderPlugin
cp bin/Release/net9.0/* MyProviderPlugin/
cp plugin.json MyProviderPlugin/
zip -r MyProviderPlugin.zip MyProviderPlugin/
```

## 🚀 Deployment

### Manual Deployment

1. Copy plugin files to the `plugins/` directory
2. Restart the NotificationService
3. Verify plugin loading in logs

### API Deployment

Upload via the Admin API:

```bash
curl -X POST "https://api.example.com/api/admin/plugins/upload" \
  -H "Authorization: Bearer your-api-key" \
  -F "plugin=@MyProviderPlugin.zip"
```

### Configuration

Add plugin configuration to `appsettings.json`:

```json
{
  "MyProvider": {
    "ApiKey": "your-api-key",
    "FromEmail": "<EMAIL>",
    "BaseUrl": "https://api.myprovider.com",
    "TimeoutSeconds": 30,
    "EnableRetry": true,
    "MaxRetryAttempts": 3
  }
}
```

## 🔍 Debugging

### Enable Debug Logging

```json
{
  "Logging": {
    "LogLevel": {
      "Plugin.Email.MyProvider": "Debug",
      "PluginCore": "Debug"
    }
  }
}
```

### Common Issues

1. **Plugin Not Loading**: Check assembly dependencies and manifest format
2. **Configuration Errors**: Verify configuration schema and required fields
3. **Runtime Errors**: Check logs for detailed error messages
4. **Health Check Failures**: Verify external service connectivity

## 📚 Best Practices

### Error Handling
- Always wrap external API calls in try-catch blocks
- Provide meaningful error messages
- Log errors with appropriate levels
- Return proper NotificationResponse objects

### Performance
- Use async/await for all I/O operations
- Implement connection pooling for HTTP clients
- Cache configuration where appropriate
- Implement proper timeout handling

### Security
- Never log sensitive configuration values
- Validate all input parameters
- Use secure HTTP clients with proper certificates
- Implement rate limiting if needed

### Testing
- Write comprehensive unit tests
- Mock external dependencies
- Test error scenarios
- Validate configuration handling

### Documentation
- Document all configuration options
- Provide usage examples
- Include troubleshooting guides
- Maintain version compatibility information

---

*For more examples and advanced scenarios, see the existing plugin implementations in the `src/Plugins/` directory.*
