# Docker Compose for CI/CD Testing
# Simulates different deployment scenarios with fallback systems

version: '3.8'

services:
  # Development scenario - All fallbacks active
  notifymaster-dev:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_CONFIGURATION: Release
    container_name: notifymaster-dev
    ports:
      - "5120:5120"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - CONNECTIONSTRINGS__REDIS=localhost:9999  # Invalid - triggers fallback
      - CONNECTIONSTRINGS__DEFAULTCONNECTION=    # Empty - triggers fallback
      - PLUGINSETTINGS__PLUGINDIRECTORY=/app/plugins
      - PLUGINSETTINGS__AUTOLOADPLUGINS=true
      - PLUGINSETTINGS__ENABLEHOTRELOAD=true
      - LOGGING__LOGLEVEL__DEFAULT=Debug
      - LOGGING__LOGLEVEL__NOTIFYMASTERAPI__SERVICES=Debug
    volumes:
      - ./plugins:/app/plugins
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5120/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - notifymaster-network

  # Staging scenario - Mixed infrastructure
  notifymaster-staging:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_CONFIGURATION: Release
    container_name: notifymaster-staging
    ports:
      - "5121:5120"
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - CONNECTIONSTRINGS__REDIS=redis:6379  # Real Redis
      - CONNECTIONSTRINGS__DEFAULTCONNECTION=  # DB fallback
      - PLUGINSETTINGS__PLUGINDIRECTORY=/app/plugins
      - PLUGINSETTINGS__AUTOLOADPLUGINS=true
      - PLUGINSETTINGS__ENABLEHOTRELOAD=false
      - LOGGING__LOGLEVEL__DEFAULT=Information
    volumes:
      - ./plugins:/app/plugins
      - ./logs:/app/logs
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5120/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - notifymaster-network

  # Production scenario - Full infrastructure
  notifymaster-prod:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_CONFIGURATION: Release
    container_name: notifymaster-prod
    ports:
      - "5122:5120"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - CONNECTIONSTRINGS__REDIS=redis:6379
      - CONNECTIONSTRINGS__DEFAULTCONNECTION=Host=postgres;Database=NotifyMaster;Username=postgres;Password=NotifyMaster123!
      - PLUGINSETTINGS__PLUGINDIRECTORY=/app/plugins
      - PLUGINSETTINGS__AUTOLOADPLUGINS=true
      - PLUGINSETTINGS__ENABLEHOTRELOAD=false
      - LOGGING__LOGLEVEL__DEFAULT=Warning
      - LOGGING__LOGLEVEL__NOTIFYMASTERAPI=Information
    volumes:
      - ./plugins:/app/plugins
      - ./logs:/app/logs
    depends_on:
      - redis
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5120/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - notifymaster-network

  # Redis service
  redis:
    image: redis:7-alpine
    container_name: notifymaster-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - notifymaster-network

  # PostgreSQL service
  postgres:
    image: postgres:15
    container_name: notifymaster-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=NotifyMaster
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=NotifyMaster123!
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - notifymaster-network

  # CI/CD Test Runner
  ci-test-runner:
    image: curlimages/curl:latest
    container_name: ci-test-runner
    depends_on:
      - notifymaster-dev
      - notifymaster-staging
      - notifymaster-prod
    volumes:
      - ./scripts:/scripts
    command: /scripts/run-ci-tests.sh
    networks:
      - notifymaster-network

  # Load balancer for production testing
  nginx:
    image: nginx:alpine
    container_name: notifymaster-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - notifymaster-prod
    networks:
      - notifymaster-network

  # Monitoring stack
  prometheus:
    image: prom/prometheus:latest
    container_name: notifymaster-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - notifymaster-network

  grafana:
    image: grafana/grafana:latest
    container_name: notifymaster-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - notifymaster-network

volumes:
  redis_data:
  postgres_data:
  prometheus_data:
  grafana_data:

networks:
  notifymaster-network:
    driver: bridge
