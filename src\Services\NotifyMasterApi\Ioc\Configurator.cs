using System.Reflection;
using System.Text.Json.Serialization;

using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using StackExchange.Redis;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using NotifyMasterApi.Services;
using PluginCore.Extensions;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.Elasticsearch;
using EmailService.Library.Extensions;
using SmsService.Library.Extensions;
using PushNotificationService.Library.Extensions;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Interfaces;

namespace NotifyMasterApi.Ioc;

public static class Configurator
{
    public static void InjectService(this IServiceCollection services , IConfiguration configuration)
    {
        services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            });
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "NotificationService API",
                Version = "v1",
                Description = "A comprehensive notification service supporting Email, SMS, and Push notifications with plugin architecture",
                Contact = new OpenApiContact
                {
                    Name = "BlackBee Software",
                    Email = "<EMAIL>"
                },
                License = new OpenApiLicense
                {
                    Name = "MIT License",
                    Url = new Uri("https://opensource.org/licenses/MIT")
                }
            });

            // Include XML comments for better documentation
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            if (File.Exists(xmlPath))
            {
                c.IncludeXmlComments(xmlPath);
            }

            // Add security definitions for future authentication
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer"
            });

            // Group endpoints by tags for better organization in Stoplight
            c.TagActionsBy(api => new[] { api.GroupName ?? api.ActionDescriptor.RouteValues["controller"] });
            c.DocInclusionPredicate((name, api) => true);
        });

        // Add Entity Framework with PostgreSQL and fallback
        services.AddDatabaseWithFallback(configuration);

        // Add library services instead of HTTP gateways
        services.AddEmailService(configuration);
        services.AddSmsService();
        services.AddPushNotificationService();

        // Add gateway services
        services.AddScoped<IEmailGateway, EmailGateway>();
        services.AddScoped<ISmsGateway, SmsGateway>();
        services.AddScoped<IPushGateway, PushGateway>();

        // Add runtime plugin manager
        services.AddScoped<IPluginManager, RuntimePluginManager>();

        // Add runtime notification service
        services.AddScoped<RuntimeNotificationService>();

        // Add plugin auto-loader service
        services.AddHostedService<PluginAutoLoaderService>();

        // Add notification logging service
        services.AddScoped<INotificationLoggingService, NotificationLoggingService>();

        // Add Redis connection with fallback
        services.AddSingleton<RedisConnectionService>();
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var connectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";

            try
            {
                var connection = ConnectionMultiplexer.Connect(connectionString);
                if (connection.IsConnected)
                {
                    Console.WriteLine("✅ Redis connection established successfully");
                    return connection;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️  Redis connection failed: {ex.Message}");
                Console.WriteLine("🔄 Services will use RedisConnectionService fallback");
            }

            // Return a dummy connection - services should use RedisConnectionService instead
            return ConnectionMultiplexer.Connect("localhost:1"); // This will fail but won't crash startup
        });

        // Add Redis queue services (replaces RabbitMQ)
        services.AddSingleton<IQueueService, RedisQueueService>();
        services.AddSingleton<INotificationQueueService, RedisNotificationQueueService>();
        services.AddHostedService<NotificationProcessingService>();

        // Add health checks
        services.AddHealthChecks()
            .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(), tags: new[] { "ready" })
            .AddCheck("database", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(), tags: new[] { "ready" })
            .AddCheck("redis", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(), tags: new[] { "ready" });

        // Add plugin system (keeping for backward compatibility)
        services.AddPluginSystem();

        Configuration.SetUp(configuration);
        AddLogging(configuration);
    }
    
    public static void ConfigurePipeline(this WebApplication app)
    {
        // Always expose OpenAPI JSON for Stoplight integration
        app.UseSwagger(c =>
        {
            c.RouteTemplate = "api-docs/{documentName}/openapi.json";
        });

        // Add a custom endpoint to serve the OpenAPI spec at a well-known location
        app.MapGet("/openapi.json", (HttpContext context) =>
        {
            context.Response.Redirect("/api-docs/v1/openapi.json");
        });

        // Add Stoplight Elements UI for development (optional)
        if (app.Environment.IsDevelopment())
        {
            app.MapGet("/docs", async (HttpContext context) =>
            {
                var html = """
                <!doctype html>
                <html lang="en">
                <head>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
                    <title>NotificationService API Documentation</title>
                    <script src="https://unpkg.com/@stoplight/elements/web-components.min.js"></script>
                    <link rel="stylesheet" href="https://unpkg.com/@stoplight/elements/styles.min.css">
                    <style>
                        body { margin: 0; padding: 0; }
                    </style>
                </head>
                <body>
                    <elements-api
                        apiDescriptionUrl="/openapi.json"
                        router="hash"
                        layout="sidebar"
                        hideInternal="false"
                    />
                </body>
                </html>
                """;

                context.Response.ContentType = "text/html";
                await context.Response.WriteAsync(html);
            });
        }

        app.UseRouting();
        app.UseHttpsRedirection();
        app.UseAuthorization();
        app.MapControllers();

        app.MapHealthChecks("/health/ready", new HealthCheckOptions()
        {
            Predicate = (check) => check.Tags.Contains("ready"),
        });

        app.MapHealthChecks("/health/live", new HealthCheckOptions());

    }
    
    private static void AddLogging(IConfiguration configuration)
    {
        string environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .Enrich.WithEnvironmentName()
            .Enrich.WithMachineName()
            .WriteTo.Debug()
            .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri(Configuration.AppSetting.ElasticSearch.Uri))
            {
                AutoRegisterTemplate = true,
                AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv6,
                IndexFormat = $"{Assembly.GetExecutingAssembly().GetName().Name!.ToLower().Replace(".", "-")}-{environment?.ToLower().Replace(".", "-")}-{DateTime.UtcNow:yyyy-MM}"
            })
            .ReadFrom.Configuration(configuration)
            .CreateLogger();
    }
}