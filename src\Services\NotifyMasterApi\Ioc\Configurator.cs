using System.Reflection;
using System.Text.Json.Serialization;
using System.Text;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.IdentityModel.Tokens;
using StackExchange.Redis;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using Scalar.AspNetCore;
using NotifyMasterApi.Authentication;
using NotifyMasterApi.Documentation;
using NotifyMasterApi.Services;
using PluginCore.Extensions;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.Elasticsearch;
using EmailService.Library.Extensions;
using SmsService.Library.Extensions;
using PushNotificationService.Library.Extensions;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Interfaces;
using NotifyMasterApi.Events.Hubs;
using NotifyMasterApi.Events.Services;
using NotifyMasterApi.Persistence.Extensions;

namespace NotifyMasterApi.Ioc;

public static class Configurator
{
    public static void InjectService(this IServiceCollection services , IConfiguration configuration)
    {
        services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            });
        services.AddEndpointsApiExplorer();

        // Add authentication services
        services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(options =>
        {
            var secretKey = configuration["Jwt:SecretKey"] ?? "NotifyMaster-Super-Secret-Key-That-Should-Be-Changed-In-Production-12345678901234567890";
            var issuer = configuration["Jwt:Issuer"] ?? "NotifyMasterApi";
            var audience = configuration["Jwt:Audience"] ?? "NotifyMasterApi";

            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secretKey)),
                ValidateIssuer = true,
                ValidIssuer = issuer,
                ValidateAudience = true,
                ValidAudience = audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };
        })
        .AddScheme<ApiKeyAuthenticationSchemeOptions, ApiKeyAuthenticationHandler>(
            ApiKeyAuthenticationSchemeOptions.DefaultScheme, options => { })
        .AddScheme<BasicAuthenticationSchemeOptions, BasicAuthenticationHandler>(
            BasicAuthenticationSchemeOptions.DefaultScheme, options => { });

        // Add OAuth authentication
        services.AddOAuthAuthentication(configuration);

        // Add authorization
        services.AddAuthorization(options =>
        {
            options.AddPolicy("AdminOnly", policy => policy.RequireRole("Admin"));
            options.AddPolicy("UserOrAdmin", policy => policy.RequireRole("User", "Admin"));
            options.AddPolicy("ReadPermission", policy => policy.RequireClaim("permission", "read"));
            options.AddPolicy("WritePermission", policy => policy.RequireClaim("permission", "write"));
            options.AddPolicy("AdminPermission", policy => policy.RequireClaim("permission", "admin"));
            options.AddPolicy("MetricsPermission", policy => policy.RequireClaim("permission", "metrics"));
        });

        // Register authentication services
        services.AddScoped<IJwtAuthenticationService, JwtAuthenticationService>();
        services.AddScoped<IApiKeyService, InMemoryApiKeyService>();
        services.AddScoped<IUserService, InMemoryUserService>();
        // Add enhanced Scalar documentation
        services.AddScalarDocumentationEnhancements();

        // Add persistence layer with database fallback
        services.AddPersistence(configuration);

        // Add library services instead of HTTP gateways
        services.AddEmailService(configuration);
        services.AddSmsService();
        services.AddPushNotificationService();

        // Add gateway services
        services.AddScoped<IEmailGateway, EmailGateway>();
        services.AddScoped<ISmsGateway, SmsGateway>();
        services.AddScoped<IPushGateway, PushGateway>();

        // Add runtime plugin manager
        services.AddScoped<IPluginManager, RuntimePluginManager>();

        // Add runtime notification service
        services.AddScoped<RuntimeNotificationService>();

        // Add plugin auto-loader service
        services.AddHostedService<PluginAutoLoaderService>();

        // Add notification logging service
        services.AddScoped<INotificationLoggingService, NotificationLoggingService>();

        // Add Redis connection with fallback
        services.AddSingleton<RedisConnectionService>();
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var connectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";

            try
            {
                var connection = ConnectionMultiplexer.Connect(connectionString);
                if (connection.IsConnected)
                {
                    Console.WriteLine("✅ Redis connection established successfully");
                    return connection;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️  Redis connection failed: {ex.Message}");
                Console.WriteLine("🔄 Services will use RedisConnectionService fallback");
            }

            // Return a dummy connection - services should use RedisConnectionService instead
            return ConnectionMultiplexer.Connect("localhost:1"); // This will fail but won't crash startup
        });

        // Add Redis queue services (replaces RabbitMQ)
        services.AddSingleton<IQueueService, RedisQueueService>();
        services.AddSingleton<INotificationQueueService, RedisNotificationQueueService>();
        services.AddHostedService<NotificationProcessingService>();

        // Add health checks
        services.AddHealthChecks()
            .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(), tags: new[] { "ready" })
            .AddCheck("database", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(), tags: new[] { "ready" })
            .AddCheck("redis", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(), tags: new[] { "ready" });

        // Add plugin system (keeping for backward compatibility)
        services.AddPluginSystem();

        // Add SignalR for WebSocket events
        services.AddSignalR(options =>
        {
            options.EnableDetailedErrors = true;
            options.KeepAliveInterval = TimeSpan.FromSeconds(15);
            options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
        });

        // Add event publishing services
        services.AddScoped<IEventPublisher, EventPublisher>();

        Configuration.SetUp(configuration);
        AddLogging(configuration);
    }
    
    public static void ConfigurePipeline(this WebApplication app)
    {
        // Map OpenAPI endpoints
        app.MapOpenApi();

        // Add Scalar API documentation UI
        app.MapScalarApiReference(options =>
        {
            options
                .WithTitle("NotificationService API")
                .WithTheme(ScalarTheme.Purple)
                .WithDefaultHttpClient(ScalarTarget.CSharp, ScalarClient.HttpClient);
        });

        app.UseRouting();
        app.UseHttpsRedirection();
        app.UseStaticFiles();

        // Add authentication and authorization middleware
        app.UseAuthentication();
        app.UseAuthorization();

        app.MapControllers();

        // Map SignalR hubs for WebSocket events
        app.MapHub<NotificationHub>("/events/notifications");
        app.MapHub<AdminHub>("/events/admin");

        app.MapHealthChecks("/health/ready", new HealthCheckOptions()
        {
            Predicate = (check) => check.Tags.Contains("ready"),
        });

        app.MapHealthChecks("/health/live", new HealthCheckOptions());

    }
    
    private static void AddLogging(IConfiguration configuration)
    {
        string environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .Enrich.WithEnvironmentName()
            .Enrich.WithMachineName()
            .WriteTo.Debug()
            .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri(Configuration.AppSetting.ElasticSearch.Uri))
            {
                AutoRegisterTemplate = true,
                AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv6,
                IndexFormat = $"{Assembly.GetExecutingAssembly().GetName().Name!.ToLower().Replace(".", "-")}-{environment?.ToLower().Replace(".", "-")}-{DateTime.UtcNow:yyyy-MM}"
            })
            .ReadFrom.Configuration(configuration)
            .CreateLogger();
    }
}