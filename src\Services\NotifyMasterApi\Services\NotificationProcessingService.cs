using NotificationContract.Enums;
using NotificationContract.Models;
using System.Diagnostics;
using System.Diagnostics.Metrics;

namespace NotifyMasterApi.Services;

/// <summary>
/// Background service that processes notifications from the queue with enhanced error handling and monitoring.
/// </summary>
/// <remarks>
/// This service continuously checks the notification queue for new notifications and processes them
/// through the appropriate service channels (Email, SMS, Push). It provides comprehensive error handling,
/// retry logic, dead letter queue support, and performance monitoring with metrics collection.
///
/// Key features:
/// - Configurable processing intervals and batch sizes
/// - Automatic retry with exponential backoff
/// - Dead letter queue for failed notifications
/// - Performance metrics and health monitoring
/// - Graceful shutdown with in-flight request completion
/// </remarks>
public class NotificationProcessingService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<NotificationProcessingService> _logger;
    private readonly IConfiguration _configuration;
    private readonly TimeSpan _processingInterval;
    private readonly int _maxConcurrentProcessing;
    private readonly int _maxRetryAttempts;
    private readonly SemaphoreSlim _processingSemaphore;
    private readonly Meter _meter;
    private readonly Counter<long> _processedNotificationsCounter;
    private readonly Counter<long> _failedNotificationsCounter;
    private readonly Histogram<double> _processingDurationHistogram;

    /// <summary>
    /// Initializes a new instance of the <see cref="NotificationProcessingService"/> class.
    /// </summary>
    /// <param name="serviceProvider">The service provider for dependency resolution</param>
    /// <param name="logger">The logger instance for this service</param>
    /// <param name="configuration">The configuration provider for service settings</param>
    /// <exception cref="ArgumentNullException">Thrown when any required parameter is null</exception>
    public NotificationProcessingService(
        IServiceProvider serviceProvider,
        ILogger<NotificationProcessingService> logger,
        IConfiguration configuration)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        // Load configuration with defaults
        _processingInterval = TimeSpan.FromSeconds(_configuration.GetValue<int>("NotificationProcessing:IntervalSeconds", 5));
        _maxConcurrentProcessing = _configuration.GetValue<int>("NotificationProcessing:MaxConcurrent", 10);
        _maxRetryAttempts = _configuration.GetValue<int>("NotificationProcessing:MaxRetryAttempts", 3);

        _processingSemaphore = new SemaphoreSlim(_maxConcurrentProcessing, _maxConcurrentProcessing);

        // Initialize metrics
        _meter = new Meter("NotificationService.Processing");
        _processedNotificationsCounter = _meter.CreateCounter<long>("notifications_processed_total",
            description: "Total number of notifications processed");
        _failedNotificationsCounter = _meter.CreateCounter<long>("notifications_failed_total",
            description: "Total number of notifications that failed processing");
        _processingDurationHistogram = _meter.CreateHistogram<double>("notification_processing_duration_seconds",
            description: "Duration of notification processing in seconds");

        _logger.LogInformation("NotificationProcessingService initialized with interval: {Interval}, max concurrent: {MaxConcurrent}, max retries: {MaxRetries}",
            _processingInterval, _maxConcurrentProcessing, _maxRetryAttempts);
    }

    /// <summary>
    /// Main execution loop for processing notifications from the queue.
    /// </summary>
    /// <param name="stoppingToken">Token to signal when the service should stop</param>
    /// <returns>A task representing the asynchronous operation</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Notification processing service started with {MaxConcurrent} concurrent processors",
            _maxConcurrentProcessing);

        var activeTasks = new List<Task>();

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // Clean up completed tasks
                    activeTasks.RemoveAll(t => t.IsCompleted);

                    // Check if we can process more notifications
                    if (activeTasks.Count < _maxConcurrentProcessing)
                    {
                        using var scope = _serviceProvider.CreateScope();
                        var queueService = scope.ServiceProvider.GetRequiredService<INotificationQueueService>();

                        var notification = await queueService.DequeueNotificationAsync();
                        if (notification != null)
                        {
                            // Start processing notification concurrently
                            var processingTask = ProcessNotificationWithSemaphoreAsync(notification, stoppingToken);
                            activeTasks.Add(processingTask);

                            _logger.LogDebug("Started processing notification {NotificationId}, active tasks: {ActiveCount}",
                                notification.Id, activeTasks.Count);
                        }
                        else
                        {
                            // No notifications available, wait before checking again
                            await Task.Delay(_processingInterval, stoppingToken);
                        }
                    }
                    else
                    {
                        // Wait for at least one task to complete before checking for more work
                        if (activeTasks.Count > 0)
                        {
                            await Task.WhenAny(activeTasks);
                        }
                        else
                        {
                            await Task.Delay(_processingInterval, stoppingToken);
                        }
                    }
                }
                catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                {
                    _logger.LogInformation("Notification processing service cancellation requested");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error in notification processing service main loop");
                    _failedNotificationsCounter.Add(1, new KeyValuePair<string, object?>("error_type", "service_loop_error"));

                    // Wait before retrying to avoid tight error loops
                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                }
            }

            // Wait for all active tasks to complete during shutdown
            if (activeTasks.Count > 0)
            {
                _logger.LogInformation("Waiting for {ActiveCount} active notification processing tasks to complete",
                    activeTasks.Count);

                using var shutdownCts = new CancellationTokenSource(TimeSpan.FromMinutes(2));
                try
                {
                    await Task.WhenAll(activeTasks).WaitAsync(shutdownCts.Token);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogWarning("Some notification processing tasks did not complete within shutdown timeout");
                }
            }
        }
        finally
        {
            _logger.LogInformation("Notification processing service stopped");
            _meter.Dispose();
            _processingSemaphore.Dispose();
        }
    }

    /// <summary>
    /// Processes a single notification with semaphore-based concurrency control.
    /// </summary>
    /// <param name="notification">The notification to process</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task representing the asynchronous operation</returns>
    private async Task ProcessNotificationWithSemaphoreAsync(NotificationQueueItem notification, CancellationToken cancellationToken)
    {
        await _processingSemaphore.WaitAsync(cancellationToken);
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var queueService = scope.ServiceProvider.GetRequiredService<INotificationQueueService>();
            var loggingService = scope.ServiceProvider.GetRequiredService<INotificationLoggingService>();
            var emailService = scope.ServiceProvider.GetRequiredService<EmailService.Library.Interfaces.IEmailService>();
            var smsService = scope.ServiceProvider.GetRequiredService<SmsService.Library.Interfaces.ISmsService>();
            var pushService = scope.ServiceProvider.GetRequiredService<PushNotificationService.Library.Interfaces.IPushNotificationService>();

            await ProcessNotificationAsync(notification, queueService, loggingService, emailService, smsService, pushService, cancellationToken);
        }
        finally
        {
            _processingSemaphore.Release();
        }
    }

    /// <summary>
    /// Processes a single notification with comprehensive error handling and retry logic.
    /// </summary>
    /// <param name="notification">The notification to process</param>
    /// <param name="queueService">The queue service for updating notification status</param>
    /// <param name="loggingService">The logging service for tracking processing results</param>
    /// <param name="emailService">The email service for processing email notifications</param>
    /// <param name="smsService">The SMS service for processing SMS notifications</param>
    /// <param name="pushService">The push notification service for processing push notifications</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task representing the asynchronous operation</returns>
    private async Task ProcessNotificationAsync(
        NotificationQueueItem notification,
        INotificationQueueService queueService,
        INotificationLoggingService loggingService,
        EmailService.Library.Interfaces.IEmailService emailService,
        SmsService.Library.Interfaces.ISmsService smsService,
        PushNotificationService.Library.Interfaces.IPushNotificationService pushService,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var success = false;
        string? errorMessage = null;
        var providerName = GetProviderName(notification.Type);

        try
        {
            _logger.LogInformation("Processing notification {QueueId} of type {Type} for {Recipient} (attempt {RetryCount}/{MaxRetries})",
                notification.QueueId, notification.Type, notification.Recipient, notification.RetryCount + 1, _maxRetryAttempts);

            // Update notification status to processing
            await loggingService.UpdateNotificationStatusAsync(
                notification.MessageId,
                NotifyMasterApi.Data.Entities.NotificationStatus.Processing,
                providerName);

            // Process notification based on type with timeout
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(TimeSpan.FromMinutes(5)); // 5-minute timeout per notification

            try
            {
                success = notification.Type switch
                {
                    NotificationType.Email => await ProcessEmailNotificationAsync(notification, emailService, loggingService, timeoutCts.Token),
                    NotificationType.Sms => await ProcessSmsNotificationAsync(notification, smsService, loggingService, timeoutCts.Token),
                    NotificationType.PushMessage => await ProcessPushNotificationAsync(notification, pushService, loggingService, timeoutCts.Token),
                    _ => throw new NotSupportedException($"Unsupported notification type: {notification.Type}")
                };
            }
            catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested && !cancellationToken.IsCancellationRequested)
            {
                errorMessage = "Notification processing timed out";
                _logger.LogWarning("Notification {QueueId} processing timed out after 5 minutes", notification.QueueId);
            }

            stopwatch.Stop();

            // Record metrics
            var processingDuration = stopwatch.Elapsed.TotalSeconds;
            _processingDurationHistogram.Record(processingDuration,
                new KeyValuePair<string, object?>("notification_type", notification.Type.ToString()),
                new KeyValuePair<string, object?>("success", success));

            if (success)
            {
                await queueService.MarkNotificationProcessedAsync(notification.QueueId);
                await loggingService.UpdateMetricsAsync(notification.Type, providerName, true, stopwatch.ElapsedMilliseconds);

                _processedNotificationsCounter.Add(1,
                    new KeyValuePair<string, object?>("notification_type", notification.Type.ToString()),
                    new KeyValuePair<string, object?>("provider", providerName));

                _logger.LogInformation("Successfully processed notification {QueueId} in {Duration:F2}s",
                    notification.QueueId, processingDuration);
            }
            else
            {
                await HandleNotificationFailureAsync(notification, queueService, loggingService,
                    errorMessage ?? "Unknown processing error", stopwatch.ElapsedMilliseconds, providerName);
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            errorMessage = ex.Message;

            _logger.LogError(ex, "Unexpected error processing notification {QueueId} of type {Type}",
                notification.QueueId, notification.Type);

            await HandleNotificationFailureAsync(notification, queueService, loggingService,
                $"Unexpected error: {ex.Message}", stopwatch.ElapsedMilliseconds, providerName);
        }
    }

    /// <summary>
    /// Handles notification processing failures with retry logic and dead letter queue support.
    /// </summary>
    /// <param name="notification">The failed notification</param>
    /// <param name="queueService">The queue service for updating notification status</param>
    /// <param name="loggingService">The logging service for tracking failures</param>
    /// <param name="errorMessage">The error message describing the failure</param>
    /// <param name="processingTimeMs">The processing time in milliseconds</param>
    /// <param name="providerName">The name of the provider that failed</param>
    /// <returns>A task representing the asynchronous operation</returns>
    private async Task HandleNotificationFailureAsync(
        NotificationQueueItem notification,
        INotificationQueueService queueService,
        INotificationLoggingService loggingService,
        string errorMessage,
        long processingTimeMs,
        string providerName)
    {
        var shouldRetry = notification.RetryCount < _maxRetryAttempts;

        if (shouldRetry)
        {
            // Calculate exponential backoff delay
            var delaySeconds = Math.Pow(2, notification.RetryCount) * 30; // 30s, 60s, 120s, etc.
            var retryAt = DateTime.UtcNow.AddSeconds(delaySeconds);

            await queueService.ScheduleRetryAsync(notification.QueueId, retryAt, errorMessage);

            _logger.LogWarning("Notification {QueueId} failed (attempt {RetryCount}/{MaxRetries}), scheduled for retry at {RetryAt}: {ErrorMessage}",
                notification.QueueId, notification.RetryCount + 1, _maxRetryAttempts, retryAt, errorMessage);
        }
        else
        {
            // Move to dead letter queue after max retries
            await queueService.MoveToDeadLetterQueueAsync(notification.QueueId, errorMessage);

            _logger.LogError("Notification {QueueId} moved to dead letter queue after {MaxRetries} failed attempts: {ErrorMessage}",
                notification.QueueId, _maxRetryAttempts, errorMessage);
        }

        await loggingService.UpdateMetricsAsync(notification.Type, providerName, false, processingTimeMs);

        _failedNotificationsCounter.Add(1,
            new KeyValuePair<string, object?>("notification_type", notification.Type.ToString()),
            new KeyValuePair<string, object?>("provider", providerName),
            new KeyValuePair<string, object?>("retry_exhausted", !shouldRetry));
    }
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            errorMessage = ex.Message;
            await queueService.MarkNotificationFailedAsync(notification.QueueId, errorMessage, notification.RetryCount);
            await loggingService.UpdateMetricsAsync(notification.Type, GetProviderName(notification.Type), false, stopwatch.ElapsedMilliseconds);
            await loggingService.LogErrorAsync(notification.Type, GetProviderName(notification.Type), "PROCESSING_ERROR", errorMessage, notification.Recipient, notification.MessageId, ex.StackTrace);
            _logger.LogError(ex, "Exception processing notification {QueueId}", notification.QueueId);
        }
    }

    private async Task<bool> ProcessEmailNotificationAsync(NotificationQueueItem notification, EmailService.Library.Interfaces.IEmailService emailService, INotificationLoggingService loggingService)
    {
        try
        {
            var emailRequest = new EmailMessageRequest
            {
                To = notification.Recipient,
                Subject = notification.Subject,
                Body = notification.Content,
                IsHtml = true
            };

            var result = await emailService.SendAsync(emailRequest);
            
            await loggingService.UpdateNotificationStatusAsync(
                notification.MessageId,
                result.IsSuccess ? NotifyMasterApi.Data.Entities.NotificationStatus.Sent : NotifyMasterApi.Data.Entities.NotificationStatus.Failed,
                "EmailService",
                result.IsSuccess ? null : result.ErrorMessage,
                result.IsSuccess ? "Email sent successfully" : null);

            if (!result.IsSuccess)
            {
                await loggingService.LogErrorAsync(NotificationType.Email, "EmailService", "SEND_FAILED", result.ErrorMessage ?? "Unknown error", notification.Recipient, notification.MessageId);
            }

            return result.IsSuccess;
        }
        catch (Exception ex)
        {
            await loggingService.LogErrorAsync(NotificationType.Email, "EmailService", "EXCEPTION", ex.Message, notification.Recipient, notification.MessageId, ex.StackTrace);
            return false;
        }
    }

    private async Task<bool> ProcessSmsNotificationAsync(NotificationQueueItem notification, SmsService.Library.Interfaces.ISmsService smsService, INotificationLoggingService loggingService)
    {
        try
        {
            var smsRequest = new SmsMessageRequest
            {
                PhoneNumber = notification.Recipient,
                Message = notification.Content
            };

            var result = await smsService.SendAsync(smsRequest);
            
            await loggingService.UpdateNotificationStatusAsync(
                notification.MessageId,
                result.IsSuccess ? NotifyMasterApi.Data.Entities.NotificationStatus.Sent : NotifyMasterApi.Data.Entities.NotificationStatus.Failed,
                "SmsService",
                result.IsSuccess ? null : result.ErrorMessage,
                result.IsSuccess ? "SMS sent successfully" : null);

            if (!result.IsSuccess)
            {
                await loggingService.LogErrorAsync(NotificationType.Sms, "SmsService", "SEND_FAILED", result.ErrorMessage ?? "Unknown error", notification.Recipient, notification.MessageId);
            }

            return result.IsSuccess;
        }
        catch (Exception ex)
        {
            await loggingService.LogErrorAsync(NotificationType.Sms, "SmsService", "EXCEPTION", ex.Message, notification.Recipient, notification.MessageId, ex.StackTrace);
            return false;
        }
    }

    private async Task<bool> ProcessPushNotificationAsync(NotificationQueueItem notification, PushNotificationService.Library.Interfaces.IPushNotificationService pushService, INotificationLoggingService loggingService)
    {
        try
        {
            var pushRequest = new PushMessageRequest
            {
                DeviceToken = notification.Recipient,
                Title = notification.Subject,
                Body = notification.Content
            };

            var result = await pushService.SendAsync(pushRequest);
            
            await loggingService.UpdateNotificationStatusAsync(
                notification.MessageId,
                result.IsSuccess ? NotifyMasterApi.Data.Entities.NotificationStatus.Sent : NotifyMasterApi.Data.Entities.NotificationStatus.Failed,
                "PushService",
                result.IsSuccess ? null : result.ErrorMessage,
                result.IsSuccess ? "Push notification sent successfully" : null);

            if (!result.IsSuccess)
            {
                await loggingService.LogErrorAsync(NotificationType.PushMessage, "PushService", "SEND_FAILED", result.ErrorMessage ?? "Unknown error", notification.Recipient, notification.MessageId);
            }

            return result.IsSuccess;
        }
        catch (Exception ex)
        {
            await loggingService.LogErrorAsync(NotificationType.PushMessage, "PushService", "EXCEPTION", ex.Message, notification.Recipient, notification.MessageId, ex.StackTrace);
            return false;
        }
    }

    private string GetProviderName(NotificationType type)
    {
        return type switch
        {
            NotificationType.Email => "EmailService",
            NotificationType.Sms => "SmsService",
            NotificationType.PushMessage => "PushService",
            _ => "Unknown"
        };
    }
}
