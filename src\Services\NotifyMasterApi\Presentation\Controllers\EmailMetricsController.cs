using Microsoft.AspNetCore.Mvc;
using EmailService.Library.Interfaces;
using NotifyMasterApi.Services;
using NotificationContract.Enums;

namespace NotifyMasterApi.Controllers;

/// <summary>
/// Email service metrics and analytics controller
/// </summary>
[ApiController]
[Route("api/email/metrics")]
[Produces("application/json")]
[Tags("Email Metrics")]
public class EmailMetricsController : ControllerBase
{
    private readonly IEmailService _emailService;
    private readonly INotificationLoggingService _loggingService;
    private readonly ILogger<EmailMetricsController> _logger;

    public EmailMetricsController(
        IEmailService emailService,
        INotificationLoggingService loggingService,
        ILogger<EmailMetricsController> logger)
    {
        _emailService = emailService;
        _loggingService = loggingService;
        _logger = logger;
    }

    /// <summary>
    /// Get email service summary metrics
    /// </summary>
    /// <returns>Summary metrics including total sent, success rate, and recent activity</returns>
    /// <response code="200">Summary metrics retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("summary")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetSummaryMetrics()
    {
        try
        {
            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Email);
            var serviceMetrics = await _emailService.GetSummaryMetricsAsync();
            
            return Ok(new
            {
                Database = metrics,
                Service = serviceMetrics,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email summary metrics");
            return StatusCode(500, new { Error = "Failed to get summary metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get detailed email metrics for a specific time period
    /// </summary>
    /// <param name="from">Start date for metrics (optional)</param>
    /// <param name="to">End date for metrics (optional)</param>
    /// <param name="provider">Filter by specific email provider (optional)</param>
    /// <returns>Detailed metrics including hourly/daily breakdowns</returns>
    /// <response code="200">Detailed metrics retrieved successfully</response>
    /// <response code="400">Invalid date range</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("detailed")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetDetailedMetrics(
        [FromQuery] DateTime? from = null,
        [FromQuery] DateTime? to = null,
        [FromQuery] string? provider = null)
    {
        try
        {
            if (from.HasValue && to.HasValue && from > to)
            {
                return BadRequest(new { Error = "Start date cannot be after end date" });
            }

            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Email, provider, from, to);
            var serviceMetrics = await _emailService.GetDetailedMetricsAsync(from, to);
            
            return Ok(new
            {
                Database = metrics,
                Service = serviceMetrics,
                Period = new { From = from, To = to },
                Provider = provider,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting detailed email metrics");
            return StatusCode(500, new { Error = "Failed to get detailed metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get email error metrics and failure analysis
    /// </summary>
    /// <param name="from">Start date for error analysis (optional)</param>
    /// <param name="to">End date for error analysis (optional)</param>
    /// <param name="provider">Filter by specific email provider (optional)</param>
    /// <param name="unresolved">Show only unresolved errors</param>
    /// <returns>Error metrics and failure patterns</returns>
    /// <response code="200">Error metrics retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("errors")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetErrorMetrics(
        [FromQuery] DateTime? from = null,
        [FromQuery] DateTime? to = null,
        [FromQuery] string? provider = null,
        [FromQuery] bool unresolved = false)
    {
        try
        {
            var errors = await _loggingService.GetErrorsAsync(NotificationType.Email, provider, from, to, unresolved);
            var serviceErrors = await _emailService.GetErrorMetricsAsync();
            
            return Ok(new
            {
                Database = errors,
                Service = serviceErrors,
                Period = new { From = from, To = to },
                Provider = provider,
                UnresolvedOnly = unresolved,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email error metrics");
            return StatusCode(500, new { Error = "Failed to get error metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get monthly email statistics and trends
    /// </summary>
    /// <param name="months">Number of months to include (default: 12)</param>
    /// <param name="provider">Filter by specific email provider (optional)</param>
    /// <returns>Monthly statistics and trend analysis</returns>
    /// <response code="200">Monthly statistics retrieved successfully</response>
    /// <response code="400">Invalid months parameter</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("monthly")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetMonthlyStatistics(
        [FromQuery] int months = 12,
        [FromQuery] string? provider = null)
    {
        try
        {
            if (months <= 0 || months > 24)
            {
                return BadRequest(new { Error = "Months must be between 1 and 24" });
            }

            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddMonths(-months);
            
            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Email, provider, startDate, endDate);
            var serviceStats = await _emailService.GetMonthlyStatisticsAsync();
            
            return Ok(new
            {
                Database = metrics,
                Service = serviceStats,
                Period = new { StartDate = startDate, EndDate = endDate, Months = months },
                Provider = provider,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monthly email statistics");
            return StatusCode(500, new { Error = "Failed to get monthly statistics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get real-time email service performance metrics
    /// </summary>
    /// <returns>Real-time performance data including response times and throughput</returns>
    /// <response code="200">Performance metrics retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("performance")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetPerformanceMetrics()
    {
        try
        {
            // Get recent performance data (last hour)
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddHours(-1);
            
            var recentMetrics = await _loggingService.GetMetricsAsync(NotificationType.Email, null, startTime, endTime);
            
            return Ok(new
            {
                RecentActivity = recentMetrics,
                Period = new { StartTime = startTime, EndTime = endTime },
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email performance metrics");
            return StatusCode(500, new { Error = "Failed to get performance metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get email delivery rate analysis by provider
    /// </summary>
    /// <param name="days">Number of days to analyze (default: 30)</param>
    /// <returns>Delivery rate comparison across providers</returns>
    /// <response code="200">Delivery rate analysis retrieved successfully</response>
    /// <response code="400">Invalid days parameter</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("delivery-rates")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetDeliveryRateAnalysis([FromQuery] int days = 30)
    {
        try
        {
            if (days <= 0 || days > 365)
            {
                return BadRequest(new { Error = "Days must be between 1 and 365" });
            }

            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-days);
            
            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Email, null, startDate, endDate);
            
            return Ok(new
            {
                DeliveryAnalysis = metrics,
                Period = new { StartDate = startDate, EndDate = endDate, Days = days },
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery rate analysis");
            return StatusCode(500, new { Error = "Failed to get delivery rate analysis", Message = ex.Message });
        }
    }
}
