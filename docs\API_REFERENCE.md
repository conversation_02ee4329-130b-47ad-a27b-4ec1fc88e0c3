# 🔌 NotificationService API Reference

## 📋 Overview

This document provides detailed API reference for the NotificationService, including request/response schemas, authentication, error handling, and usage examples.

## 🔐 Authentication

### API Key Authentication
```http
Authorization: Bearer your-api-key
Content-Type: application/json
```

### Headers
- `Authorization`: Bearer token for API access
- `Content-Type`: application/json
- `X-Correlation-ID`: Optional correlation ID for request tracking

## 📧 Email API

### Send Single Email

**Endpoint:** `POST /api/email/send`

**Request Body:**
```json
{
  "to": "<EMAIL>",
  "subject": "Test Email",
  "body": "<h1>Hello World</h1>",
  "isHtml": true,
  "from": "<EMAIL>",
  "cc": ["<EMAIL>"],
  "bcc": ["<EMAIL>"],
  "attachments": [
    {
      "fileName": "document.pdf",
      "content": "base64-encoded-content",
      "contentType": "application/pdf"
    }
  ]
}
```

**Response:**
```json
{
  "isSuccess": true,
  "messageId": "msg_123456789",
  "errorMessage": null,
  "metadata": {
    "provider": "SendGrid",
    "timestamp": "2024-01-15T10:30:00Z",
    "queueId": "queue_987654321"
  }
}
```

### Send Bulk Email

**Endpoint:** `POST /api/email/send/bulk`

**Request Body:**
```json
{
  "messages": [
    {
      "to": "<EMAIL>",
      "subject": "Bulk Email 1",
      "body": "Content for user 1",
      "isHtml": false
    },
    {
      "to": "<EMAIL>",
      "subject": "Bulk Email 2",
      "body": "Content for user 2",
      "isHtml": false
    }
  ]
}
```

**Response:**
```json
{
  "isSuccess": true,
  "results": [
    {
      "isSuccess": true,
      "messageId": "msg_123456789",
      "errorMessage": null
    },
    {
      "isSuccess": true,
      "messageId": "msg_123456790",
      "errorMessage": null
    }
  ],
  "errorMessage": null,
  "metadata": {
    "totalMessages": 2,
    "successCount": 2,
    "failureCount": 0,
    "correlationId": "corr_abc123"
  }
}
```

### Get Email Status

**Endpoint:** `GET /api/email/status/{messageId}`

**Response:**
```json
{
  "isSuccess": true,
  "messageId": "msg_123456789",
  "status": "delivered",
  "sentAt": "2024-01-15T10:30:00Z",
  "deliveredAt": "2024-01-15T10:31:15Z",
  "errorMessage": null,
  "metadata": {
    "provider": "SendGrid",
    "attempts": 1,
    "lastAttempt": "2024-01-15T10:30:00Z"
  }
}
```

### Get Email History

**Endpoint:** `GET /api/email/history?userId={userId}&page=1&pageSize=50`

**Query Parameters:**
- `userId` (optional): Filter by user ID
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Items per page (default: 50)

**Response:**
```json
{
  "isSuccess": true,
  "messages": [
    {
      "messageId": "msg_123456789",
      "content": "Email subject and preview",
      "status": "delivered",
      "sentAt": "2024-01-15T10:30:00Z",
      "deliveredAt": "2024-01-15T10:31:15Z",
      "errorMessage": null
    }
  ],
  "errorMessage": null,
  "totalCount": 150,
  "page": 1,
  "pageSize": 50,
  "totalPages": 3
}
```

## 📱 SMS API

### Send Single SMS

**Endpoint:** `POST /api/sms/send`

**Request Body:**
```json
{
  "phoneNumber": "+**********",
  "message": "Hello from NotificationService!"
}
```

**Response:**
```json
{
  "isSuccess": true,
  "messageId": "sms_123456789",
  "errorMessage": null,
  "metadata": {
    "provider": "Twilio",
    "timestamp": "2024-01-15T10:30:00Z",
    "cost": 0.0075,
    "segments": 1
  }
}
```

### Send Bulk SMS

**Endpoint:** `POST /api/sms/send/bulk`

**Request Body:**
```json
{
  "messages": [
    {
      "phoneNumber": "+**********",
      "message": "Bulk SMS message 1"
    },
    {
      "phoneNumber": "+**********",
      "message": "Bulk SMS message 2"
    }
  ]
}
```

**Response:**
```json
{
  "isSuccess": true,
  "results": [
    {
      "isSuccess": true,
      "messageId": "sms_123456789",
      "errorMessage": null
    },
    {
      "isSuccess": true,
      "messageId": "sms_123456790",
      "errorMessage": null
    }
  ],
  "errorMessage": null,
  "metadata": {
    "totalMessages": 2,
    "successCount": 2,
    "failureCount": 0,
    "totalCost": 0.015
  }
}
```

## 🔔 Push Notification API

### Send Single Push Notification

**Endpoint:** `POST /api/push/send`

**Request Body:**
```json
{
  "deviceToken": "device_token_123",
  "title": "Notification Title",
  "body": "Notification body content",
  "data": {
    "action": "open_screen",
    "screen": "home",
    "userId": "user123"
  }
}
```

**Response:**
```json
{
  "isSuccess": true,
  "messageId": "push_123456789",
  "errorMessage": null,
  "metadata": {
    "provider": "Firebase",
    "timestamp": "2024-01-15T10:30:00Z",
    "platform": "android"
  }
}
```

### Send Bulk Push Notifications

**Endpoint:** `POST /api/push/send/bulk`

**Request Body:**
```json
{
  "messages": [
    {
      "deviceToken": "device_token_123",
      "title": "Bulk Notification 1",
      "body": "Content for device 1",
      "data": {"userId": "user1"}
    },
    {
      "deviceToken": "device_token_456",
      "title": "Bulk Notification 2",
      "body": "Content for device 2",
      "data": {"userId": "user2"}
    }
  ]
}
```

## 🔧 Admin API

### List Plugins

**Endpoint:** `GET /api/admin/plugins`

**Response:**
```json
{
  "plugins": [
    {
      "name": "SendGrid Email Plugin",
      "version": "1.0.0",
      "type": "Email",
      "provider": "SendGrid",
      "isEnabled": true,
      "isLoaded": true,
      "configuration": {
        "apiKey": "configured",
        "fromEmail": "<EMAIL>"
      },
      "health": {
        "status": "healthy",
        "lastCheck": "2024-01-15T10:30:00Z"
      }
    }
  ]
}
```

### Upload Plugin

**Endpoint:** `POST /api/admin/plugins/upload`

**Request:** Multipart form data with plugin assembly file

**Response:**
```json
{
  "isSuccess": true,
  "pluginName": "New Email Plugin",
  "version": "1.0.0",
  "message": "Plugin uploaded and loaded successfully"
}
```

### Enable/Disable Plugin

**Endpoint:** `POST /api/admin/plugins/{pluginName}/enable`
**Endpoint:** `POST /api/admin/plugins/{pluginName}/disable`

**Response:**
```json
{
  "isSuccess": true,
  "pluginName": "SendGrid Email Plugin",
  "status": "enabled",
  "message": "Plugin enabled successfully"
}
```

## 📊 Metrics API

### Email Metrics Summary

**Endpoint:** `GET /api/email/metrics/summary`

**Response:**
```json
{
  "totalSent": 15420,
  "successRate": 98.5,
  "failureRate": 1.5,
  "averageResponseTime": 245,
  "last24Hours": {
    "sent": 1250,
    "delivered": 1230,
    "failed": 20
  },
  "topProviders": [
    {
      "name": "SendGrid",
      "percentage": 75.2,
      "successRate": 99.1
    },
    {
      "name": "SMTP",
      "percentage": 24.8,
      "successRate": 96.8
    }
  ]
}
```

### Detailed Metrics

**Endpoint:** `GET /api/email/metrics/detailed?from=2024-01-01&to=2024-01-15`

**Query Parameters:**
- `from` (optional): Start date (ISO 8601)
- `to` (optional): End date (ISO 8601)

**Response:**
```json
{
  "period": {
    "from": "2024-01-01T00:00:00Z",
    "to": "2024-01-15T23:59:59Z"
  },
  "hourlyBreakdown": [
    {
      "hour": "2024-01-15T10:00:00Z",
      "sent": 125,
      "delivered": 123,
      "failed": 2,
      "averageResponseTime": 230
    }
  ],
  "providerBreakdown": [
    {
      "provider": "SendGrid",
      "sent": 11565,
      "delivered": 11450,
      "failed": 115,
      "successRate": 99.0
    }
  ],
  "errorBreakdown": [
    {
      "errorType": "Invalid Email",
      "count": 45,
      "percentage": 2.1
    },
    {
      "errorType": "Rate Limited",
      "count": 12,
      "percentage": 0.6
    }
  ]
}
```

## 🏥 Health API

### Basic Health Check

**Endpoint:** `GET /api/health`

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "services": {
    "email": "available",
    "sms": "available",
    "push": "available"
  }
}
```

### Plugin Health Status

**Endpoint:** `GET /api/health/plugins`

**Response:**
```json
{
  "overallStatus": "healthy",
  "plugins": [
    {
      "name": "SendGrid Email Plugin",
      "status": "healthy",
      "lastCheck": "2024-01-15T10:30:00Z",
      "responseTime": 145,
      "details": {
        "apiConnectivity": "ok",
        "configuration": "valid",
        "lastSuccessfulSend": "2024-01-15T10:25:00Z"
      }
    }
  ]
}
```

### System Status

**Endpoint:** `GET /api/system/status`

**Response:**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "application": {
    "name": "NotifyMasterApi",
    "version": "1.0.0",
    "environment": "Production"
  },
  "redis": {
    "status": "connected",
    "isUsingFallback": false,
    "connectionString": "localhost:6379",
    "lastCheck": "2024-01-15T10:30:00Z"
  },
  "database": {
    "status": "connected",
    "isUsingFallback": false,
    "provider": "PostgreSQL",
    "lastCheck": "2024-01-15T10:30:00Z"
  },
  "fallbacks": {
    "redisUsingFallback": false,
    "databaseUsingFallback": false,
    "anyFallbackActive": false
  },
  "health": {
    "status": "Healthy",
    "message": "All systems operational"
  }
}
```

## ❌ Error Handling

### Error Response Format

All API endpoints return errors in a consistent format:

```json
{
  "isSuccess": false,
  "errorMessage": "Detailed error description",
  "errorCode": "EMAIL_INVALID_RECIPIENT",
  "timestamp": "2024-01-15T10:30:00Z",
  "correlationId": "corr_abc123",
  "details": {
    "field": "to",
    "value": "invalid-email",
    "constraint": "valid email format required"
  }
}
```

### HTTP Status Codes

- `200 OK`: Successful operation
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error
- `503 Service Unavailable`: Service temporarily unavailable

### Common Error Codes

**Email Errors:**
- `EMAIL_INVALID_RECIPIENT`: Invalid email address
- `EMAIL_PROVIDER_UNAVAILABLE`: Email provider is down
- `EMAIL_RATE_LIMITED`: Rate limit exceeded
- `EMAIL_ATTACHMENT_TOO_LARGE`: Attachment exceeds size limit

**SMS Errors:**
- `SMS_INVALID_PHONE`: Invalid phone number format
- `SMS_PROVIDER_UNAVAILABLE`: SMS provider is down
- `SMS_INSUFFICIENT_CREDITS`: Insufficient account credits
- `SMS_MESSAGE_TOO_LONG`: Message exceeds character limit

**Push Errors:**
- `PUSH_INVALID_TOKEN`: Invalid device token
- `PUSH_PROVIDER_UNAVAILABLE`: Push provider is down
- `PUSH_PAYLOAD_TOO_LARGE`: Payload exceeds size limit
- `PUSH_INVALID_CREDENTIALS`: Invalid provider credentials

**System Errors:**
- `PLUGIN_NOT_FOUND`: Requested plugin not available
- `PLUGIN_LOAD_FAILED`: Plugin failed to load
- `CONFIGURATION_INVALID`: Invalid configuration provided
- `QUEUE_FULL`: Message queue is full

## 🔄 Rate Limiting

### Rate Limit Headers

All responses include rate limiting information:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
X-RateLimit-Window: 3600
```

### Rate Limits by Endpoint

- **Email endpoints**: 1000 requests/hour
- **SMS endpoints**: 500 requests/hour  
- **Push endpoints**: 2000 requests/hour
- **Admin endpoints**: 100 requests/hour
- **Metrics endpoints**: 200 requests/hour

## 📝 Request/Response Examples

### cURL Examples

**Send Email:**
```bash
curl -X POST "https://api.example.com/api/email/send" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test Email",
    "body": "Hello World!",
    "isHtml": false
  }'
```

**Send SMS:**
```bash
curl -X POST "https://api.example.com/api/sms/send" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumber": "+**********",
    "message": "Hello from API!"
  }'
```

**Send Push Notification:**
```bash
curl -X POST "https://api.example.com/api/push/send" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "deviceToken": "device_token_123",
    "title": "Test Notification",
    "body": "Hello from API!",
    "data": {"action": "test"}
  }'
```

---

*For more examples and advanced usage, see the [Comprehensive Documentation](COMPREHENSIVE_DOCUMENTATION.md).*
