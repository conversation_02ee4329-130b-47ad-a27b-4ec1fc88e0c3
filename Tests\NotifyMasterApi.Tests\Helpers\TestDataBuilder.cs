using AutoFixture;
using EmailContract.Models;
using SmsContract.Models;
using PushNotificationContract.Models;
using PluginContract.Models;
using PluginContract.Enums;

namespace NotifyMasterApi.Tests.Helpers;

/// <summary>
/// Test data builder for creating test objects
/// </summary>
public static class TestDataBuilder
{
    private static readonly Fixture _fixture = new();

    public static SendEmailRequest CreateValidEmailRequest()
    {
        return new SendEmailRequest(
            ReceptorMail: "<EMAIL>",
            ReceptorName: "Test User",
            Subject: "Test Subject",
            Body: "Test email body content"
        );
    }

    public static SendEmailRequest CreateInvalidEmailRequest()
    {
        return new SendEmailRequest(
            ReceptorMail: "invalid-email",
            ReceptorName: "",
            Subject: "",
            Body: ""
        );
    }

    public static SendSmsRequest CreateValidSmsRequest()
    {
        return new SendSmsRequest(
            ReceptorPhoneNumber: "+1234567890",
            Message: "Test SMS message content"
        );
    }

    public static SendSmsRequest CreateInvalidSmsRequest()
    {
        return new SendSmsRequest(
            ReceptorPhoneNumber: "invalid-phone",
            Message: ""
        );
    }

    public static SendPushMessageRequest CreateValidPushRequest()
    {
        return new SendPushMessageRequest(
            DeviceToken: "valid-device-token-123",
            Title: "Test Notification",
            Message: "This is a test push notification"
        );
    }

    public static SendPushMessageRequest CreateInvalidPushRequest()
    {
        return new SendPushMessageRequest(
            DeviceToken: "",
            Title: "",
            Message: ""
        );
    }

    public static PluginInfo CreateSmsPluginInfo(string name = "Test SMS Plugin")
    {
        return new PluginInfo(
            Name: name,
            Version: "1.0.0",
            Description: "Test SMS plugin for unit testing",
            Type: PluginType.Sms,
            Author: "Test Author"
        );
    }

    public static PluginInfo CreateEmailPluginInfo(string name = "Test Email Plugin")
    {
        return new PluginInfo(
            Name: name,
            Version: "1.0.0",
            Description: "Test Email plugin for unit testing",
            Type: PluginType.Email,
            Author: "Test Author"
        );
    }

    public static PluginInfo CreatePushPluginInfo(string name = "Test Push Plugin")
    {
        return new PluginInfo(
            Name: name,
            Version: "1.0.0",
            Description: "Test Push plugin for unit testing",
            Type: PluginType.PushNotification,
            Author: "Test Author"
        );
    }

    public static PluginConfiguration CreatePluginConfiguration(string pluginName = "TestPlugin")
    {
        return new PluginConfiguration(
            PluginName: pluginName,
            Settings: new Dictionary<string, object>
            {
                { "ApiKey", "test-api-key-12345" },
                { "BaseUrl", "https://api.testprovider.com" },
                { "Timeout", 30 },
                { "RetryCount", 3 },
                { "EnableLogging", true }
            }
        );
    }

    public static List<PluginInfo> CreateMultiplePluginInfos(int count = 3)
    {
        var plugins = new List<PluginInfo>();
        var types = new[] { PluginType.Sms, PluginType.Email, PluginType.PushNotification };

        for (int i = 0; i < count; i++)
        {
            var type = types[i % types.Length];
            var plugin = type switch
            {
                PluginType.Sms => CreateSmsPluginInfo($"TestSmsPlugin{i + 1}"),
                PluginType.Email => CreateEmailPluginInfo($"TestEmailPlugin{i + 1}"),
                PluginType.PushNotification => CreatePushPluginInfo($"TestPushPlugin{i + 1}"),
                _ => CreateSmsPluginInfo($"TestPlugin{i + 1}")
            };
            plugins.Add(plugin);
        }

        return plugins;
    }

    public static T CreateRandom<T>() where T : class
    {
        return _fixture.Create<T>();
    }

    public static List<T> CreateRandomList<T>(int count = 5) where T : class
    {
        return _fixture.CreateMany<T>(count).ToList();
    }
}
