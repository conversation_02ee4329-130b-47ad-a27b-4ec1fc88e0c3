# API Documentation

This document provides comprehensive documentation for the NotificationService REST API.

## Base URL

- **Development**: `https://localhost:7001`
- **Production**: `https://your-domain.com`

## Authentication

Currently, the API does not require authentication. In production environments, consider implementing:
- API Key authentication
- JWT Bearer tokens
- OAuth 2.0

## Content Type

All API endpoints accept and return JSON data:
```
Content-Type: application/json
```

## Error Handling

The API uses standard HTTP status codes and returns error details in a consistent format:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "One or more validation errors occurred.",
    "details": [
      {
        "field": "to",
        "message": "The email address is not valid."
      }
    ]
  }
}
```

### HTTP Status Codes

- `200 OK` - Request successful
- `400 Bad Request` - Invalid request data
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server error
- `503 Service Unavailable` - Service temporarily unavailable

## Email API

### Send Email

Send an email notification through the configured email providers.

**Endpoint**: `POST /api/v1/email/send`

**Request Body**:
```json
{
  "to": "<EMAIL>",
  "subject": "Email Subject",
  "body": "Email body content",
  "isHtml": false,
  "cc": "<EMAIL>",
  "bcc": "<EMAIL>",
  "replyTo": "<EMAIL>",
  "attachments": [
    {
      "fileName": "document.pdf",
      "content": "base64-encoded-content",
      "contentType": "application/pdf"
    }
  ],
  "customHeaders": {
    "X-Priority": "High",
    "X-Custom-Header": "CustomValue"
  },
  "tags": ["newsletter", "marketing"],
  "metadata": {
    "campaignId": "12345",
    "userId": "user-123"
  }
}
```

**Request Fields**:

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `to` | string | Yes | Recipient email address |
| `subject` | string | Yes | Email subject line |
| `body` | string | Yes | Email body content |
| `isHtml` | boolean | No | Whether body contains HTML (default: false) |
| `cc` | string | No | Carbon copy recipient |
| `bcc` | string | No | Blind carbon copy recipient |
| `replyTo` | string | No | Reply-to email address |
| `attachments` | array | No | Email attachments |
| `customHeaders` | object | No | Custom email headers |
| `tags` | array | No | Tags for categorization |
| `metadata` | object | No | Additional metadata |

**Response**:
```json
{
  "success": true,
  "messageId": "msg_123456789",
  "providerId": "SendGrid",
  "timestamp": "2024-01-15T10:30:00Z",
  "metadata": {
    "processingTimeMs": 150,
    "queuePosition": 1
  }
}
```

**Example cURL**:
```bash
curl -X POST https://localhost:7001/api/v1/email/send \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Welcome!",
    "body": "Welcome to our service!",
    "isHtml": false
  }'
```

## SMS API

### Send SMS

Send an SMS notification through the configured SMS providers.

**Endpoint**: `POST /api/v1/sms/send`

**Request Body**:
```json
{
  "phoneNumber": "+**********",
  "message": "Your verification code is 123456",
  "senderId": "YourApp",
  "metadata": {
    "userId": "user-123",
    "type": "verification"
  }
}
```

**Request Fields**:

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `phoneNumber` | string | Yes | Recipient phone number (E.164 format) |
| `message` | string | Yes | SMS message content |
| `senderId` | string | No | Sender ID or short code |
| `metadata` | object | No | Additional metadata |

**Response**:
```json
{
  "success": true,
  "messageId": "sms_123456789",
  "providerId": "Twilio",
  "timestamp": "2024-01-15T10:30:00Z",
  "metadata": {
    "processingTimeMs": 200,
    "segments": 1
  }
}
```

**Example cURL**:
```bash
curl -X POST https://localhost:7001/api/v1/sms/send \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumber": "+**********",
    "message": "Your verification code is 123456"
  }'
```

## Push Notification API

### Send Push Notification

Send a push notification through the configured push notification providers.

**Endpoint**: `POST /api/v1/push/send`

**Request Body**:
```json
{
  "deviceToken": "device-token-here",
  "title": "Notification Title",
  "body": "Notification body content",
  "data": {
    "messageId": "12345",
    "type": "chat",
    "url": "https://example.com/message/12345"
  },
  "priority": "High",
  "badge": 1,
  "sound": "default",
  "metadata": {
    "userId": "user-123",
    "campaignId": "campaign-456"
  }
}
```

**Request Fields**:

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `deviceToken` | string | Yes | Device registration token |
| `title` | string | Yes | Notification title |
| `body` | string | Yes | Notification body |
| `data` | object | No | Custom data payload |
| `priority` | string | No | Notification priority (High, Normal, Low) |
| `badge` | integer | No | Badge count for iOS |
| `sound` | string | No | Notification sound |
| `metadata` | object | No | Additional metadata |

**Response**:
```json
{
  "success": true,
  "messageId": "push_123456789",
  "providerId": "Firebase",
  "timestamp": "2024-01-15T10:30:00Z",
  "metadata": {
    "processingTimeMs": 100
  }
}
```

**Example cURL**:
```bash
curl -X POST https://localhost:7001/api/v1/push/send \
  -H "Content-Type: application/json" \
  -d '{
    "deviceToken": "device-token-here",
    "title": "New Message",
    "body": "You have a new message"
  }'
```

## Admin API

### Health Check

Check the health status of the service and its dependencies.

**Endpoint**: `GET /api/v1/admin/health`

**Response**:
```json
{
  "status": "Healthy",
  "totalDuration": "00:00:00.0123456",
  "entries": {
    "database": {
      "status": "Healthy",
      "duration": "00:00:00.0050000",
      "data": {
        "connectionString": "Host=localhost;Database=NotifyMasterApi"
      }
    },
    "redis": {
      "status": "Healthy",
      "duration": "00:00:00.0030000"
    },
    "plugins": {
      "status": "Healthy",
      "duration": "00:00:00.0020000",
      "data": {
        "loadedPlugins": 3,
        "enabledPlugins": 2
      }
    }
  }
}
```

### Service Metrics

Get service performance metrics and statistics.

**Endpoint**: `GET /api/v1/admin/metrics`

**Response**:
```json
{
  "notifications": {
    "totalSent": 12345,
    "successRate": 98.5,
    "averageProcessingTime": 150,
    "byType": {
      "email": {
        "sent": 8000,
        "failed": 120,
        "successRate": 98.5
      },
      "sms": {
        "sent": 3000,
        "failed": 45,
        "successRate": 98.5
      },
      "push": {
        "sent": 1345,
        "failed": 20,
        "successRate": 98.5
      }
    }
  },
  "plugins": {
    "totalLoaded": 5,
    "totalEnabled": 3,
    "byProvider": {
      "SendGrid": {
        "status": "Enabled",
        "successRate": 99.2,
        "averageResponseTime": 120
      },
      "Twilio": {
        "status": "Enabled",
        "successRate": 98.8,
        "averageResponseTime": 200
      }
    }
  },
  "system": {
    "uptime": "2.15:30:45",
    "memoryUsage": "256MB",
    "cpuUsage": "15%"
  }
}
```

### Plugin Management

#### List Plugins

Get a list of all available plugins and their status.

**Endpoint**: `GET /api/v1/admin/plugins`

**Response**:
```json
{
  "plugins": [
    {
      "name": "SendGrid",
      "version": "1.0.0",
      "type": "Email",
      "status": "Enabled",
      "health": "Healthy",
      "description": "SendGrid email provider",
      "author": "NotificationService Team",
      "loadedAt": "2024-01-15T09:00:00Z"
    },
    {
      "name": "Twilio",
      "version": "1.0.0",
      "type": "SMS",
      "status": "Enabled",
      "health": "Healthy",
      "description": "Twilio SMS provider",
      "author": "NotificationService Team",
      "loadedAt": "2024-01-15T09:00:00Z"
    }
  ]
}
```

#### Upload Plugin

Upload and install a new plugin.

**Endpoint**: `POST /api/v1/admin/plugins/upload`

**Request**: Multipart form data with plugin file

**Response**:
```json
{
  "success": true,
  "message": "Plugin uploaded and installed successfully",
  "plugin": {
    "name": "CustomProvider",
    "version": "1.0.0",
    "type": "Email",
    "status": "Disabled"
  }
}
```

#### Remove Plugin

Remove an installed plugin.

**Endpoint**: `DELETE /api/v1/admin/plugins/{name}`

**Response**:
```json
{
  "success": true,
  "message": "Plugin removed successfully"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Default**: 100 requests per minute per IP
- **Burst**: Up to 20 requests in a 10-second window
- **Headers**: Rate limit information is returned in response headers

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## Webhooks

Configure webhooks to receive notifications about delivery status:

```json
{
  "webhookUrl": "https://your-app.com/webhooks/notifications",
  "events": ["delivered", "failed", "bounced"],
  "secret": "your-webhook-secret"
}
```

## SDKs and Libraries

Official SDKs are available for:
- .NET/C#
- JavaScript/Node.js
- Python
- PHP
- Java

## Support

For API support and questions:
- Documentation: [https://docs.notificationservice.com](https://docs.notificationservice.com)
- GitHub Issues: [https://github.com/bloodchild8906/NotificationService-master/issues](https://github.com/bloodchild8906/NotificationService-master/issues)
- Email: <EMAIL>
