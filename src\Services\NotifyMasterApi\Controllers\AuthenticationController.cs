using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NotifyMasterApi.Authentication;
using NotifyMasterApi.Documentation;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace NotifyMasterApi.Controllers;

#region Request/Response Models

/// <summary>
/// Login request model for username/password authentication.
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// Username for authentication
    /// </summary>
    /// <example>admin</example>
    [Required(ErrorMessage = "Username is required")]
    [Description("Username for authentication")]
    public string Username { get; set; } = "";

    /// <summary>
    /// Password for authentication
    /// </summary>
    /// <example>admin123</example>
    [Required(ErrorMessage = "Password is required")]
    [Description("Password for authentication")]
    public string Password { get; set; } = "";
}

/// <summary>
/// Login response containing JWT token and user information.
/// </summary>
public class LoginResponse
{
    /// <summary>
    /// JWT access token
    /// </summary>
    [Description("JWT access token for API authentication")]
    public string Token { get; set; } = "";

    /// <summary>
    /// Refresh token for obtaining new access tokens
    /// </summary>
    [Description("Refresh token for obtaining new access tokens")]
    public string RefreshToken { get; set; } = "";

    /// <summary>
    /// Token expiration timestamp
    /// </summary>
    [Description("When the access token expires")]
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// Authenticated user information
    /// </summary>
    [Description("Information about the authenticated user")]
    public UserInfo User { get; set; } = new();
}

/// <summary>
/// User information model.
/// </summary>
public class UserInfo
{
    /// <summary>
    /// Username
    /// </summary>
    [Description("User's username")]
    public string Username { get; set; } = "";

    /// <summary>
    /// User roles
    /// </summary>
    [Description("List of roles assigned to the user")]
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// User permissions/claims
    /// </summary>
    [Description("List of permissions/claims for the user")]
    public List<string> Claims { get; set; } = new();
}

/// <summary>
/// Token refresh request model.
/// </summary>
public class RefreshTokenRequest
{
    /// <summary>
    /// Refresh token
    /// </summary>
    /// <example>refresh_token_here</example>
    [Required(ErrorMessage = "Refresh token is required")]
    [Description("Valid refresh token")]
    public string RefreshToken { get; set; } = "";
}

/// <summary>
/// API key creation request model.
/// </summary>
public class CreateApiKeyRequest
{
    /// <summary>
    /// Name/description for the API key
    /// </summary>
    /// <example>My Application Key</example>
    [Required(ErrorMessage = "Name is required")]
    [Description("Descriptive name for the API key")]
    public string Name { get; set; } = "";

    /// <summary>
    /// Roles to assign to the API key
    /// </summary>
    /// <example>["User"]</example>
    [Description("List of roles to assign to the API key")]
    public List<string> Roles { get; set; } = new() { "User" };

    /// <summary>
    /// Optional expiration date for the API key
    /// </summary>
    [Description("Optional expiration date for the API key")]
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// API key information model.
/// </summary>
public class ApiKeyInfo
{
    /// <summary>
    /// API key identifier
    /// </summary>
    [Description("Unique identifier for the API key")]
    public string Id { get; set; } = "";

    /// <summary>
    /// API key name/description
    /// </summary>
    [Description("Descriptive name for the API key")]
    public string Name { get; set; } = "";

    /// <summary>
    /// The actual API key (only shown once during creation)
    /// </summary>
    [Description("The actual API key value (only shown during creation)")]
    public string? Key { get; set; }

    /// <summary>
    /// Roles assigned to the API key
    /// </summary>
    [Description("List of roles assigned to the API key")]
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// When the API key was created
    /// </summary>
    [Description("Creation timestamp")]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When the API key expires (if applicable)
    /// </summary>
    [Description("Expiration timestamp (if applicable)")]
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Whether the API key is currently active
    /// </summary>
    [Description("Whether the API key is currently active")]
    public bool IsActive { get; set; }
}

#endregion

/// <summary>
/// 🔐 Authentication & Authorization Management
/// </summary>
/// <remarks>
/// **Complete authentication solution supporting multiple methods:**
///
/// ### 🎯 Authentication Methods
/// - **JWT Tokens**: Secure, stateless authentication with configurable expiry
/// - **API Keys**: Long-lived keys for service-to-service communication
/// - **Basic Auth**: Simple username/password authentication
/// - **OAuth 2.0**: Third-party authentication (Google, Microsoft, GitHub, etc.)
///
/// ### 🔑 Default Credentials (Development)
/// - **Username**: `admin` | **Password**: `admin123`
/// - **API Key**: `notify-master-admin-key-12345`
///
/// ### 🛡️ Security Features
/// - Role-based access control (Admin, User roles)
/// - Claims-based authorization
/// - Secure password hashing with BCrypt
/// - Token refresh capabilities
/// - API key management and rotation
///
/// ### 📊 Usage Examples
/// ```bash
/// # Login with credentials
/// curl -X POST /api/authentication/login \
///   -H "Content-Type: application/json" \
///   -d '{"username":"admin","password":"admin123"}'
///
/// # Use JWT token
/// curl -H "Authorization: Bearer YOUR_JWT_TOKEN" /api/email/send
///
/// # Use API key
/// curl -H "X-API-Key: notify-master-admin-key-12345" /api/sms/send
/// ```
/// </remarks>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[Tags("🔐 Authentication")]
public class AuthenticationController : ControllerBase
{
    private readonly IJwtAuthenticationService _jwtService;
    private readonly IApiKeyService _apiKeyService;
    private readonly IUserService _userService;
    private readonly ILogger<AuthenticationController> _logger;

    public AuthenticationController(
        IJwtAuthenticationService jwtService,
        IApiKeyService apiKeyService,
        IUserService userService,
        ILogger<AuthenticationController> logger)
    {
        _jwtService = jwtService;
        _apiKeyService = apiKeyService;
        _userService = userService;
        _logger = logger;
    }

    /// <summary>
    /// 🔑 User Login - Get JWT Token
    /// </summary>
    /// <remarks>
    /// **Authenticate with username and password to receive a JWT token.**
    ///
    /// ### 🎯 Default Test Credentials
    /// - **Username**: `admin`
    /// - **Password**: `admin123`
    ///
    /// ### 📝 Request Example
    /// ```json
    /// {
    ///   "username": "admin",
    ///   "password": "admin123"
    /// }
    /// ```
    ///
    /// ### ✅ Success Response
    /// ```json
    /// {
    ///   "success": true,
    ///   "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    ///   "refreshToken": "refresh_token_here",
    ///   "expiresAt": "2024-01-01T12:00:00Z",
    ///   "user": {
    ///     "username": "admin",
    ///     "roles": ["Admin", "User"]
    ///   }
    /// }
    /// ```
    ///
    /// ### ❌ Error Response
    /// ```json
    /// {
    ///   "success": false,
    ///   "message": "Invalid username or password"
    /// }
    /// ```
    ///
    /// ### 🔧 Usage
    /// 1. Send POST request with credentials
    /// 2. Copy the returned `token`
    /// 3. Use in Authorization header: `Bearer {token}`
    /// 4. Token expires in 24 hours (configurable)
    /// </remarks>
    /// <param name="request">Login credentials containing username and password</param>
    /// <returns>JWT token and user information if authentication successful</returns>
    /// <response code="200">Login successful - returns JWT token and user info</response>
    /// <response code="400">Invalid request - missing username or password</response>
    /// <response code="401">Authentication failed - invalid credentials</response>
    [HttpPost("login")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ApiResponse<LoginResponse>), 200)]
    [ProducesResponseType(typeof(ApiResponse<object>), 400)]
    [ProducesResponseType(typeof(ApiResponse<object>), 401)]
    [ApiExample("""
    {
      "username": "admin",
      "password": "admin123"
    }
    """, "Login with default admin credentials")]
    [ApiResponseExample(200, """
    {
      "success": true,
      "message": "Login successful",
      "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "refresh_token_here",
        "expiresAt": "2024-01-01T12:00:00Z",
        "user": {
          "username": "admin",
          "roles": ["Admin", "User"]
        }
      },
      "requestId": "12345-67890",
      "timestamp": "2024-01-01T10:00:00Z"
    }
    """, "Successful login response")]
    [ApiPerformance("< 100ms", "100 requests/minute per IP")]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
        {
            return BadRequest(new { message = "Username and password are required" });
        }

        var result = await _jwtService.AuthenticateAsync(request.Username, request.Password);
        
        if (result.IsSuccess)
        {
            return Ok(new
            {
                token = result.Token,
                user = new
                {
                    id = result.User!.Id,
                    username = result.User.Username,
                    email = result.User.Email,
                    roles = result.User.Roles,
                    permissions = result.User.Permissions
                },
                expiresAt = DateTime.UtcNow.AddHours(24)
            });
        }

        return Unauthorized(new { message = result.ErrorMessage });
    }

    /// <summary>
    /// Refresh JWT token.
    /// </summary>
    [HttpPost("refresh")]
    [Authorize]
    public async Task<IActionResult> RefreshToken()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var username = User.FindFirst(ClaimTypes.Name)?.Value;

        if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(username))
        {
            return Unauthorized(new { message = "Invalid token" });
        }

        var user = await _userService.GetUserByIdAsync(userId);
        if (user == null || !user.IsActive)
        {
            return Unauthorized(new { message = "User not found or inactive" });
        }

        var token = _jwtService.GenerateToken(user.Id, user.Username, user.Roles, user.Permissions);

        return Ok(new
        {
            token,
            expiresAt = DateTime.UtcNow.AddHours(24)
        });
    }

    /// <summary>
    /// Get current user information.
    /// </summary>
    [HttpGet("me")]
    [Authorize]
    public async Task<IActionResult> GetCurrentUser()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized(new { message = "Invalid token" });
        }

        var user = await _userService.GetUserByIdAsync(userId);
        if (user == null)
        {
            return NotFound(new { message = "User not found" });
        }

        return Ok(new
        {
            id = user.Id,
            username = user.Username,
            email = user.Email,
            roles = user.Roles,
            permissions = user.Permissions,
            createdAt = user.CreatedAt,
            lastLoginAt = user.LastLoginAt
        });
    }

    /// <summary>
    /// Create a new API key.
    /// </summary>
    [HttpPost("api-keys")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> CreateApiKey([FromBody] CreateApiKeyRequest request)
    {
        if (string.IsNullOrEmpty(request.Name))
        {
            return BadRequest(new { message = "API key name is required" });
        }

        var apiKey = await _apiKeyService.CreateApiKeyAsync(
            request.Name,
            request.Roles ?? Array.Empty<string>(),
            request.Permissions ?? Array.Empty<string>(),
            request.ExpiresAt
        );

        return Ok(new
        {
            id = apiKey.Id,
            name = apiKey.Name,
            roles = apiKey.Roles,
            permissions = apiKey.Permissions,
            createdAt = apiKey.CreatedAt,
            expiresAt = apiKey.ExpiresAt,
            message = "API key created successfully. Make sure to save it as it won't be shown again."
        });
    }

    /// <summary>
    /// Get all API keys.
    /// </summary>
    [HttpGet("api-keys")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetApiKeys()
    {
        var apiKeys = await _apiKeyService.GetApiKeysAsync();
        
        return Ok(apiKeys.Select(key => new
        {
            id = key.Id,
            name = key.Name,
            roles = key.Roles,
            permissions = key.Permissions,
            createdAt = key.CreatedAt,
            expiresAt = key.ExpiresAt,
            isActive = key.IsActive
        }));
    }

    /// <summary>
    /// Revoke an API key.
    /// </summary>
    [HttpDelete("api-keys/{keyId}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> RevokeApiKey(string keyId)
    {
        var success = await _apiKeyService.RevokeApiKeyAsync(keyId);
        
        if (success)
        {
            return Ok(new { message = "API key revoked successfully" });
        }

        return NotFound(new { message = "API key not found" });
    }

    /// <summary>
    /// OAuth login redirect.
    /// </summary>
    [HttpGet("oauth/login")]
    public IActionResult OAuthLogin(string provider = "OAuth", string returnUrl = "/")
    {
        var redirectUrl = Url.Action(nameof(OAuthCallback), new { returnUrl });
        var properties = new AuthenticationProperties { RedirectUri = redirectUrl };
        
        return Challenge(properties, provider);
    }

    /// <summary>
    /// OAuth callback handler.
    /// </summary>
    [HttpGet("oauth/callback")]
    public async Task<IActionResult> OAuthCallback(string returnUrl = "/")
    {
        var result = await HttpContext.AuthenticateAsync("OAuth");
        
        if (result.Succeeded)
        {
            // Generate JWT token for OAuth user
            var username = result.Principal.Identity?.Name ?? "";
            var userId = result.Principal.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "";
            
            var user = await _userService.GetUserByUsernameAsync(username);
            if (user != null)
            {
                var token = _jwtService.GenerateToken(user.Id, user.Username, user.Roles, user.Permissions);
                
                // Return token or redirect with token
                return Ok(new
                {
                    token,
                    user = new
                    {
                        id = user.Id,
                        username = user.Username,
                        email = user.Email,
                        roles = user.Roles,
                        permissions = user.Permissions
                    }
                });
            }
        }

        return Unauthorized(new { message = "OAuth authentication failed" });
    }

    /// <summary>
    /// Logout (for OAuth).
    /// </summary>
    [HttpPost("logout")]
    public async Task<IActionResult> Logout()
    {
        await HttpContext.SignOutAsync();
        return Ok(new { message = "Logged out successfully" });
    }
}

/// <summary>
/// Login request model.
/// </summary>
public class LoginRequest
{
    public string Username { get; set; } = "";
    public string Password { get; set; } = "";
}

/// <summary>
/// Create API key request model.
/// </summary>
public class CreateApiKeyRequest
{
    public string Name { get; set; } = "";
    public string[]? Roles { get; set; }
    public string[]? Permissions { get; set; }
    public DateTime? ExpiresAt { get; set; }
}
