namespace NotificationContract.Models;

/// <summary>
///  Model for representing a request to send an email
/// </summary>
/// <param name="To"> Email address of the recipient </param>
/// <param name="Subject"> Subject of the email </param>
/// <param name="Body"> Body of the email </param>
/// <param name="IsHtml"> Whether the body is HTML </param>
/// <param name="From"> Email address of the sender </param>
/// <param name="Cc"> Carbon copy recipients </param>
/// <param name="Bcc"> Blind carbon copy recipients </param>
/// <param name="Attachments"> Attachments to the email </param>
public class EmailMessageRequest
{
    public string To { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public bool IsHtml { get; set; } = true;
    public string? From { get; set; }
    public List<string>? Cc { get; set; }
    public List<string>? Bcc { get; set; }
    public List<EmailAttachment>? Attachments { get; set; }
}
/// <summary>
///  Model for representing an email attachment
/// </summary>
/// <param name="FileName"> Name of the attachment </param>
/// <param name="Content"> Content of the attachment </param>
/// <param name="ContentType"> Content type of the attachment </param>
public class EmailAttachment
{
    public string FileName { get; set; } = string.Empty;
    public byte[] Content { get; set; } = Array.Empty<byte>();
    public string ContentType { get; set; } = string.Empty;
}

public class BulkEmailRequest
{
    public List<EmailMessageRequest> Messages { get; set; } = new();
}

public class EmailResponse
{
    public bool IsSuccess { get; set; }
    public string? MessageId { get; set; }
    public string? ErrorMessage { get; set; }

    public EmailResponse(bool isSuccess, string? errorMessage = null, string? messageId = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
        MessageId = messageId;
    }
}

public class BulkEmailResponse
{
    public bool IsSuccess { get; set; }
    public List<EmailResponse> Results { get; set; } = new();
    public string? ErrorMessage { get; set; }

    public BulkEmailResponse(bool isSuccess, string? errorMessage = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
    }
}

// SMS Gateway Models
public class SmsMessageRequest
{
    public string PhoneNumber { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
}

public class BulkSmsRequest
{
    public List<SmsMessageRequest> Messages { get; set; } = new();
}

public class SmsResponse
{
    public bool IsSuccess { get; set; }
    public string? MessageId { get; set; }
    public string? ErrorMessage { get; set; }

    public SmsResponse(bool isSuccess, string? errorMessage = null, string? messageId = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
        MessageId = messageId;
    }
}

public class BulkSmsResponse
{
    public bool IsSuccess { get; set; }
    public List<SmsResponse> Results { get; set; } = new();
    public string? ErrorMessage { get; set; }

    public BulkSmsResponse(bool isSuccess, string? errorMessage = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
    }
}

// Push Gateway Models
public class PushMessageRequest
{
    public string DeviceToken { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public Dictionary<string, string>? Data { get; set; }
}

public class BulkPushRequest
{
    public List<PushMessageRequest> Messages { get; set; } = new();
}

public class PushResponse
{
    public bool IsSuccess { get; set; }
    public string? MessageId { get; set; }
    public string? ErrorMessage { get; set; }

    public PushResponse(bool isSuccess, string? errorMessage = null, string? messageId = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
        MessageId = messageId;
    }
}

public class BulkPushResponse
{
    public bool IsSuccess { get; set; }
    public List<PushResponse> Results { get; set; } = new();
    public string? ErrorMessage { get; set; }

    public BulkPushResponse(bool isSuccess, string? errorMessage = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
    }
}

// Common Service Models
public class ServiceResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public object? Data { get; set; }
    public int PurgedCount { get; set; }
}

public class MessageStatusResponse
{
    public bool IsSuccess { get; set; }
    public string? MessageId { get; set; }
    public string? Status { get; set; }
    public DateTime? SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class MessageHistoryResponse
{
    public bool IsSuccess { get; set; }
    public List<MessageHistoryItem> Messages { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public int TotalCount { get; set; }
}

public class MessageHistoryItem
{
    public string MessageId { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public string? ErrorMessage { get; set; }
}
