namespace SmsContract.Models;
/// <summary>
///  Model for representing a response to sending an SMS
/// </summary>
/// <remarks>
/// This model represents the response to sending an SMS, including:
/// - Status of the request
/// - Message id of the SMS
/// - Error message if the request was not successful
/// - Response data if the request was successful
/// </remarks>
/// <param name="providesStatus"> Status of the request </param>
/// <param name="errorMessage"> Error message if the request was not successful </param>
/// <param name="messageId"> Message id of the SMS </param>
/// <param name="responseData"> Response data if the request was successful </param>
public sealed record SendSmsResponse(string providesStatus, string? errorMessage = null, string? messageId = null, Dictionary<string, object>? responseData = null);