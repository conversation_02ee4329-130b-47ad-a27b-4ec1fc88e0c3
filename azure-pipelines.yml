# Azure DevOps Pipeline for NotificationService
# Supports multi-environment deployment with fallback systems

trigger:
  branches:
    include:
    - main
    - master
    - develop
    - feature/*
  paths:
    exclude:
    - docs/*
    - README.md
    - .gitignore

pr:
  branches:
    include:
    - main
    - master
    - develop

variables:
  # Build Configuration
  buildConfiguration: 'Release'
  dotNetFramework: 'net9.0'
  dotNetVersion: '9.0.x'
  
  # Application Settings
  appName: 'notifymaster-api'
  containerRegistry: 'notifymasterregistry.azurecr.io'
  imageName: '$(appName)'
  imageTag: '$(Build.BuildId)'
  
  # Azure Resources
  resourceGroupName: 'NotifyMaster-RG'
  appServicePlan: 'NotifyMaster-Plan'
  
  # Environment-specific variables
  ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/main') }}:
    environmentName: 'production'
    azureSubscription: 'NotifyMaster-Production'
    appServiceName: 'notifymaster-api-prod'
    redisConnectionString: '$(REDIS_CONNECTION_PROD)'
    databaseConnectionString: '$(DATABASE_CONNECTION_PROD)'
  ${{ elseif eq(variables['Build.SourceBranch'], 'refs/heads/develop') }}:
    environmentName: 'staging'
    azureSubscription: 'NotifyMaster-Staging'
    appServiceName: 'notifymaster-api-staging'
    redisConnectionString: '$(REDIS_CONNECTION_STAGING)'
    databaseConnectionString: '$(DATABASE_CONNECTION_STAGING)'
  ${{ else }}:
    environmentName: 'development'
    azureSubscription: 'NotifyMaster-Development'
    appServiceName: 'notifymaster-api-dev'
    redisConnectionString: 'localhost:9999'  # Triggers fallback
    databaseConnectionString: ''  # Triggers fallback

stages:
- stage: Build
  displayName: 'Build and Test'
  jobs:
  - job: BuildJob
    displayName: 'Build Application'
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    - checkout: self
      fetchDepth: 0
    
    - task: UseDotNet@2
      displayName: 'Install .NET SDK'
      inputs:
        packageType: 'sdk'
        version: '$(dotNetVersion)'
        includePreviewVersions: false
    
    - task: DotNetCoreCLI@2
      displayName: 'Restore NuGet Packages'
      inputs:
        command: 'restore'
        projects: 'src/NotificationService.sln'
        feedsToUse: 'select'
        verbosityRestore: 'minimal'
    
    - task: DotNetCoreCLI@2
      displayName: 'Build Solution'
      inputs:
        command: 'build'
        projects: 'src/NotificationService.sln'
        arguments: '--configuration $(buildConfiguration) --no-restore'
    
    - task: DotNetCoreCLI@2
      displayName: 'Run Unit Tests'
      inputs:
        command: 'test'
        projects: 'Tests/**/*.csproj'
        arguments: '--configuration $(buildConfiguration) --no-build --collect:"XPlat Code Coverage" --logger trx --results-directory $(Agent.TempDirectory)'
        publishTestResults: true
    
    - task: PublishCodeCoverageResults@1
      displayName: 'Publish Code Coverage'
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: '$(Agent.TempDirectory)/**/coverage.cobertura.xml'
    
    - task: DotNetCoreCLI@2
      displayName: 'Publish Application'
      inputs:
        command: 'publish'
        projects: 'src/Services/NotifyMasterApi/NotifyMasterApi.csproj'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/app --no-build'
        publishWebProjects: false
        zipAfterPublish: false
    
    - task: Docker@2
      displayName: 'Build Docker Image'
      inputs:
        containerRegistry: '$(containerRegistry)'
        repository: '$(imageName)'
        command: 'build'
        Dockerfile: 'Dockerfile'
        tags: |
          $(imageTag)
          latest
        arguments: '--build-arg BUILD_CONFIGURATION=$(buildConfiguration)'
    
    - task: Docker@2
      displayName: 'Push Docker Image'
      inputs:
        containerRegistry: '$(containerRegistry)'
        repository: '$(imageName)'
        command: 'push'
        tags: |
          $(imageTag)
          latest
    
    - task: PublishBuildArtifacts@1
      displayName: 'Publish Build Artifacts'
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'

- stage: DeployDevelopment
  displayName: 'Deploy to Development'
  dependsOn: Build
  condition: and(succeeded(), or(eq(variables['Build.SourceBranch'], 'refs/heads/develop'), startsWith(variables['Build.SourceBranch'], 'refs/heads/feature/')))
  jobs:
  - deployment: DeployDev
    displayName: 'Deploy to Development Environment'
    pool:
      vmImage: 'ubuntu-latest'
    environment: 'development'
    strategy:
      runOnce:
        deploy:
          steps:
          - template: templates/deploy-steps.yml
            parameters:
              environmentName: 'development'
              azureSubscription: '$(azureSubscription)'
              appServiceName: '$(appServiceName)'
              containerImage: '$(containerRegistry)/$(imageName):$(imageTag)'
              redisConnectionString: '$(redisConnectionString)'
              databaseConnectionString: '$(databaseConnectionString)'

- stage: DeployStaging
  displayName: 'Deploy to Staging'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
  jobs:
  - deployment: DeployStaging
    displayName: 'Deploy to Staging Environment'
    pool:
      vmImage: 'ubuntu-latest'
    environment: 'staging'
    strategy:
      runOnce:
        deploy:
          steps:
          - template: templates/deploy-steps.yml
            parameters:
              environmentName: 'staging'
              azureSubscription: '$(azureSubscription)'
              appServiceName: '$(appServiceName)'
              containerImage: '$(containerRegistry)/$(imageName):$(imageTag)'
              redisConnectionString: '$(redisConnectionString)'
              databaseConnectionString: '$(databaseConnectionString)'

- stage: DeployProduction
  displayName: 'Deploy to Production'
  dependsOn: 
  - Build
  - DeployStaging
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: DeployProduction
    displayName: 'Deploy to Production Environment'
    pool:
      vmImage: 'ubuntu-latest'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - template: templates/deploy-steps.yml
            parameters:
              environmentName: 'production'
              azureSubscription: '$(azureSubscription)'
              appServiceName: '$(appServiceName)'
              containerImage: '$(containerRegistry)/$(imageName):$(imageTag)'
              redisConnectionString: '$(redisConnectionString)'
              databaseConnectionString: '$(databaseConnectionString)'
          
          - task: AzureCLI@2
            displayName: 'Run Production Health Checks'
            inputs:
              azureSubscription: '$(azureSubscription)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                echo "Running production health checks..."
                
                # Wait for app to start
                sleep 30
                
                # Get app URL
                APP_URL=$(az webapp show --name $(appServiceName) --resource-group $(resourceGroupName) --query defaultHostName -o tsv)
                
                # Health check
                HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$APP_URL/health/ready)
                if [ $HEALTH_STATUS -eq 200 ]; then
                  echo "✅ Health check passed"
                else
                  echo "❌ Health check failed with status: $HEALTH_STATUS"
                  exit 1
                fi
                
                # System status check
                SYSTEM_STATUS=$(curl -s https://$APP_URL/api/systemstatus | jq -r '.health.status')
                echo "System status: $SYSTEM_STATUS"
                
                # Check for fallbacks
                FALLBACKS=$(curl -s https://$APP_URL/api/systemstatus | jq -r '.fallbacks.anyFallbackActive')
                if [ "$FALLBACKS" = "true" ]; then
                  echo "⚠️  Warning: Some fallback systems are active"
                  curl -s https://$APP_URL/api/systemstatus/fallbacks | jq '.Summary'
                else
                  echo "✅ All systems operational - no fallbacks active"
                fi
