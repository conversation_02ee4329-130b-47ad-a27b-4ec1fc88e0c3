<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
        <PackageReference Include="xunit" Version="2.9.2" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="6.0.2">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Moq" Version="4.20.72" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\src\Libraries\SmsService.Library\SmsService.Library.csproj" />
        <ProjectReference Include="..\..\..\src\Contracts\SmsContract\SmsContract.csproj" />
    </ItemGroup>

</Project>
