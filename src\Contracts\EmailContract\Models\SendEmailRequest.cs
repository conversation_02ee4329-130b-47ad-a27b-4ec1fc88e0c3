namespace EmailContract.Models;
/// <summary>
///  Model for representing a request to send an email
/// </summary>
/// <param name="ReceptorMail"> Email address of the recipient </param>
/// <param name="ReceptorName"> Name of the recipient </param>
/// <param name="Subject"> Subject of the email </param>
/// <param name="Body"> Body of the email </param>
public sealed record SendEmailRequest(string ReceptorMail, string ReceptorName, string Subject, string Body);