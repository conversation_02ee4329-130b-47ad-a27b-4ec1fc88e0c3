<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FirebaseAdmin" Version="3.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Contracts\PushNotificationContract\PushNotificationContract.csproj" />
    <ProjectReference Include="..\..\Contracts\PluginContract\PluginContract.csproj" />
    <ProjectReference Include="..\..\Core\PluginCore\PluginCore.csproj" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="manifest.json" />
  </ItemGroup>

</Project>
