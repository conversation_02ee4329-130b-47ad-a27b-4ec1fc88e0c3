# 🚀 CI/CD Setup Guide

Complete guide for setting up CI/CD pipelines for the NotificationService using Azure DevOps Pipelines and GitHub Actions.

## 🎯 Overview

Both CI/CD solutions provide:
- ✅ **Multi-environment deployment** (Development, Staging, Production)
- ✅ **Fallback-aware health checks** - Validates fallback systems work correctly
- ✅ **Comprehensive testing** - Unit tests, integration tests, security scans
- ✅ **Docker containerization** - Consistent deployment across environments
- ✅ **Automated rollback** - On deployment failures
- ✅ **Performance monitoring** - Response time validation

## 🔧 Azure DevOps Pipeline Setup

### Prerequisites
- Azure DevOps organization
- Azure subscription with appropriate permissions
- Azure Container Registry
- Azure App Services for each environment

### 1. Create Azure Resources

```bash
# Create Resource Groups
az group create --name NotifyMaster-Dev-RG --location eastus
az group create --name NotifyMaster-Staging-RG --location eastus
az group create --name NotifyMaster-Prod-RG --location eastus

# Create Container Registry
az acr create --resource-group NotifyMaster-Prod-RG --name notifymasterregistry --sku Basic

# Create App Service Plans
az appservice plan create --name NotifyMaster-Dev-Plan --resource-group NotifyMaster-Dev-RG --sku B1 --is-linux
az appservice plan create --name NotifyMaster-Staging-Plan --resource-group NotifyMaster-Staging-RG --sku S1 --is-linux
az appservice plan create --name NotifyMaster-Prod-Plan --resource-group NotifyMaster-Prod-RG --sku P1V2 --is-linux

# Create App Services
az webapp create --resource-group NotifyMaster-Dev-RG --plan NotifyMaster-Dev-Plan --name notifymaster-api-dev --deployment-container-image-name nginx
az webapp create --resource-group NotifyMaster-Staging-RG --plan NotifyMaster-Staging-Plan --name notifymaster-api-staging --deployment-container-image-name nginx
az webapp create --resource-group NotifyMaster-Prod-RG --plan NotifyMaster-Prod-Plan --name notifymaster-api-prod --deployment-container-image-name nginx
```

### 2. Configure Service Connections

In Azure DevOps:
1. Go to **Project Settings** → **Service connections**
2. Create **Azure Resource Manager** connections for each environment:
   - `NotifyMaster-Development`
   - `NotifyMaster-Staging`
   - `NotifyMaster-Production`

### 3. Set Pipeline Variables

Create variable groups for each environment:

**NotifyMaster-Development**:
```yaml
REDIS_CONNECTION_DEV: "localhost:9999"  # Triggers fallback
DATABASE_CONNECTION_DEV: ""  # Triggers fallback
```

**NotifyMaster-Staging**:
```yaml
REDIS_CONNECTION_STAGING: "your-staging-redis-connection"
DATABASE_CONNECTION_STAGING: "your-staging-db-connection"
```

**NotifyMaster-Production**:
```yaml
REDIS_CONNECTION_PROD: "your-production-redis-connection"
DATABASE_CONNECTION_PROD: "your-production-db-connection"
```

### 4. Create Pipeline

1. In Azure DevOps, go to **Pipelines** → **New pipeline**
2. Select your repository
3. Choose **Existing Azure Pipelines YAML file**
4. Select `/azure-pipelines.yml`
5. Save and run

## 🐙 GitHub Actions Setup

### Prerequisites
- GitHub repository
- Azure subscription
- GitHub secrets configured

### 1. Configure GitHub Secrets

Go to **Settings** → **Secrets and variables** → **Actions** and add:

**Repository Secrets**:
```
AZURE_CREDENTIALS_DEV: {
  "clientId": "your-client-id",
  "clientSecret": "your-client-secret",
  "subscriptionId": "your-subscription-id",
  "tenantId": "your-tenant-id"
}
AZURE_CREDENTIALS_STAGING: { ... }
AZURE_CREDENTIALS_PROD: { ... }

REDIS_CONNECTION_STAGING: "your-staging-redis-connection"
DATABASE_CONNECTION_STAGING: "your-staging-db-connection"
REDIS_CONNECTION_PROD: "your-production-redis-connection"
DATABASE_CONNECTION_PROD: "your-production-db-connection"
```

### 2. Create Service Principal

```bash
# Create service principal for GitHub Actions
az ad sp create-for-rbac --name "NotifyMaster-GitHub-Actions" \
  --role contributor \
  --scopes /subscriptions/{subscription-id} \
  --sdk-auth
```

### 3. Configure Environments

In GitHub, go to **Settings** → **Environments** and create:
- `development` (no protection rules)
- `staging` (require reviewers)
- `production` (require reviewers + deployment branches)

## 🔄 Pipeline Features

### Multi-Environment Strategy

| Environment | Trigger | Infrastructure | Fallbacks | Purpose |
|-------------|---------|----------------|-----------|---------|
| **Development** | Feature branches | Minimal | All active | Testing fallbacks |
| **Staging** | Develop branch | Partial | Some active | Integration testing |
| **Production** | Main branch | Full | None | Live service |

### Fallback Testing Strategy

#### Development Environment
```yaml
# Intentionally trigger fallbacks for testing
CONNECTIONSTRINGS__REDIS: "localhost:9999"  # Invalid port
CONNECTIONSTRINGS__DEFAULTCONNECTION: ""    # Empty string
```

**Expected Results**:
- ✅ Application starts successfully
- ✅ All fallback systems active
- ✅ Health checks pass
- ✅ API functionality works

#### Staging Environment
```yaml
# Mix of real and fallback systems
CONNECTIONSTRINGS__REDIS: "real-redis-connection"
CONNECTIONSTRINGS__DEFAULTCONNECTION: ""  # Test DB fallback
```

#### Production Environment
```yaml
# Full infrastructure - no fallbacks
CONNECTIONSTRINGS__REDIS: "production-redis-cluster"
CONNECTIONSTRINGS__DEFAULTCONNECTION: "production-database"
```

**Expected Results**:
- ✅ No fallback systems active
- ✅ All infrastructure connected
- ✅ Performance benchmarks met

### Health Check Validation

Both pipelines validate:

1. **Liveness Check**: `/health/live` returns 200
2. **Readiness Check**: `/health/ready` returns 200 or 503 (with fallbacks)
3. **System Status**: `/api/systemstatus` accessible
4. **Fallback Status**: Validates expected fallback state
5. **API Functionality**: Swagger documentation accessible
6. **Plugin System**: Plugin endpoints responsive

### Performance Validation

Production deployments include:
- Response time checks (< 3 seconds)
- Memory usage monitoring
- CPU utilization checks
- Fallback performance comparison

## 🔧 Customization Options

### Environment-Specific Configuration

**Development**:
```yaml
# Focus on fallback testing
PLUGINSETTINGS__ENABLEHOTRELOAD: "true"
LOGGING__LOGLEVEL__DEFAULT: "Debug"
NOTIFICATIONSETTINGS__MAXCONCURRENTNOTIFICATIONS: "10"
```

**Staging**:
```yaml
# Production-like settings
PLUGINSETTINGS__ENABLEHOTRELOAD: "false"
LOGGING__LOGLEVEL__DEFAULT: "Information"
NOTIFICATIONSETTINGS__MAXCONCURRENTNOTIFICATIONS: "100"
```

**Production**:
```yaml
# Optimized for performance
PLUGINSETTINGS__ENABLEHOTRELOAD: "false"
LOGGING__LOGLEVEL__DEFAULT: "Warning"
NOTIFICATIONSETTINGS__MAXCONCURRENTNOTIFICATIONS: "1000"
```

### Custom Health Checks

Add custom validation steps:

```yaml
- name: Custom Business Logic Test
  run: |
    # Test specific business functionality
    curl -X POST $APP_URL/api/notifications/email \
      -H "Content-Type: application/json" \
      -d '{"to":"<EMAIL>","subject":"Test","body":"Test"}'
```

### Notification Integration

Add Slack/Teams notifications:

```yaml
- name: Notify Deployment Success
  if: success()
  uses: 8398a7/action-slack@v3
  with:
    status: success
    text: "✅ NotificationService deployed to ${{ inputs.environment }}"
  env:
    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
```

## 🚨 Troubleshooting

### Common Issues

**1. Health Checks Failing**
```bash
# Check application logs
az webapp log tail --name notifymaster-api-dev --resource-group NotifyMaster-Dev-RG

# Check system status
curl https://notifymaster-api-dev.azurewebsites.net/api/systemstatus
```

**2. Fallbacks Not Activating**
```bash
# Verify connection strings are invalid
echo "Redis: $CONNECTIONSTRINGS__REDIS"
echo "Database: $CONNECTIONSTRINGS__DEFAULTCONNECTION"

# Check fallback status
curl https://app-url/api/systemstatus/fallbacks
```

**3. Performance Issues**
```bash
# Check response times
time curl https://app-url/health/ready

# Monitor resource usage
az monitor metrics list --resource /subscriptions/{sub}/resourceGroups/{rg}/providers/Microsoft.Web/sites/{app}
```

### Debug Commands

```bash
# Test deployment locally
docker build -t notifymaster-api .
docker run -p 5120:5120 -e CONNECTIONSTRINGS__REDIS="localhost:9999" notifymaster-api

# Validate health endpoints
curl http://localhost:5120/health/live
curl http://localhost:5120/health/ready
curl http://localhost:5120/api/systemstatus
```

## 📊 Monitoring & Alerts

### Application Insights Integration

Both pipelines automatically configure Application Insights for production:

```yaml
- name: Setup Application Insights
  run: |
    # Create Application Insights
    az monitor app-insights component create \
      --app notifymaster-api-insights \
      --location "East US" \
      --resource-group NotifyMaster-Prod-RG
```

### Custom Alerts

Set up alerts for:
- Application downtime
- High response times
- Fallback system activation
- Error rate increases

## 🎉 Success Metrics

### Deployment Success Criteria

✅ **Build Stage**:
- All tests pass
- Code coverage > 80%
- Security scan passes
- Docker image builds successfully

✅ **Deployment Stage**:
- Application starts within 5 minutes
- Health checks pass
- Expected fallback state achieved
- Performance benchmarks met

✅ **Validation Stage**:
- API endpoints responsive
- System status reports correctly
- Fallback operations work
- Documentation accessible

**Your CI/CD pipeline is now ready to deploy the NotificationService with comprehensive fallback testing!** 🚀
