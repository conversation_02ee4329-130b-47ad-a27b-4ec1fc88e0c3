using FirebaseAdmin.Messaging;

namespace PushNotificationContract.Models;
/// <summary>
///  Model for representing a request to send a push notification
/// </summary>
/// <remarks>
/// This model represents the request to send a push notification, including:
/// - Device token of the recipient
/// - Title of the push notification
/// - Body of the push notification
/// </remarks>
/// <param name="DeviceToken"> Device token of the recipient </param>
/// <param name="Title"> Title of the push notification </param>
/// <param name="Message"> Body of the push notification </param>
public sealed record SendPushMessageRequest
{
    /// <summary>
    ///  Constructor for the push message request
    /// </summary>
    public SendPushMessageRequest(string DeviceToken, string Title, string Message)
    {
        this.DeviceToken = DeviceToken;
        this.Title = Title;
        this.Message = Message;
    }
    /// <summary>
    ///  Converts the notification request to a push message request
    /// </summary>
    /// <param name="notification"> Notification request to convert </param>
    public string DeviceToken { get; init; }
    /// <summary>
    ///  Title of the push notification
    /// </summary>
    public string Title { get; init; }
    /// <summary>
    ///  Body of the push notification
    /// </summary>
    public string Message { get; init; }
    /// <summary>
    ///  Deconstructs the push message request
    /// </summary>
    public void Deconstruct(out string DeviceToken, out string Title, out string Message)
    {
        DeviceToken = this.DeviceToken;
        Title = this.Title;
        Message = this.Message;
    }
    
    public static explicit operator Message (SendPushMessageRequest request)
        => new Message()
        {
            Token = request.DeviceToken,
            Notification = new Notification()
            {
                Title = request.Title,
                Body = request.Message
            }
        };
}

