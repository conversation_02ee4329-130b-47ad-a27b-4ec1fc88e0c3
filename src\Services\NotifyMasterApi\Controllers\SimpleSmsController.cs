using Microsoft.AspNetCore.Mvc;
using NotificationContract.Models;
using NotifyMasterApi.Gateways;

namespace NotifyMasterApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SimpleSmsController : ControllerBase
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<SimpleSmsController> _logger;

    public SimpleSmsController(ISmsGateway smsGateway, ILogger<SimpleSmsController> logger)
    {
        _smsGateway = smsGateway;
        _logger = logger;
    }

    [HttpPost("send")]
    public async Task<IActionResult> SendSms([FromBody] SmsMessageRequest request)
    {
        try
        {
            _logger.LogInformation("Sending SMS to {PhoneNumber}", request.PhoneNumber);
            
            var result = await _smsGateway.SendAsync(request);
            
            if (result.IsSuccess)
            {
                return Ok(new { success = true, messageId = result.MessageId });
            }
            
            return BadRequest(new { success = false, error = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending SMS");
            return StatusCode(500, new { success = false, error = "Internal server error" });
        }
    }

    [HttpPost("send-bulk")]
    public async Task<IActionResult> SendBulkSms([FromBody] BulkSmsRequest request)
    {
        try
        {
            _logger.LogInformation("Sending bulk SMS to {Count} recipients", request.Messages.Count());
            
            var result = await _smsGateway.SendBulkAsync(request);
            
            return Ok(new { 
                success = result.IsSuccess, 
                totalSent = result.Results.Count(r => r.IsSuccess),
                totalFailed = result.Results.Count(r => !r.IsSuccess),
                results = result.Results
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk SMS");
            return StatusCode(500, new { success = false, error = "Internal server error" });
        }
    }

    [HttpGet("providers")]
    public async Task<IActionResult> GetProviders()
    {
        try
        {
            var providers = await _smsGateway.GetProvidersAsync();
            return Ok(providers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS providers");
            return StatusCode(500, new { success = false, error = "Internal server error" });
        }
    }

    [HttpPost("test/{provider}")]
    public async Task<IActionResult> TestProvider(string provider, [FromQuery] string? testPhoneNumber = null)
    {
        try
        {
            var result = await _smsGateway.TestProviderAsync(provider, testPhoneNumber);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing SMS provider {Provider}", provider);
            return StatusCode(500, new { success = false, error = "Internal server error" });
        }
    }
}
