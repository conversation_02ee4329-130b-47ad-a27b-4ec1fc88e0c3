using Microsoft.AspNetCore.Mvc;
using NotificationContract.Models;
using NotifyMasterApi.Gateways;
using System.ComponentModel.DataAnnotations;
using System.Net;
using System.Text.RegularExpressions;

namespace NotifyMasterApi.Controllers;

/// <summary>
/// Provides SMS notification endpoints for sending single and bulk SMS messages through various providers.
/// </summary>
/// <remarks>
/// This controller handles SMS operations including sending individual messages, bulk SMS operations,
/// provider management, and testing functionality. All operations support multiple SMS providers
/// through the plugin architecture with international phone number support.
/// </remarks>
[ApiController]
[Route("api/v1/sms")]
[Produces("application/json")]
[Tags("SMS")]
public class SimpleSmsController : ControllerBase
{
    private readonly ISmsGateway _smsGateway;
    private readonly ILogger<SimpleSmsController> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="SimpleSmsController"/> class.
    /// </summary>
    /// <param name="smsGateway">The SMS gateway service for handling SMS operations</param>
    /// <param name="logger">The logger instance for this controller</param>
    /// <exception cref="ArgumentNullException">Thrown when smsGateway or logger is null</exception>
    public SimpleSmsController(ISmsGateway smsGateway, ILogger<SimpleSmsController> logger)
    {
        _smsGateway = smsGateway ?? throw new ArgumentNullException(nameof(smsGateway));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Sends a single SMS message through the configured SMS provider.
    /// </summary>
    /// <param name="request">The SMS message request containing phone number and message content</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A response indicating the success status and message ID if successful</returns>
    /// <response code="200">SMS was successfully sent or queued for delivery</response>
    /// <response code="400">Invalid request data or SMS sending failed</response>
    /// <response code="422">Request validation failed</response>
    /// <response code="500">Internal server error occurred</response>
    /// <example>
    /// POST /api/v1/sms/send
    /// {
    ///   "phoneNumber": "+**********",
    ///   "message": "Your verification code is 123456",
    ///   "senderId": "MyApp"
    /// }
    /// </example>
    [HttpPost("send")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ValidationProblemDetails), (int)HttpStatusCode.UnprocessableEntity)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> SendSms(
        [FromBody, Required] SmsMessageRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the request
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid SMS request received: {ValidationErrors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return UnprocessableEntity(ModelState);
            }

            // Additional phone number validation
            if (!IsValidPhoneNumber(request.PhoneNumber))
            {
                return BadRequest(new {
                    success = false,
                    error = "Invalid phone number format. Please use international format (e.g., +**********)"
                });
            }

            _logger.LogInformation("Sending SMS to {PhoneNumber} with message length {MessageLength}",
                request.PhoneNumber, request.Message?.Length ?? 0);

            var result = await _smsGateway.SendAsync(request);

            if (result.IsSuccess)
            {
                _logger.LogInformation("SMS successfully sent to {PhoneNumber}, MessageId: {MessageId}",
                    request.PhoneNumber, result.MessageId);

                return Ok(new {
                    success = true,
                    messageId = result.MessageId,
                    timestamp = DateTime.UtcNow,
                    recipient = request.PhoneNumber,
                    messageLength = request.Message?.Length ?? 0
                });
            }

            _logger.LogWarning("SMS sending failed for {PhoneNumber}: {Error}", request.PhoneNumber, result.ErrorMessage);
            return BadRequest(new {
                success = false,
                error = result.ErrorMessage,
                timestamp = DateTime.UtcNow,
                recipient = request.PhoneNumber
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument provided for SMS sending to {PhoneNumber}", request?.PhoneNumber);
            return BadRequest(new { success = false, error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending SMS to {PhoneNumber}", request?.PhoneNumber);
            return StatusCode(500, new {
                success = false,
                error = "An internal server error occurred while sending the SMS",
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Sends multiple SMS messages in a single batch operation.
    /// </summary>
    /// <param name="request">The bulk SMS request containing multiple SMS messages</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A response with overall status and individual results for each SMS</returns>
    /// <response code="200">Bulk operation completed (check individual results for per-SMS status)</response>
    /// <response code="400">Invalid request data</response>
    /// <response code="422">Request validation failed</response>
    /// <response code="500">Internal server error occurred</response>
    /// <example>
    /// POST /api/v1/sms/send-bulk
    /// {
    ///   "messages": [
    ///     {
    ///       "phoneNumber": "+**********",
    ///       "message": "Your verification code is 123456"
    ///     },
    ///     {
    ///       "phoneNumber": "+0987654321",
    ///       "message": "Your verification code is 654321"
    ///     }
    ///   ]
    /// }
    /// </example>
    [HttpPost("send-bulk")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ValidationProblemDetails), (int)HttpStatusCode.UnprocessableEntity)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> SendBulkSms(
        [FromBody, Required] BulkSmsRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the request
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid bulk SMS request received: {ValidationErrors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return UnprocessableEntity(ModelState);
            }

            var messageCount = request.Messages?.Count() ?? 0;
            if (messageCount == 0)
            {
                return BadRequest(new { success = false, error = "At least one SMS message is required" });
            }

            // Validate phone numbers in bulk request
            var invalidNumbers = request.Messages
                .Where(m => !IsValidPhoneNumber(m.PhoneNumber))
                .Select(m => m.PhoneNumber)
                .ToList();

            if (invalidNumbers.Any())
            {
                return BadRequest(new {
                    success = false,
                    error = "Invalid phone number format detected",
                    invalidNumbers = invalidNumbers.Take(5) // Limit to first 5 for response size
                });
            }

            _logger.LogInformation("Sending bulk SMS to {Count} recipients", messageCount);

            var result = await _smsGateway.SendBulkAsync(request);

            var successCount = result.Results.Count(r => r.IsSuccess);
            var failureCount = result.Results.Count(r => !r.IsSuccess);

            _logger.LogInformation("Bulk SMS operation completed: {SuccessCount} successful, {FailureCount} failed",
                successCount, failureCount);

            return Ok(new {
                success = result.IsSuccess,
                totalSent = successCount,
                totalFailed = failureCount,
                totalRequested = messageCount,
                timestamp = DateTime.UtcNow,
                results = result.Results.Select(r => new {
                    success = r.IsSuccess,
                    messageId = r.MessageId,
                    error = r.ErrorMessage
                })
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument provided for bulk SMS sending");
            return BadRequest(new { success = false, error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending bulk SMS");
            return StatusCode(500, new {
                success = false,
                error = "An internal server error occurred while sending bulk SMS messages",
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Retrieves a list of available SMS providers and their status.
    /// </summary>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A list of available SMS providers with their configuration status</returns>
    /// <response code="200">Successfully retrieved provider list</response>
    /// <response code="500">Internal server error occurred</response>
    /// <example>
    /// GET /api/v1/sms/providers
    /// </example>
    [HttpGet("providers")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> GetProviders(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Retrieving available SMS providers");

            var providers = await _smsGateway.GetProvidersAsync();

            _logger.LogInformation("Retrieved {Count} SMS providers", providers?.Count() ?? 0);

            return Ok(new {
                success = true,
                providers = providers,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error retrieving SMS providers");
            return StatusCode(500, new {
                success = false,
                error = "An internal server error occurred while retrieving SMS providers",
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Tests the configuration and connectivity of a specific SMS provider.
    /// </summary>
    /// <param name="provider">The name of the SMS provider to test</param>
    /// <param name="testPhoneNumber">Optional test phone number to send a test message to (in international format)</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>The result of the provider test including connectivity and configuration status</returns>
    /// <response code="200">Provider test completed (check result for success/failure details)</response>
    /// <response code="400">Invalid provider name or test parameters</response>
    /// <response code="404">Provider not found</response>
    /// <response code="500">Internal server error occurred</response>
    /// <example>
    /// POST /api/v1/sms/test/twilio?testPhoneNumber=+**********
    /// </example>
    [HttpPost("test/{provider}")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.NotFound)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> TestProvider(
        [FromRoute, Required] string provider,
        [FromQuery] string? testPhoneNumber = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate provider parameter
            if (string.IsNullOrWhiteSpace(provider))
            {
                return BadRequest(new { success = false, error = "Provider name is required" });
            }

            // Validate test phone number if provided
            if (!string.IsNullOrWhiteSpace(testPhoneNumber) && !IsValidPhoneNumber(testPhoneNumber))
            {
                return BadRequest(new {
                    success = false,
                    error = "Invalid test phone number format. Please use international format (e.g., +**********)"
                });
            }

            _logger.LogInformation("Testing SMS provider '{Provider}' with test phone number '{TestPhoneNumber}'",
                provider, testPhoneNumber ?? "none");

            var result = await _smsGateway.TestProviderAsync(provider, testPhoneNumber);

            _logger.LogInformation("SMS provider test completed for '{Provider}': {Success}",
                provider, result?.IsSuccess ?? false);

            return Ok(new {
                success = true,
                provider = provider,
                testPhoneNumber = testPhoneNumber,
                result = result,
                timestamp = DateTime.UtcNow
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument for testing SMS provider '{Provider}'", provider);
            return BadRequest(new { success = false, error = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Provider '{Provider}' not found or not available", provider);
            return NotFound(new {
                success = false,
                error = $"SMS provider '{provider}' not found or not available",
                provider = provider
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error testing SMS provider '{Provider}'", provider);
            return StatusCode(500, new {
                success = false,
                error = "An internal server error occurred while testing the SMS provider",
                provider = provider,
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Validates if the provided string is a valid international phone number format.
    /// </summary>
    /// <param name="phoneNumber">The phone number to validate</param>
    /// <returns>True if the phone number format is valid, false otherwise</returns>
    private static bool IsValidPhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // Basic international phone number validation
        // Should start with + followed by 1-3 digits for country code, then 4-15 digits
        var phoneRegex = new Regex(@"^\+[1-9]\d{1,3}\d{4,14}$");
        return phoneRegex.IsMatch(phoneNumber);
    }
}
