# ⚡ Quick Start Guide - 5 Minutes to Running

Get the NotificationService running in under 5 minutes with zero infrastructure setup required!

## 🚀 Super Quick Start (2 Minutes)

### Prerequisites
- **.NET 9.0 SDK** - [Download here](https://dotnet.microsoft.com/download/dotnet/9.0)

### 1. <PERSON>lone & Run
```bash
# Clone the repository
git clone https://github.com/bloodchild8906/NotificationService-master.git
cd NotificationService-master

# Build and run (uses fallback systems automatically)
dotnet run --project src/Services/NotifyMasterApi
```

### 2. Verify It's Working
```bash
# Check health (in another terminal)
curl http://localhost:5120/health/ready

# View system status
curl http://localhost:5120/api/systemstatus
```

### 3. Access the API
Open your browser: **http://localhost:5120/swagger**

**🎉 Done! The service is running with all fallback systems active.**

---

## 🔧 Quick Start with Infrastructure (5 Minutes)

### Prerequisites
- **.NET 9.0 SDK**
- **Docker** (for Redis & PostgreSQL)

### 1. Start Infrastructure
```bash
# Start Redis
docker run -d --name redis -p 6379:6379 redis:7-alpine

# Start PostgreSQL
docker run -d --name postgres \
  -e POSTGRES_DB=NotifyMaster \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 postgres:15
```

### 2. Configure Connection Strings
```bash
# Clone repository
git clone https://github.com/bloodchild8906/NotificationService-master.git
cd NotificationService-master

# Update appsettings.json (or use environment variables)
export CONNECTIONSTRINGS__REDIS="localhost:6379"
export CONNECTIONSTRINGS__DEFAULTCONNECTION="Host=localhost;Database=NotifyMaster;Username=postgres;Password=password"
```

### 3. Run the Service
```bash
dotnet run --project src/Services/NotifyMasterApi
```

**Expected Output:**
```
✅ Using preferred port 5120
✅ Database connection established successfully
✅ Redis connection established successfully
🚀 Starting NotifyMasterApi...
info: Now listening on: http://localhost:5120
```

---

## 🧪 Quick Test Commands

### Health Checks
```bash
# Application ready
curl http://localhost:5120/health/ready

# Application alive
curl http://localhost:5120/health/live
```

### System Status
```bash
# Overall status
curl http://localhost:5120/api/systemstatus | jq

# Fallback status
curl http://localhost:5120/api/systemstatus/fallbacks | jq

# Redis test
curl -X POST http://localhost:5120/api/systemstatus/redis/test | jq
```

### API Testing
```bash
# Get all notification endpoints
curl http://localhost:5120/api/notifications | jq

# Check plugin status
curl http://localhost:5120/api/admin/plugins | jq
```

---

## 🔄 Understanding Fallback Systems

When you run without infrastructure, the service automatically activates fallback systems:

### Port Fallback
```
⚠️  Port 5120 is in use. Falling back to port 5121
```
**What it means:** Service finds an available port automatically.

### Redis Fallback
```
⚠️  Redis connection failed: Connection refused
🔄 Services will use RedisConnectionService fallback
```
**What it means:** All Redis operations use in-memory storage.

### Database Fallback
```
🔄 Database fallback activated - Using in-memory storage
```
**What it means:** All database operations use in-memory Entity Framework.

**✅ Result:** Service runs perfectly without any external dependencies!

---

## 📊 Quick Status Check

### Check What's Running
```bash
# System status overview
curl -s http://localhost:5120/api/systemstatus | jq '{
  Application: .Application.Name,
  Redis: .Redis.ConnectionStatus,
  Database: .Database.IsUsingInMemoryFallback,
  AnyFallbacks: .Fallbacks.AnyFallbackActive
}'
```

**Example Output:**
```json
{
  "Application": "NotifyMasterApi",
  "Redis": "Fallback (In-Memory)",
  "Database": true,
  "AnyFallbacks": true
}
```

### Performance Test
```bash
# Test Redis operations
time curl -X POST http://localhost:5120/api/systemstatus/redis/test

# Expected: Very fast response (~10ms) due to in-memory operations
```

---

## 🎯 Next Steps

### 1. Explore the API
- **Swagger UI**: http://localhost:5120/swagger
- **Health Checks**: http://localhost:5120/health/ready
- **System Status**: http://localhost:5120/api/systemstatus

### 2. Run Tests
```bash
# Run all tests
dotnet test Tests/NotifyMasterApi.Tests/

# Run fallback-specific tests
dotnet test Tests/NotifyMasterApi.Tests/ --filter Category=Fallback
```

### 3. Add Plugins
```bash
# Create plugins directory
mkdir src/Services/NotifyMasterApi/Plugins

# Plugins will be auto-loaded from this directory
```

### 4. Monitor System
```bash
# Watch system status
watch -n 2 'curl -s http://localhost:5120/api/systemstatus/fallbacks | jq .Summary.Message'

# Monitor logs
dotnet run --project src/Services/NotifyMasterApi --verbosity detailed
```

---

## 🔧 Common Quick Fixes

### Port Already in Use
```bash
# Let the service find another port automatically
# ✅ No action needed - fallback handles this

# Or kill the process using the port
sudo lsof -ti:5120 | xargs kill -9
```

### Want to Use Real Infrastructure Later
```bash
# Start Redis
docker run -d --name redis -p 6379:6379 redis:7-alpine

# Start PostgreSQL
docker run -d --name postgres \
  -e POSTGRES_DB=NotifyMaster \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 postgres:15

# Restart the service
# ✅ It will automatically detect and use real infrastructure
```

### Reset Everything
```bash
# Stop the service (Ctrl+C)
# Clear fallback data
curl -X POST http://localhost:5120/api/systemstatus/fallbacks/clear

# Restart
dotnet run --project src/Services/NotifyMasterApi
```

---

## 🎉 You're Ready!

The NotificationService is now running and ready to handle notifications!

**Key URLs:**
- **API Base**: http://localhost:5120
- **Swagger Docs**: http://localhost:5120/swagger
- **Health Check**: http://localhost:5120/health/ready
- **System Status**: http://localhost:5120/api/systemstatus

**What's Working:**
- ✅ REST API endpoints
- ✅ Plugin system (ready for plugins)
- ✅ Health monitoring
- ✅ Fallback systems (if needed)
- ✅ Comprehensive logging
- ✅ Status monitoring

**Next:** Check out the [Full Setup Guide](./SETUP_GUIDE.md) for production configuration!
