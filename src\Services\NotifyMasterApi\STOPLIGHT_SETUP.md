# Stoplight API Documentation Setup

This document explains how to set up and use Stoplight for API documentation with the NotificationService.

## Overview

The NotificationService has been configured to work with Stoplight for comprehensive API documentation. Stoplight provides:

- Interactive API documentation
- API design and validation
- Mock servers for testing
- Code generation
- Collaborative editing

## Quick Start

### 1. Local Development Documentation

When running the service in development mode, you can access the interactive documentation at:

```
http://localhost:5000/docs
```

This uses Stoplight Elements to render the OpenAPI specification directly in your browser.

### 2. OpenAPI Specification

The service exposes its OpenAPI specification at:

```
http://localhost:5000/openapi.json
```

This JSON file can be imported into Stoplight Studio or other API tools.

### 3. Using Stoplight Studio

1. **Install Stoplight Studio**: Download from https://stoplight.io/studio
2. **Import the API**: 
   - Open Stoplight Studio
   - Click "Import" → "From URL"
   - Enter: `http://localhost:5000/openapi.json`
   - Or use the local `stoplight.yml` configuration file

3. **Configure the Project**:
   - The `stoplight.yml` file contains pre-configured settings
   - Customize branding, themes, and validation rules as needed

### 4. Stoplight Platform Integration

For team collaboration and hosted documentation:

1. **Create a Stoplight Account**: Sign up at https://stoplight.io
2. **Create a New Project**: Import the OpenAPI spec
3. **Configure Publishing**: Set up hosted documentation
4. **Invite Team Members**: Collaborate on API design

## API Endpoints Overview

The NotificationService provides the following main endpoints:

### Notifications (`/api/notifications`)
- `POST /send` - Send multi-channel notifications

### Email (`/api/email`)
- `POST /send` - Send email (queued)
- `POST /send/direct` - Send email (immediate)
- `GET /status` - Check email status
- `GET /history` - Get email history
- `POST /resend` - Resend email
- Admin and metrics endpoints

### SMS (`/api/sms`)
- `POST /send` - Send SMS (queued)
- `POST /send/direct` - Send SMS (immediate)
- `POST /send/bulk` - Send bulk SMS
- `GET /status` - Check SMS status
- `GET /history` - Get SMS history
- `POST /resend` - Resend SMS
- Admin and metrics endpoints

### Push Notifications (`/api/push`)
- `POST /send` - Send push notification (queued)
- `POST /send/direct` - Send push notification (immediate)
- `POST /send/bulk` - Send bulk push notifications
- `GET /status` - Check push notification status
- `GET /history` - Get push notification history
- `POST /resend` - Resend push notification
- Admin and metrics endpoints

### Administration (`/api/admin`)
- Plugin management endpoints
- System status and monitoring
- Queue management
- Logging and metrics

## Configuration

### Environment Variables

Set these environment variables for different environments:

```bash
# Development
ASPNETCORE_ENVIRONMENT=Development

# Production
ASPNETCORE_ENVIRONMENT=Production
STOPLIGHT_API_URL=https://api.notificationservice.com
```

### Customization

Edit `stoplight.yml` to customize:

- Branding and themes
- Server URLs
- Validation rules
- Mock server settings
- Publishing options

## Benefits of Stoplight Integration

1. **Interactive Documentation**: Users can test API endpoints directly
2. **Code Generation**: Generate client SDKs in multiple languages
3. **API Validation**: Ensure OpenAPI spec compliance
4. **Mock Servers**: Test against mock responses
5. **Collaboration**: Team-based API design and review
6. **Version Control**: Track API changes over time

## Troubleshooting

### Common Issues

1. **OpenAPI spec not loading**: Ensure the service is running on the correct port
2. **CORS errors**: Configure CORS settings if accessing from different domains
3. **Missing documentation**: Ensure XML documentation is enabled in the project

### Support

For Stoplight-specific issues, refer to:
- Stoplight Documentation: https://docs.stoplight.io
- Stoplight Community: https://community.stoplight.io

For NotificationService API issues, check the application logs and health endpoints.
