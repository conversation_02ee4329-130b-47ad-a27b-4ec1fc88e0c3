# ⚙️ Configuration Reference

Complete configuration reference for the NotificationService, including all settings, environment variables, and configuration options.

## 📋 Configuration Files

### appsettings.json (Main Configuration)

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=NotifyMaster;Username=postgres;Password=your_****word",
    "Redis": "localhost:6379"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Warning",
      "NotifyMasterApi.Services": "Information",
      "NotifyMasterApi.Services.RedisConnectionService": "Information",
      "NotifyMasterApi.Services.InMemoryDatabaseService": "Information"
    }
  },
  "AllowedHosts": "*",
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://localhost:5120"
      },
      "Https": {
        "Url": "https://localhost:5121",
        "Certificate": {
          "Path": "path/to/certificate.pfx",
          "Password": "certificate_****word"
        }
      }
    }
  },
  "PluginSettings": {
    "PluginDirectory": "./Plugins",
    "AutoLoadPlugins": true,
    "PluginScanInterval": "00:01:00",
    "EnableHotReload": true
  },
  "NotificationSettings": {
    "DefaultRetryCount": 3,
    "RetryDelaySeconds": 30,
    "MaxConcurrentNotifications": 100,
    "EnableBatching": true,
    "BatchSize": 50
  },
  "HealthChecks": {
    "EnableDetailedErrors": true,
    "Timeout": "00:00:30"
  }
}
```

### appsettings.Development.json

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "",
    "Redis": "localhost:9999"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Information",
      "NotifyMasterApi.Services.RedisConnectionService": "Debug",
      "NotifyMasterApi.Services.InMemoryDatabaseService": "Debug",
      "NotifyMasterApi.Services.RuntimePluginManager": "Debug"
    }
  },
  "PluginSettings": {
    "EnableHotReload": true,
    "PluginScanInterval": "00:00:30"
  },
  "NotificationSettings": {
    "EnableBatching": false,
    "MaxConcurrentNotifications": 10
  }
}
```

### appsettings.Production.json

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "NotifyMasterApi": "Information"
    }
  },
  "PluginSettings": {
    "EnableHotReload": false,
    "PluginScanInterval": "00:05:00"
  },
  "NotificationSettings": {
    "MaxConcurrentNotifications": 1000,
    "EnableBatching": true,
    "BatchSize": 100
  },
  "HealthChecks": {
    "EnableDetailedErrors": false
  }
}
```

## 🌍 Environment Variables

### Connection Strings
```bash
# Database connection
CONNECTIONSTRINGS__DEFAULTCONNECTION="Host=localhost;Database=NotifyMaster;Username=postgres;Password=****word"

# Redis connection
CONNECTIONSTRINGS__REDIS="localhost:6379"

# Redis with authentication
CONNECTIONSTRINGS__REDIS="localhost:6379,****word=your_redis_****word"

# Redis cluster
CONNECTIONSTRINGS__REDIS="redis-node1:6379,redis-node2:6379,redis-node3:6379"
```

### Application Settings
```bash
# Environment
ASPNETCORE_ENVIRONMENT=Development|Staging|Production

# URLs
ASPNETCORE_URLS="http://localhost:5120;https://localhost:5121"

# Logging level
LOGGING__LOGLEVEL__DEFAULT=Information|Debug|Warning|Error

# Plugin settings
PLUGINSETTINGS__PLUGINDIRECTORY="./Plugins"
PLUGINSETTINGS__AUTOLOADPLUGINS=true|false
PLUGINSETTINGS__ENABLEHOTRELOAD=true|false
```

### Security Settings
```bash
# HTTPS certificate
KESTREL__CERTIFICATES__DEFAULT__PATH="/path/to/certificate.pfx"
KESTREL__CERTIFICATES__DEFAULT__PASSWORD="certificate_****word"

# CORS settings
CORS__ALLOWEDORIGINS="https://yourdomain.com,https://anotherdomain.com"
CORS__ALLOWEDMETHODS="GET,POST,PUT,DELETE"
```

## 🔧 Configuration Sections

### 1. Connection Strings

| Setting | Description | Default | Example |
|---------|-------------|---------|---------|
| `DefaultConnection` | PostgreSQL database connection | `""` (triggers fallback) | `Host=localhost;Database=NotifyMaster;Username=postgres;Password=****` |
| `Redis` | Redis server connection | `"localhost:6379"` | `localhost:6379,****word=my****` |

### 2. Plugin Settings

| Setting | Description | Default | Values |
|---------|-------------|---------|--------|
| `PluginDirectory` | Directory to scan for plugins | `"./Plugins"` | Any valid path |
| `AutoLoadPlugins` | Automatically load plugins on startup | `true` | `true`, `false` |
| `PluginScanInterval` | How often to scan for new plugins | `"00:01:00"` | TimeSpan format |
| `EnableHotReload` | Allow runtime plugin loading/unloading | `true` | `true`, `false` |

### 3. Notification Settings

| Setting | Description | Default | Range |
|---------|-------------|---------|-------|
| `DefaultRetryCount` | Default retry attempts for failed notifications | `3` | 0-10 |
| `RetryDelaySeconds` | Delay between retry attempts | `30` | 1-3600 |
| `MaxConcurrentNotifications` | Maximum concurrent notification processing | `100` | 1-10000 |
| `EnableBatching` | Enable batch processing of notifications | `true` | `true`, `false` |
| `BatchSize` | Number of notifications per batch | `50` | 1-1000 |

### 4. Health Check Settings

| Setting | Description | Default | Values |
|---------|-------------|---------|--------|
| `EnableDetailedErrors` | Include detailed error information | `true` | `true`, `false` |
| `Timeout` | Health check timeout | `"00:00:30"` | TimeSpan format |

## 🔒 Security Configuration

### HTTPS Configuration
```json
{
  "Kestrel": {
    "Endpoints": {
      "Https": {
        "Url": "https://localhost:5121",
        "Certificate": {
          "Path": "certificate.pfx",
          "Password": "certificate_****word"
        }
      }
    }
  }
}
```

### CORS Configuration
```json
{
  "Cors": {
    "AllowedOrigins": ["https://yourdomain.com"],
    "AllowedMethods": ["GET", "POST", "PUT", "DELETE"],
    "AllowedHeaders": ["Content-Type", "Authorization"],
    "AllowCredentials": true
  }
}
```

### Authentication Configuration
```json
{
  "Authentication": {
    "JwtBearer": {
      "Authority": "https://your-auth-server.com",
      "Audience": "notificationservice-api",
      "RequireHttpsMetadata": true
    }
  }
}
```

## 🐳 Docker Configuration

### Environment Variables for Docker
```bash
# Docker run with environment variables
docker run -d \
  --name notifymaster-api \
  -p 5120:5120 \
  -e ASPNETCORE_ENVIRONMENT=Production \
  -e CONNECTIONSTRINGS__REDIS="redis:6379" \
  -e CONNECTIONSTRINGS__DEFAULTCONNECTION="Host=postgres;Database=NotifyMaster;Username=postgres;Password=****word" \
  -e PLUGINSETTINGS__PLUGINDIRECTORY="/app/plugins" \
  -e LOGGING__LOGLEVEL__DEFAULT=Information \
  notifymaster-api
```

### Docker Compose Configuration
```yaml
version: '3.8'
services:
  notifymaster-api:
    image: notifymaster-api
    ports:
      - "5120:5120"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - CONNECTIONSTRINGS__REDIS=redis:6379
      - CONNECTIONSTRINGS__DEFAULTCONNECTION=Host=postgres;Database=NotifyMaster;Username=postgres;Password=****word
      - PLUGINSETTINGS__PLUGINDIRECTORY=/app/plugins
    volumes:
      - ./plugins:/app/plugins
    depends_on:
      - redis
      - postgres

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=NotifyMaster
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=****word
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 🔄 Fallback Configuration

### Triggering Fallbacks

**Redis Fallback:**
```json
{
  "ConnectionStrings": {
    "Redis": "localhost:9999"  // Invalid port triggers fallback
  }
}
```

**Database Fallback:**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": ""  // Empty string triggers fallback
  }
}
```

**Port Fallback:**
```json
{
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://localhost:5120"  // If occupied, auto-fallback to 5121, 5122, etc.
      }
    }
  }
}
```

### Fallback Monitoring
```json
{
  "Logging": {
    "LogLevel": {
      "NotifyMasterApi.Services.RedisConnectionService": "Information",
      "NotifyMasterApi.Services.InMemoryDatabaseService": "Information"
    }
  }
}
```

## 📊 Monitoring Configuration

### Application Insights (Optional)
```json
{
  "ApplicationInsights": {
    "InstrumentationKey": "your-instrumentation-key",
    "EnableAdaptiveSampling": true,
    "EnableQuickPulseMetricStream": true
  }
}
```

### Serilog Configuration (Optional)
```json
{
  "Serilog": {
    "Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"],
    "MinimumLevel": "Information",
    "WriteTo": [
      { "Name": "Console" },
      {
        "Name": "File",
        "Args": {
          "path": "logs/notifymaster-.txt",
          "rollingInterval": "Day"
        }
      }
    ]
  }
}
```

## 🧪 Testing Configuration

### Test Environment Settings
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "",
    "Redis": "localhost:9999"
  },
  "PluginSettings": {
    "AutoLoadPlugins": false,
    "EnableHotReload": false
  },
  "NotificationSettings": {
    "MaxConcurrentNotifications": 1,
    "EnableBatching": false
  }
}
```

### Integration Test Configuration
```bash
# Environment variables for integration tests
ASPNETCORE_ENVIRONMENT=Testing
CONNECTIONSTRINGS__DEFAULTCONNECTION=""
CONNECTIONSTRINGS__REDIS="localhost:9999"
PLUGINSETTINGS__AUTOLOADPLUGINS=false
```

## 🔍 Configuration Validation

### Startup Validation
The application validates configuration on startup and provides clear error messages:

```
✅ Configuration valid
⚠️  Redis connection failed - using fallback
⚠️  Database connection failed - using fallback
✅ Plugin directory found: ./Plugins
✅ Health checks configured
```

### Configuration Endpoints
```bash
# Check current configuration status
curl http://localhost:5120/api/systemstatus

# Check specific component configuration
curl http://localhost:5120/api/systemstatus/redis
curl http://localhost:5120/api/systemstatus/database
```

## 📝 Configuration Best Practices

### Development
- Use fallback systems (empty connection strings)
- Enable detailed logging
- Enable hot reload for plugins
- Use smaller batch sizes

### Production
- Use real infrastructure connections
- Disable detailed error messages
- Disable hot reload for stability
- Use larger batch sizes for performance
- Enable HTTPS
- Configure proper authentication

### Security
- Never store ****words in appsettings.json
- Use environment variables or Azure Key Vault
- Enable HTTPS in production
- Configure CORS appropriately
- Use strong authentication mechanisms

## 🔗 Related Documentation

- [Setup Guide](./SETUP_GUIDE.md) - Complete installation instructions
- [Quick Start](./QUICK_START.md) - Get running in 5 minutes
- [Fallback Systems](./FALLBACK_SYSTEMS.md) - Understanding fallback mechanisms
- [API Documentation](./API_DOCUMENTATION.md) - Complete API reference
