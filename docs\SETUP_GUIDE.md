# 🚀 NotificationService - Complete Setup Guide

## Prerequisites

### Required Software
- **.NET 9.0 SDK** - [Download here](https://dotnet.microsoft.com/download/dotnet/9.0)
- **Git** - [Download here](https://git-scm.com/downloads)
- **Docker** (Optional but recommended) - [Download here](https://www.docker.com/get-started)

### Optional Infrastructure
- **Redis Server** (or use fallback)
- **PostgreSQL Database** (or use fallback)

## 📥 Step 1: Clone the Repository

```bash
# Clone the repository
git clone https://github.com/bloodchild8906/NotificationService-master.git
cd NotificationService-master

# Verify the structure
ls -la
```

**Expected Structure:**
```
NotificationService-master/
├── src/
│   ├── Services/NotifyMasterApi/
│   ├── Core/PluginCore/
│   ├── Contracts/
│   └── Libraries/
├── Tests/
├── docs/
└── README.md
```

## 🔧 Step 2: Basic Configuration

### 2.1 Configure Application Settings

Navigate to the main API project:
```bash
cd src/Services/NotifyMasterApi
```

Edit `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=NotifyMaster;Username=postgres;Password=your_****word",
    "Redis": "localhost:6379"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "NotifyMasterApi.Services": "Information"
    }
  },
  "AllowedHosts": "*",
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://localhost:5120"
      }
    }
  }
}
```

### 2.2 Configure Development Settings (Optional)

Create `appsettings.Development.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "",
    "Redis": "localhost:9999"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "NotifyMasterApi.Services.RedisConnectionService": "Debug",
      "NotifyMasterApi.Services.InMemoryDatabaseService": "Debug"
    }
  }
}
```

**Note:** Invalid connection strings will trigger fallback systems for development.

## 🐳 Step 3: Infrastructure Setup (Choose Your Path)

### Option A: Quick Start with Fallbacks (Recommended for Development)

**No infrastructure needed!** The application will use fallback systems:

```bash
# Build and run immediately
dotnet build
dotnet run
```

**Expected Output:**
```
⚠️  Port 5120 is in use. Falling back to port 5121
✅ Database connection configured successfully
⚠️  Redis connection failed: Connection refused
🔄 Services will use RedisConnectionService fallback
🚀 Starting NotifyMasterApi...
info: Now listening on: http://localhost:5121
```

### Option B: Full Infrastructure Setup

#### 3.1 Setup PostgreSQL Database

**Using Docker:**
```bash
# Start PostgreSQL container
docker run -d \
  --name notifymaster-postgres \
  -e POSTGRES_DB=NotifyMaster \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=your_****word \
  -p 5432:5432 \
  postgres:15

# Verify connection
docker exec -it notifymaster-postgres psql -U postgres -d NotifyMaster -c "SELECT version();"
```

**Using Local Installation:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS
brew install postgresql
brew services start postgresql

# Windows
# Download from https://www.postgresql.org/download/windows/
```

#### 3.2 Setup Redis Server

**Using Docker:**
```bash
# Start Redis container
docker run -d \
  --name notifymaster-redis \
  -p 6379:6379 \
  redis:7-alpine

# Verify connection
docker exec -it notifymaster-redis redis-cli ping
# Expected: PONG
```

**Using Local Installation:**
```bash
# Ubuntu/Debian
sudo apt install redis-server
sudo systemctl start redis-server

# macOS
brew install redis
brew services start redis

# Windows
# Download from https://github.com/microsoftarchive/redis/releases
```

## 🏗️ Step 4: Build the Application

### 4.1 Restore Dependencies
```bash
# From the root directory
dotnet restore src/NotificationService.sln
```

### 4.2 Build All Projects
```bash
# Build the entire solution
dotnet build src/NotificationService.sln --configuration Release

# Or build just the API
dotnet build src/Services/NotifyMasterApi --configuration Release
```

**Expected Output:**
```
Build succeeded.
    0 Error(s)
    8 Warning(s) (non-critical)
```

## 🚀 Step 5: Run the Application

### 5.1 Start the API Service
```bash
# From the API project directory
cd src/Services/NotifyMasterApi
dotnet run --configuration Release

# Or from root directory
dotnet run --project src/Services/NotifyMasterApi --configuration Release
```

### 5.2 Verify Startup
**Successful startup indicators:**
- ✅ Port binding successful (or fallback port used)
- ✅ Database connection (or fallback activated)
- ✅ Redis connection (or fallback activated)
- ✅ Plugin system initialized
- ✅ Health checks registered

**Example successful output:**
```
✅ Using preferred port 5120
✅ Database connection established successfully
✅ Redis connection established successfully
🚀 Starting NotifyMasterApi...
info: NotifyMasterApi.Services.PluginAutoLoaderService[0]
      Auto-loaded 0 plugins
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: http://localhost:5120
info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
```

## 🔍 Step 6: Verify Installation

### 6.1 Check Health Endpoints
```bash
# Check if application is ready
curl http://localhost:5120/health/ready

# Check if application is alive
curl http://localhost:5120/health/live

# Expected: HTTP 200 OK
```

### 6.2 Check System Status
```bash
# Get overall system status
curl http://localhost:5120/api/systemstatus

# Check fallback status
curl http://localhost:5120/api/systemstatus/fallbacks

# Test Redis connection
curl -X POST http://localhost:5120/api/systemstatus/redis/test
```

### 6.3 Access Swagger Documentation
Open your browser and navigate to:
```
http://localhost:5120/swagger/index.html
```

**You should see the complete API documentation with all endpoints.**

## 🔌 Step 7: Plugin Configuration

### 7.1 Create Plugin Directory
```bash
# Create plugins directory
mkdir -p src/Services/NotifyMasterApi/Plugins

# Set permissions (Linux/macOS)
chmod 755 src/Services/NotifyMasterApi/Plugins
```

### 7.2 Plugin Auto-Loading
The system automatically scans for plugins in:
- `./Plugins/` directory
- `./bin/Plugins/` directory
- Any `.dll` files with plugin manifests

**Plugin naming convention:**
- `Plugin.Email.SendGrid.dll`
- `Plugin.Sms.Twilio.dll`
- `Plugin.Push.FCM.dll`

## 🧪 Step 8: Run Tests

### 8.1 Run All Tests
```bash
# From root directory
dotnet test Tests/NotifyMasterApi.Tests/

# Run with coverage
dotnet test Tests/NotifyMasterApi.Tests/ --collect:"XPlat Code Coverage"

# Run specific test categories
dotnet test Tests/NotifyMasterApi.Tests/ --filter Category=Fallback
```

### 8.2 Run PowerShell Test Suite (Windows)
```powershell
# Navigate to tests directory
cd Tests

# Run comprehensive test suite
.\run-tests.ps1

# Run with coverage
.\run-tests.ps1 -Coverage

# Run specific categories
.\run-tests.ps1 -Category Unit,Integration
```

## 🔧 Step 9: Environment-Specific Configuration

### 9.1 Development Environment
```bash
# Set environment
export ASPNETCORE_ENVIRONMENT=Development

# Use fallback systems
export CONNECTIONSTRINGS__REDIS="localhost:9999"
export CONNECTIONSTRINGS__DEFAULTCONNECTION=""

# Run with development settings
dotnet run --project src/Services/NotifyMasterApi
```

### 9.2 Production Environment
```bash
# Set environment
export ASPNETCORE_ENVIRONMENT=Production

# Configure real infrastructure
export CONNECTIONSTRINGS__REDIS="your-redis-cluster:6379"
export CONNECTIONSTRINGS__DEFAULTCONNECTION="Host=your-db;Database=NotifyMaster;Username=user;Password=****"

# Run with production settings
dotnet run --project src/Services/NotifyMasterApi --configuration Release
```

### 9.3 Docker Deployment
```bash
# Build Docker image (if Dockerfile exists)
docker build -t notifymaster-api .

# Run with Docker Compose
docker-compose up -d

# Or run standalone
docker run -d \
  --name notifymaster-api \
  -p 5120:5120 \
  -e CONNECTIONSTRINGS__REDIS="redis:6379" \
  -e CONNECTIONSTRINGS__DEFAULTCONNECTION="Host=postgres;Database=NotifyMaster;Username=postgres;Password=****word" \
  notifymaster-api
```

## 🔍 Step 10: Troubleshooting

### 10.1 Common Issues

**Issue: Port already in use**
```bash
# Check what's using the port
sudo lsof -i :5120

# Kill the process
sudo kill -9 <PID>

# Or let the application use fallback port
# ✅ Application will automatically find available port
```

**Issue: Redis connection failed**
```bash
# Check Redis status
redis-cli ping

# Start Redis if not running
docker start notifymaster-redis

# Or use fallback (application continues working)
# ✅ Application uses in-memory Redis fallback
```

**Issue: Database connection failed**
```bash
# Check PostgreSQL status
docker exec -it notifymaster-postgres pg_isready

# Start PostgreSQL if not running
docker start notifymaster-postgres

# Or use fallback (application continues working)
# ✅ Application uses in-memory database fallback
```

### 10.2 Debug Commands

```bash
# Check application logs
dotnet run --project src/Services/NotifyMasterApi --verbosity detailed

# Check system status
curl http://localhost:5120/api/systemstatus | jq

# Check specific component status
curl http://localhost:5120/api/systemstatus/redis | jq
curl http://localhost:5120/api/systemstatus/database | jq

# Test Redis operations
curl -X POST http://localhost:5120/api/systemstatus/redis/test | jq
```

### 10.3 Reset Fallback Data

```bash
# Clear all fallback data
curl -X POST http://localhost:5120/api/systemstatus/fallbacks/clear

# Restart application to reset state
# Ctrl+C to stop, then dotnet run to restart
```

## ✅ Step 11: Verification Checklist

### Basic Functionality
- [ ] Application starts without errors
- [ ] Health endpoints respond (200 OK)
- [ ] Swagger documentation accessible
- [ ] System status API returns data

### Infrastructure
- [ ] Database connection working (or fallback active)
- [ ] Redis connection working (or fallback active)
- [ ] Port binding successful (or fallback port used)

### Plugin System
- [ ] Plugin directory created
- [ ] Plugin auto-loader service running
- [ ] Plugin management endpoints accessible

### Testing
- [ ] Unit tests ****
- [ ] Integration tests ****
- [ ] Fallback tests ****

## 🎉 Success!

Your NotificationService is now fully configured and running! 

**Next Steps:**
1. 📖 Read the [API Documentation](./API_DOCUMENTATION.md)
2. 🔌 Learn about [Plugin Development](./PLUGIN_DEVELOPMENT.md)
3. 🔄 Understand [Fallback Systems](./FALLBACK_SYSTEMS.md)
4. 🏗️ Review [Architecture Diagrams](./ARCHITECTURE_DIAGRAMS.md)

**Quick Access URLs:**
- **API**: http://localhost:5120
- **Swagger**: http://localhost:5120/swagger
- **Health**: http://localhost:5120/health/ready
- **Status**: http://localhost:5120/api/systemstatus
