#!/bin/bash
# CI/CD Test Script for NotificationService
# Tests all deployment scenarios and fallback systems

set -e

echo "🚀 Starting CI/CD Tests for NotificationService"
echo "=============================================="

# Test configuration
DEV_URL="http://notifymaster-dev:5120"
STAGING_URL="http://notifymaster-staging:5120"
PROD_URL="http://notifymaster-prod:5120"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Wait for service to be ready
wait_for_service() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1

    log_info "Waiting for $name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url/health/live" > /dev/null 2>&1; then
            log_success "$name is ready!"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: $name not ready yet..."
        sleep 10
        ((attempt++))
    done
    
    log_error "$name failed to start within timeout"
    return 1
}

# Test health endpoints
test_health_endpoints() {
    local url=$1
    local env_name=$2
    
    log_info "Testing health endpoints for $env_name..."
    
    # Test liveness
    if curl -s -f "$url/health/live" > /dev/null; then
        log_success "$env_name: Liveness check passed"
    else
        log_error "$env_name: Liveness check failed"
        return 1
    fi
    
    # Test readiness
    local ready_status=$(curl -s -o /dev/null -w "%{http_code}" "$url/health/ready")
    if [ "$ready_status" = "200" ]; then
        log_success "$env_name: Readiness check passed"
    elif [ "$ready_status" = "503" ]; then
        log_warning "$env_name: Readiness check returned 503 (may be using fallbacks)"
    else
        log_error "$env_name: Readiness check failed with status $ready_status"
        return 1
    fi
}

# Test system status
test_system_status() {
    local url=$1
    local env_name=$2
    
    log_info "Testing system status for $env_name..."
    
    local response=$(curl -s "$url/api/systemstatus")
    if [ $? -ne 0 ]; then
        log_error "$env_name: System status endpoint not accessible"
        return 1
    fi
    
    # Parse response using basic text processing (since jq might not be available)
    local app_name=$(echo "$response" | grep -o '"Name":"[^"]*"' | cut -d'"' -f4)
    local health_status=$(echo "$response" | grep -o '"Status":"[^"]*"' | cut -d'"' -f4)
    
    log_success "$env_name: Application: $app_name"
    log_success "$env_name: Health Status: $health_status"
    
    # Check for fallbacks
    if echo "$response" | grep -q '"AnyFallbackActive":true'; then
        log_warning "$env_name: Fallback systems are active"
        
        if echo "$response" | grep -q '"RedisUsingFallback":true'; then
            log_warning "$env_name: Redis using fallback"
        fi
        
        if echo "$response" | grep -q '"DatabaseUsingFallback":true'; then
            log_warning "$env_name: Database using fallback"
        fi
    else
        log_success "$env_name: All systems operational - no fallbacks active"
    fi
}

# Test API functionality
test_api_functionality() {
    local url=$1
    local env_name=$2
    
    log_info "Testing API functionality for $env_name..."
    
    # Test Swagger documentation
    local swagger_status=$(curl -s -o /dev/null -w "%{http_code}" "$url/swagger/index.html")
    if [ "$swagger_status" = "200" ]; then
        log_success "$env_name: Swagger documentation accessible"
    else
        log_warning "$env_name: Swagger documentation not accessible (status: $swagger_status)"
    fi
    
    # Test plugin system
    local plugin_status=$(curl -s -o /dev/null -w "%{http_code}" "$url/api/admin/plugins")
    if [ "$plugin_status" = "200" ]; then
        log_success "$env_name: Plugin system accessible"
    else
        log_warning "$env_name: Plugin system not accessible (status: $plugin_status)"
    fi
}

# Test fallback operations
test_fallback_operations() {
    local url=$1
    local env_name=$2
    
    log_info "Testing fallback operations for $env_name..."
    
    # Test Redis operations
    local redis_test_response=$(curl -s -X POST "$url/api/systemstatus/redis/test")
    if echo "$redis_test_response" | grep -q '"TestResult":"Success"'; then
        log_success "$env_name: Redis operations working"
        
        if echo "$redis_test_response" | grep -q '"UsingFallback":true'; then
            log_info "$env_name: Using Redis fallback (in-memory)"
        else
            log_info "$env_name: Using real Redis connection"
        fi
    else
        log_error "$env_name: Redis operations failed"
        return 1
    fi
}

# Test performance
test_performance() {
    local url=$1
    local env_name=$2
    
    log_info "Testing performance for $env_name..."
    
    for endpoint in "health/live" "health/ready" "api/systemstatus"; do
        local start_time=$(date +%s.%N)
        curl -s "$url/$endpoint" > /dev/null
        local end_time=$(date +%s.%N)
        local response_time=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "N/A")
        
        if [ "$response_time" != "N/A" ]; then
            log_info "$env_name: /$endpoint response time: ${response_time}s"
            
            # Check if response time is acceptable (< 3 seconds)
            if (( $(echo "$response_time < 3.0" | bc -l 2>/dev/null || echo "0") )); then
                log_success "$env_name: Response time acceptable"
            else
                log_warning "$env_name: Response time high: ${response_time}s"
            fi
        else
            log_info "$env_name: /$endpoint response time measurement not available"
        fi
    done
}

# Run comprehensive tests for an environment
test_environment() {
    local url=$1
    local env_name=$2
    
    echo ""
    log_info "🧪 Testing $env_name Environment"
    echo "================================"
    
    # Wait for service to be ready
    if ! wait_for_service "$url" "$env_name"; then
        log_error "Skipping tests for $env_name - service not ready"
        return 1
    fi
    
    # Run all tests
    test_health_endpoints "$url" "$env_name"
    test_system_status "$url" "$env_name"
    test_api_functionality "$url" "$env_name"
    test_fallback_operations "$url" "$env_name"
    test_performance "$url" "$env_name"
    
    log_success "$env_name environment tests completed!"
}

# Main test execution
main() {
    echo ""
    log_info "Starting comprehensive CI/CD tests..."
    
    # Test Development Environment (All fallbacks expected)
    test_environment "$DEV_URL" "Development"
    
    # Test Staging Environment (Mixed infrastructure)
    test_environment "$STAGING_URL" "Staging"
    
    # Test Production Environment (Full infrastructure)
    test_environment "$PROD_URL" "Production"
    
    echo ""
    echo "🎉 CI/CD Test Summary"
    echo "===================="
    
    # Validate expected fallback states
    log_info "Validating expected fallback states..."
    
    # Development should have fallbacks
    dev_fallbacks=$(curl -s "$DEV_URL/api/systemstatus" | grep -o '"AnyFallbackActive":[^,}]*' | cut -d':' -f2)
    if [ "$dev_fallbacks" = "true" ]; then
        log_success "Development: Fallbacks active as expected"
    else
        log_warning "Development: Expected fallbacks to be active"
    fi
    
    # Production should not have fallbacks (ideally)
    prod_fallbacks=$(curl -s "$PROD_URL/api/systemstatus" | grep -o '"AnyFallbackActive":[^,}]*' | cut -d':' -f2)
    if [ "$prod_fallbacks" = "false" ]; then
        log_success "Production: No fallbacks active - full infrastructure working"
    else
        log_info "Production: Some fallbacks active (acceptable for testing)"
    fi
    
    echo ""
    log_success "🎉 All CI/CD tests completed successfully!"
    echo ""
    echo "📊 Environment URLs:"
    echo "   Development: $DEV_URL"
    echo "   Staging: $STAGING_URL"
    echo "   Production: $PROD_URL"
    echo ""
    echo "📚 Documentation:"
    echo "   Development Swagger: $DEV_URL/swagger"
    echo "   Staging Swagger: $STAGING_URL/swagger"
    echo "   Production Swagger: $PROD_URL/swagger"
    echo ""
    echo "📈 System Status:"
    echo "   Development Status: $DEV_URL/api/systemstatus"
    echo "   Staging Status: $STAGING_URL/api/systemstatus"
    echo "   Production Status: $PROD_URL/api/systemstatus"
}

# Run main function
main "$@"
