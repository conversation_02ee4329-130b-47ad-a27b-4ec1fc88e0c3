using Microsoft.AspNetCore.Mvc;
using NotificationContract.Enums;
using NotificationContract.Models;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Services;
using NotifyMasterApi.Events.Services;
using NotifyMasterApi.Events.Models;

namespace NotifyMasterApi.Controllers;
/// <summary>
/// Email service controller for sending and managing email notifications
/// </summary>
/// <remarks>
/// This controller provides endpoints for sending and managing email notifications, including:
/// - Sending email notifications
/// - Managing email providers (switch, test, reload)
/// - Configuring email service settings
/// </remarks>
[ApiController]
[Route("api/[controller]")]
public class EmailController : ControllerBase
{
    private readonly IEmailGateway _emailGateway;
    private readonly INotificationLoggingService _loggingService;
    private readonly INotificationQueueService _queueService;
    private readonly IEventPublisher _eventPublisher;
    private readonly ILogger<EmailController> _logger;

    public EmailController(
        IEmailGateway emailGateway,
        INotificationLoggingService loggingService,
        INotificationQueueService queueService,
        IEventPublisher eventPublisher,
        ILogger<EmailController> logger)
    {
        _emailGateway = emailGateway;
        _loggingService = loggingService;
        _queueService = queueService;
        _eventPublisher = eventPublisher;
        _logger = logger;
    }

    [HttpPost("send")]
    public async Task<ActionResult> SendEmail([FromBody] EmailMessageRequest message)
    {
        try
        {
            var correlationId = Guid.NewGuid().ToString();

            // Log the notification
            var messageId = await _loggingService.LogNotificationAsync(
                NotificationType.Email,
                message.To,
                message.Subject,
                message.Body,
                null, // UserId not available in EmailMessageRequest
                correlationId);

            // Queue the notification
            var queueItem = new NotificationQueueItem
            {
                MessageId = messageId,
                Type = NotificationType.Email,
                Recipient = message.To,
                Subject = message.Subject,
                Content = message.Body,
                UserId = null, // UserId not available in EmailMessageRequest
                CorrelationId = correlationId,
                Priority = NotificationPriority.Normal
            };

            var queueId = await _queueService.QueueNotificationAsync(queueItem);

            // Publish notification queued event
            var queuedEvent = new NotificationQueuedEvent
            {
                NotificationType = NotificationType.Email,
                MessageId = messageId,
                CorrelationId = correlationId,
                QueueId = queueId,
                Recipient = message.To,
                Subject = message.Subject,
                Priority = (int)NotificationPriority.Normal
            };

            await _eventPublisher.PublishNotificationEventAsync(queuedEvent);
            await _eventPublisher.PublishNotificationEventToTypeAsync("Email", queuedEvent);
            if (!string.IsNullOrEmpty(correlationId))
            {
                await _eventPublisher.PublishNotificationEventToCorrelationAsync(correlationId, queuedEvent);
            }

            _logger.LogInformation("Queued email notification {MessageId} with queue ID {QueueId}", messageId, queueId);

            return Ok(new {
                Success = true,
                MessageId = messageId,
                QueueId = queueId,
                CorrelationId = correlationId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing email request");
            return StatusCode(500, new { Error = "Internal server error", Message = ex.Message });
        }
    }

    [HttpPost("send/direct")]
    public async Task<ActionResult> SendEmailDirect([FromBody] EmailMessageRequest message)
    {
        var result = await _emailGateway.SendEmailAsync(message);
        return Ok(result);
    }

    [HttpGet("status")]
    public async Task<ActionResult> GetEmailStatus([FromQuery] string messageId)
    {
        var status = await _emailGateway.GetMessageStatusAsync(messageId);
        return Ok(status);
    }

    [HttpGet("history")]
    public async Task<ActionResult> GetEmailHistory([FromQuery] string email)
    {
        var history = await _emailGateway.GetMessageHistoryAsync(email);
        return Ok(history);
    }

    [HttpPost("resend")]
    public async Task<ActionResult> ResendEmail([FromQuery] string messageId)
    {
        var result = await _emailGateway.ResendMessageAsync(messageId);
        return Ok(result);
    }

    [HttpGet("providers")]
    public ActionResult GetEmailProviders() => Ok(_emailGateway.GetAvailableProviders());

    [HttpPost("switch-provider")]
    public async Task<ActionResult> SwitchEmailProvider([FromQuery] string providerKey)
    {
        var previousProvider = _emailGateway.GetCurrentProvider();
        await _emailGateway.SwitchProvider(providerKey);

        // Publish provider switched event
        var switchedEvent = new ProviderSwitchedEvent
        {
            ServiceType = "Email",
            PreviousProvider = previousProvider,
            NewProvider = providerKey,
            SwitchReason = "Manual switch via API"
        };

        await _eventPublisher.PublishAdminEventAsync(switchedEvent);

        return Ok(new { provider = providerKey });
    }

    [HttpPost("test")]
    public async Task<ActionResult> TestEmail([FromQuery] string to)
    {
        var startTime = DateTime.UtcNow;
        var result = await _emailGateway.SendTestMessageAsync("current", to);
        var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

        // Publish test message event
        var testEvent = new TestMessageEvent
        {
            MessageType = "Email",
            Provider = _emailGateway.GetCurrentProvider() ?? "Unknown",
            Recipient = to,
            TestSuccessful = result.Success,
            ResponseTimeMs = responseTime,
            TestResults = new Dictionary<string, object>
            {
                { "Success", result.Success },
                { "Message", result.Message ?? string.Empty },
                { "ResponseTime", responseTime }
            }
        };

        await _eventPublisher.PublishAdminEventAsync(testEvent);

        return Ok(result);
    }

    [HttpPost("reload-providers")]
    public async Task<ActionResult> ReloadEmailProviders()
    {
        await _emailGateway.ReloadProviders();
        return Ok();
    }

    [HttpPost("config/update")]
    public async Task<ActionResult> UpdateEmailConfig([FromQuery] string providerKey, [FromBody] Dictionary<string, string> config)
    {
        await _emailGateway.UpdateProviderConfiguration(providerKey, config);
        return Ok();
    }

    [HttpGet("metrics/summary")]
    public async Task<ActionResult> GetEmailSummary() => Ok(await _emailGateway.GetSummaryMetricsAsync());

    [HttpGet("metrics/detailed")]
    public async Task<ActionResult> GetEmailDetailed([FromQuery] DateTime? from, [FromQuery] DateTime? to)
    {
        return Ok(await _emailGateway.GetDetailedMetricsAsync(from, to));
    }

    [HttpGet("metrics/errors")]
    public async Task<ActionResult> GetEmailErrors() => Ok(await _emailGateway.GetErrorMetricsAsync());

    [HttpGet("metrics/monthly")]
    public async Task<ActionResult> GetEmailMonthlyStats() => Ok(await _emailGateway.GetMonthlyStatisticsAsync(DateTime.Now.Year, DateTime.Now.Month));
}
