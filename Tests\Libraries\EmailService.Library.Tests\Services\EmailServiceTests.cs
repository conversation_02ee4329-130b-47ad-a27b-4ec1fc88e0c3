using EmailContract.Models;
using EmailService.Library.Configuration;
using EmailService.Library.Interfaces;
using EmailService.Library.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace EmailService.Library.Tests.Services;

public class EmailServiceTests
{
    private readonly Mock<IEmailSenderService> _mockEmailSender;
    private readonly Mock<ILogger<EmailService.Library.Services.EmailService>> _mockLogger;
    private readonly EmailService.Library.Services.EmailService _emailService;

    public EmailServiceTests()
    {
        _mockEmailSender = new Mock<IEmailSenderService>();
        _mockLogger = new Mock<ILogger<EmailService.Library.Services.EmailService>>();
        _emailService = new EmailService.Library.Services.EmailService(_mockEmailSender.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task SendAsync_ValidRequest_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new SendEmailRequest(
            to: "<EMAIL>",
            subject: "Test Subject",
            body: "Test Body",
            from: "<EMAIL>"
        );

        var expectedResponse = new EmailResponse(true, "message-123");
        _mockEmailSender.Setup(x => x.SendAsync(It.IsAny<SendEmailRequest>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _emailService.SendAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal("message-123", result.MessageId);
        _mockEmailSender.Verify(x => x.SendAsync(request), Times.Once);
    }

    [Fact]
    public async Task SendAsync_InvalidRequest_ReturnsFailureResponse()
    {
        // Arrange
        var request = new SendEmailRequest(
            to: "",
            subject: "Test Subject",
            body: "Test Body"
        );

        // Act
        var result = await _emailService.SendAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("To address is required", result.ErrorMessage);
    }

    [Fact]
    public async Task SendAsync_ExceptionThrown_ReturnsFailureResponse()
    {
        // Arrange
        var request = new SendEmailRequest(
            to: "<EMAIL>",
            subject: "Test Subject",
            body: "Test Body"
        );

        _mockEmailSender.Setup(x => x.SendAsync(It.IsAny<SendEmailRequest>()))
            .ThrowsAsync(new Exception("SMTP server error"));

        // Act
        var result = await _emailService.SendAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("SMTP server error", result.ErrorMessage);
    }

    [Fact]
    public async Task SendBulkAsync_ValidRequests_ReturnsSuccessResponse()
    {
        // Arrange
        var requests = new BulkEmailRequest(new[]
        {
            new SendEmailRequest("<EMAIL>", "Subject 1", "Body 1"),
            new SendEmailRequest("<EMAIL>", "Subject 2", "Body 2")
        });

        _mockEmailSender.Setup(x => x.SendAsync(It.IsAny<SendEmailRequest>()))
            .ReturnsAsync(new EmailResponse(true, "message-123"));

        // Act
        var result = await _emailService.SendBulkAsync(requests);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Results.Count());
        Assert.All(result.Results, r => Assert.True(r.IsSuccess));
        _mockEmailSender.Verify(x => x.SendAsync(It.IsAny<SendEmailRequest>()), Times.Exactly(2));
    }

    [Fact]
    public async Task SendBulkAsync_MixedResults_ReturnsPartialSuccess()
    {
        // Arrange
        var requests = new BulkEmailRequest(new[]
        {
            new SendEmailRequest("<EMAIL>", "Subject 1", "Body 1"),
            new SendEmailRequest("invalid-email", "Subject 2", "Body 2")
        });

        _mockEmailSender.SetupSequence(x => x.SendAsync(It.IsAny<SendEmailRequest>()))
            .ReturnsAsync(new EmailResponse(true, "message-123"))
            .ReturnsAsync(new EmailResponse(false, errorMessage: "Invalid email address"));

        // Act
        var result = await _emailService.SendBulkAsync(requests);

        // Assert
        Assert.False(result.IsSuccess); // Not all succeeded
        Assert.Equal(2, result.Results.Count());
        Assert.True(result.Results.First().IsSuccess);
        Assert.False(result.Results.Last().IsSuccess);
    }

    [Fact]
    public async Task ResendMessageAsync_ValidMessageId_ReturnsSuccessResponse()
    {
        // Arrange
        var messageId = "message-123";
        var expectedResponse = new EmailResponse(true, messageId);
        
        // For this test, we'll assume resend functionality is implemented
        // Currently it returns "not implemented" so we'll test that behavior

        // Act
        var result = await _emailService.ResendMessageAsync(messageId);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Resend not implemented", result.ErrorMessage);
    }

    [Fact]
    public async Task GetProvidersAsync_ReturnsProviderList()
    {
        // Act
        var result = await _emailService.GetProvidersAsync();

        // Assert
        Assert.NotNull(result);
        // The actual implementation returns a dynamic object with provider information
        // We can verify it's not null and contains expected structure
    }

    [Fact]
    public async Task ConfigureProviderAsync_ValidConfiguration_ReturnsSuccess()
    {
        // Arrange
        var provider = "SMTP";
        var configuration = new { Host = "smtp.example.com", Port = 587 };

        // Act
        var result = await _emailService.ConfigureProviderAsync(provider, configuration);

        // Assert
        Assert.True(result.Success);
    }

    [Fact]
    public async Task TestProviderAsync_ValidProvider_ReturnsTestResult()
    {
        // Arrange
        var provider = "SMTP";
        var testEmail = "<EMAIL>";

        // Act
        var result = await _emailService.TestProviderAsync(provider, testEmail);

        // Assert
        Assert.NotNull(result);
        // The actual implementation returns a dynamic object with test results
    }

    [Fact]
    public async Task UpdateProviderStatusAsync_ValidProvider_ReturnsSuccess()
    {
        // Arrange
        var provider = "SMTP";
        var enabled = true;

        // Act
        var result = await _emailService.UpdateProviderStatusAsync(provider, enabled);

        // Assert
        Assert.True(result.Success);
    }

    [Fact]
    public async Task GetDeliveryMetricsAsync_ReturnsMetrics()
    {
        // Act
        var result = await _emailService.GetDeliveryMetricsAsync();

        // Assert
        Assert.NotNull(result);
        // The actual implementation returns metrics data
    }

    [Fact]
    public async Task GetProviderMetricsAsync_ValidProvider_ReturnsMetrics()
    {
        // Arrange
        var provider = "SMTP";

        // Act
        var result = await _emailService.GetProviderMetricsAsync(provider);

        // Assert
        Assert.NotNull(result);
        // The actual implementation returns provider-specific metrics
    }
}
