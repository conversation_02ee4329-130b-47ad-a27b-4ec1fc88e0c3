using Microsoft.AspNetCore.Mvc;
using NotificationContract.Enums;
using NotificationContract.Models;
using NotifyMasterApi.Data.Entities;
using NotifyMasterApi.Services;

namespace NotifyMasterApi.Controllers;

/// <summary>
/// Unified notification controller for sending multiple notification types in a single request
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[Tags("Notifications")]
public class NotificationsController : ControllerBase
{
    private readonly INotificationLoggingService _loggingService;
    private readonly INotificationQueueService _queueService;
    private readonly ILogger<NotificationsController> _logger;

    public NotificationsController(
        INotificationLoggingService loggingService,
        INotificationQueueService queueService,
        ILogger<NotificationsController> logger)
    {
        _loggingService = loggingService;
        _queueService = queueService;
        _logger = logger;
    }

    /// <summary>
    /// Send notifications across multiple channels (Email, SMS, Push) in a single request
    /// </summary>
    /// <param name="request">The notification request containing message details and target channels</param>
    /// <returns>Results for each requested notification type with queue information</returns>
    /// <response code="200">Notifications successfully queued</response>
    /// <response code="400">Invalid request data</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("send")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> Send(SendNotificationRequest request)
    {
        var results = new List<object>();
        var correlationId = Guid.NewGuid().ToString();

        try
        {
            // Queue email if requested
            if (request.NotificationTypes.Contains(NotificationType.Email))
            {
                var messageId = await _loggingService.LogNotificationAsync(
                    NotificationType.Email,
                    request.Email ?? "",
                    request.Title ?? "Notification",
                    request.Message,
                    request.Id,
                    correlationId);

                var queueItem = new NotificationQueueItem
                {
                    MessageId = messageId,
                    Type = NotificationType.Email,
                    Recipient = request.Email ?? "",
                    Subject = request.Title ?? "Notification",
                    Content = request.Message,
                    UserId = request.Id,
                    CorrelationId = correlationId,
                    Priority = NotificationPriority.Normal
                };

                var queueId = await _queueService.QueueNotificationAsync(queueItem);
                results.Add(new { Type = "Email", Queued = true, MessageId = messageId, QueueId = queueId });
                _logger.LogInformation("Queued email notification {MessageId} with queue ID {QueueId}", messageId, queueId);
            }

            // Queue SMS if requested
            if (request.NotificationTypes.Contains(NotificationType.Sms))
            {
                var messageId = await _loggingService.LogNotificationAsync(
                    NotificationType.Sms,
                    request.PhoneNumber ?? "",
                    "",
                    request.Message,
                    request.Id,
                    correlationId);

                var queueItem = new NotificationQueueItem
                {
                    MessageId = messageId,
                    Type = NotificationType.Sms,
                    Recipient = request.PhoneNumber ?? "",
                    Subject = "",
                    Content = request.Message,
                    UserId = request.Id,
                    CorrelationId = correlationId,
                    Priority = NotificationPriority.Normal
                };

                var queueId = await _queueService.QueueNotificationAsync(queueItem);
                results.Add(new { Type = "SMS", Queued = true, MessageId = messageId, QueueId = queueId });
                _logger.LogInformation("Queued SMS notification {MessageId} with queue ID {QueueId}", messageId, queueId);
            }

            // Queue push notification if requested
            if (request.NotificationTypes.Contains(NotificationType.PushMessage))
            {
                var messageId = await _loggingService.LogNotificationAsync(
                    NotificationType.PushMessage,
                    request.DeviceToken ?? "",
                    request.Title ?? "Notification",
                    request.Message,
                    request.Id,
                    correlationId);

                var queueItem = new NotificationQueueItem
                {
                    MessageId = messageId,
                    Type = NotificationType.PushMessage,
                    Recipient = request.DeviceToken ?? "",
                    Subject = request.Title ?? "Notification",
                    Content = request.Message,
                    UserId = request.Id,
                    CorrelationId = correlationId,
                    Priority = NotificationPriority.Normal
                };

                var queueId = await _queueService.QueueNotificationAsync(queueItem);
                results.Add(new { Type = "Push", Queued = true, MessageId = messageId, QueueId = queueId });
                _logger.LogInformation("Queued push notification {MessageId} with queue ID {QueueId}", messageId, queueId);
            }

            return Ok(new { Results = results, CorrelationId = correlationId, QueueLength = await _queueService.GetQueueLengthAsync() });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing notification request");
            return StatusCode(500, new { Error = "Internal server error", Message = ex.Message });
        }
    }
}