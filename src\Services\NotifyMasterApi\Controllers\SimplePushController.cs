using Microsoft.AspNetCore.Mvc;
using NotificationContract.Models;
using NotifyMasterApi.Gateways;

namespace NotifyMasterApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SimplePushController : ControllerBase
{
    private readonly IPushGateway _pushGateway;
    private readonly ILogger<SimplePushController> _logger;

    public SimplePushController(IPushGateway pushGateway, ILogger<SimplePushController> logger)
    {
        _pushGateway = pushGateway;
        _logger = logger;
    }

    [HttpPost("send")]
    public async Task<IActionResult> SendPush([FromBody] PushMessageRequest request)
    {
        try
        {
            _logger.LogInformation("Sending push notification to {DeviceToken}", request.DeviceToken);
            
            var result = await _pushGateway.SendAsync(request);
            
            if (result.IsSuccess)
            {
                return Ok(new { success = true, messageId = result.MessageId });
            }
            
            return BadRequest(new { success = false, error = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending push notification");
            return StatusCode(500, new { success = false, error = "Internal server error" });
        }
    }

    [HttpPost("send-bulk")]
    public async Task<IActionResult> SendBulkPush([FromBody] BulkPushRequest request)
    {
        try
        {
            _logger.LogInformation("Sending bulk push notifications to {Count} devices", request.Messages.Count());
            
            var result = await _pushGateway.SendBulkAsync(request);
            
            return Ok(new { 
                success = result.IsSuccess, 
                totalSent = result.Results.Count(r => r.IsSuccess),
                totalFailed = result.Results.Count(r => !r.IsSuccess),
                results = result.Results
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk push notifications");
            return StatusCode(500, new { success = false, error = "Internal server error" });
        }
    }

    [HttpGet("platforms")]
    public async Task<IActionResult> GetPlatforms()
    {
        try
        {
            var platforms = await _pushGateway.GetPlatformsAsync();
            return Ok(platforms);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push platforms");
            return StatusCode(500, new { success = false, error = "Internal server error" });
        }
    }

    [HttpPost("test/{platform}")]
    public async Task<IActionResult> TestPlatform(string platform, [FromQuery] string? testToken = null)
    {
        try
        {
            var result = await _pushGateway.TestPlatformAsync(platform, testToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing push platform {Platform}", platform);
            return StatusCode(500, new { success = false, error = "Internal server error" });
        }
    }
}
