using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using NotificationContract.Enums;
using NotificationContract.Models;
using NotifyMasterApi.Controllers;
using NotifyMasterApi.Events.Models;
using NotifyMasterApi.Events.Services;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Services;
using System.Security.Claims;
using Xunit;

namespace NotifyMasterApi.Tests.Controllers;

/// <summary>
/// Comprehensive tests for EmailController
/// </summary>
public class EmailControllerTests
{
    private readonly Mock<IEmailGateway> _mockEmailGateway;
    private readonly Mock<INotificationLoggingService> _mockLoggingService;
    private readonly Mock<INotificationQueueService> _mockQueueService;
    private readonly Mock<IEventPublisher> _mockEventPublisher;
    private readonly Mock<ILogger<EmailController>> _mockLogger;
    private readonly EmailController _controller;

    public EmailControllerTests()
    {
        _mockEmailGateway = new Mock<IEmailGateway>();
        _mockLoggingService = new Mock<INotificationLoggingService>();
        _mockQueueService = new Mock<INotificationQueueService>();
        _mockEventPublisher = new Mock<IEventPublisher>();
        _mockLogger = new Mock<ILogger<EmailController>>();

        _controller = new EmailController(
            _mockEmailGateway.Object,
            _mockLoggingService.Object,
            _mockQueueService.Object,
            _mockEventPublisher.Object,
            _mockLogger.Object);

        // Setup HTTP context
        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext()
        };
        _controller.HttpContext.TraceIdentifier = "test-trace-id";
    }

    #region SendEmail Tests

    [Fact]
    public async Task SendEmail_ValidRequest_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new EmailMessageRequest
        {
            To = "<EMAIL>",
            Subject = "Test Subject",
            Body = "Test Body",
            IsHtml = false
        };

        var messageId = "test-message-id";
        var queueId = "test-queue-id";

        _mockLoggingService
            .Setup(x => x.LogNotificationAsync(
                NotificationType.Email,
                request.To,
                request.Subject,
                request.Body,
                null,
                It.IsAny<string>()))
            .ReturnsAsync(messageId);

        _mockQueueService
            .Setup(x => x.QueueNotificationAsync(It.IsAny<NotificationQueueItem>()))
            .ReturnsAsync(queueId);

        _mockEmailGateway
            .Setup(x => x.GetCurrentProvider())
            .Returns("TestProvider");

        // Act
        var result = await _controller.SendEmail(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<ApiResponse<EmailSendResult>>(okResult.Value);

        Assert.True(response.Success);
        Assert.Equal("Email queued successfully", response.Message);
        Assert.Equal(messageId, response.Data.MessageId);
        Assert.Equal("TestProvider", response.Data.Provider);
        Assert.Equal("Queued", response.Data.Status);
        Assert.Equal(request.To, response.Data.To);
        Assert.Equal(request.Subject, response.Data.Subject);
        Assert.Equal(queueId, response.Data.QueueId);

        // Verify all services were called
        _mockLoggingService.Verify(x => x.LogNotificationAsync(
            NotificationType.Email,
            request.To,
            request.Subject,
            request.Body,
            null,
            It.IsAny<string>()), Times.Once);

        _mockQueueService.Verify(x => x.QueueNotificationAsync(It.IsAny<NotificationQueueItem>()), Times.Once);
        _mockEventPublisher.Verify(x => x.PublishNotificationEventAsync(It.IsAny<NotificationQueuedEvent>()), Times.Once);
        _mockEventPublisher.Verify(x => x.PublishNotificationEventToTypeAsync("Email", It.IsAny<NotificationQueuedEvent>()), Times.Once);
    }

    [Fact]
    public async Task SendEmail_LoggingServiceThrows_ReturnsInternalServerError()
    {
        // Arrange
        var request = new EmailMessageRequest
        {
            To = "<EMAIL>",
            Subject = "Test Subject",
            Body = "Test Body"
        };

        _mockLoggingService
            .Setup(x => x.LogNotificationAsync(It.IsAny<NotificationType>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.SendEmail(request);

        // Assert
        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(500, statusResult.StatusCode);

        var response = Assert.IsType<ApiResponse<object>>(statusResult.Value);
        Assert.False(response.Success);
        Assert.Equal("Internal server error", response.Message);
        Assert.NotNull(response.Data);
    }

    [Theory]
    [InlineData("", "Subject", "Body")] // Empty To
    [InlineData("invalid-email", "Subject", "Body")] // Invalid email format
    [InlineData("<EMAIL>", "", "Body")] // Empty Subject
    [InlineData("<EMAIL>", "Subject", "")] // Empty Body
    public async Task SendEmail_InvalidRequest_ShouldFailValidation(string to, string subject, string body)
    {
        // Arrange
        var request = new EmailMessageRequest
        {
            To = to,
            Subject = subject,
            Body = body
        };

        // Note: In a real scenario, model validation would be handled by ASP.NET Core
        // This test demonstrates the expected behavior
        
        // Act & Assert
        // In actual implementation, validation attributes would prevent this from reaching the controller
        // This test serves as documentation of expected validation behavior
        Assert.True(string.IsNullOrEmpty(to) || string.IsNullOrEmpty(subject) || string.IsNullOrEmpty(body) || !to.Contains("@"));
    }

    #endregion

    #region SendEmailDirect Tests

    [Fact]
    public async Task SendEmailDirect_ValidRequest_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new EmailMessageRequest
        {
            To = "<EMAIL>",
            Subject = "Urgent Test",
            Body = "Urgent message"
        };

        var gatewayResult = new EmailResponse(true, null, "direct-message-id");

        _mockEmailGateway
            .Setup(x => x.SendEmailAsync(request))
            .ReturnsAsync(gatewayResult);

        _mockEmailGateway
            .Setup(x => x.GetCurrentProvider())
            .Returns("TestProvider");

        // Act
        var result = await _controller.SendEmailDirect(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<ApiResponse<EmailSendResult>>(okResult.Value);
        
        Assert.True(response.Success);
        Assert.Equal("Email sent successfully", response.Message);
        Assert.Equal("direct-message-id", response.Data.MessageId);
        Assert.Equal("TestProvider", response.Data.Provider);
        Assert.Equal("Sent", response.Data.Status);
        Assert.Equal(request.To, response.Data.To);
        Assert.Equal(request.Subject, response.Data.Subject);
        Assert.Empty(response.Data.QueueId); // Direct send doesn't use queue

        _mockEmailGateway.Verify(x => x.SendEmailAsync(request), Times.Once);
    }

    [Fact]
    public async Task SendEmailDirect_GatewayFails_ReturnsBadRequest()
    {
        // Arrange
        var request = new EmailMessageRequest
        {
            To = "<EMAIL>",
            Subject = "Test",
            Body = "Test"
        };

        var gatewayResult = new EmailResponse(false, "Provider error", null);

        _mockEmailGateway
            .Setup(x => x.SendEmailAsync(request))
            .ReturnsAsync(gatewayResult);

        // Act
        var result = await _controller.SendEmailDirect(request);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        var response = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);

        Assert.False(response.Success);
        Assert.Equal("Email sending failed", response.Message);
        Assert.NotNull(response.Data);
    }

    [Fact]
    public async Task SendEmailDirect_GatewayThrows_ReturnsInternalServerError()
    {
        // Arrange
        var request = new EmailMessageRequest
        {
            To = "<EMAIL>",
            Subject = "Test",
            Body = "Test"
        };

        _mockEmailGateway
            .Setup(x => x.SendEmailAsync(request))
            .ThrowsAsync(new Exception("Network error"));

        // Act
        var result = await _controller.SendEmailDirect(request);

        // Assert
        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(500, statusResult.StatusCode);
        
        var response = Assert.IsType<ApiResponse<object>>(statusResult.Value);
        Assert.False(response.Success);
        Assert.Equal("Internal server error", response.Message);
        Assert.NotNull(response.Data);
    }

    #endregion

    #region Authentication Tests

    [Fact]
    public void Controller_HasAuthorizeAttribute()
    {
        // Arrange & Act
        var authorizeAttributes = _controller.GetType().GetCustomAttributes(typeof(Microsoft.AspNetCore.Authorization.AuthorizeAttribute), false);

        // Assert
        Assert.NotEmpty(authorizeAttributes);
    }

    [Fact]
    public void SendEmail_HasCorrectHttpPostAttribute()
    {
        // Arrange & Act
        var method = _controller.GetType().GetMethod(nameof(EmailController.SendEmail));
        var httpPostAttributes = method?.GetCustomAttributes(typeof(HttpPostAttribute), false);

        // Assert
        Assert.NotNull(method);
        Assert.NotEmpty(httpPostAttributes);
    }

    #endregion

    #region Performance Tests

    [Fact]
    public async Task SendEmail_PerformanceTest_CompletesWithinTimeout()
    {
        // Arrange
        var request = new EmailMessageRequest
        {
            To = "<EMAIL>",
            Subject = "Performance Test",
            Body = "Testing response time"
        };

        _mockLoggingService
            .Setup(x => x.LogNotificationAsync(It.IsAny<NotificationType>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync("test-id");

        _mockQueueService
            .Setup(x => x.QueueNotificationAsync(It.IsAny<NotificationQueueItem>()))
            .ReturnsAsync("queue-id");

        var startTime = DateTime.UtcNow;

        // Act
        var result = await _controller.SendEmail(request);

        // Assert
        var endTime = DateTime.UtcNow;
        var duration = endTime - startTime;
        
        Assert.True(duration.TotalMilliseconds < 200, $"SendEmail took {duration.TotalMilliseconds}ms, expected < 200ms");
        Assert.IsType<OkObjectResult>(result);
    }

    #endregion

    #endregion
}
