# Azure DevOps Deployment Template for NotificationService
# Supports fallback-aware deployment with comprehensive health checks

parameters:
- name: environmentName
  type: string
- name: azureSubscription
  type: string
- name: appServiceName
  type: string
- name: containerImage
  type: string
- name: redisConnectionString
  type: string
- name: databaseConnectionString
  type: string

steps:
- download: current
  artifact: drop

- task: AzureWebAppContainer@1
  displayName: 'Deploy to Azure App Service'
  inputs:
    azureSubscription: '${{ parameters.azureSubscription }}'
    appName: '${{ parameters.appServiceName }}'
    containers: '${{ parameters.containerImage }}'
    appSettings: |
      -ASPNETCORE_ENVIRONMENT ${{ parameters.environmentName }}
      -CONNECTIONSTRINGS__REDIS "${{ parameters.redisConnectionString }}"
      -CONNECTIONSTRINGS__DEFAULTCONNECTION "${{ parameters.databaseConnectionString }}"
      -PLUGINSETTINGS__PLUGINDIRECTORY "/home/<USER>/wwwroot/plugins"
      -PLUGINSETTINGS__AUTOLOADPLUGINS "true"
      -PLUGINSETTINGS__ENABLEHOTRELOAD "false"
      -LOGGING__LOGLEVEL__DEFAULT "Information"
      -LOGGING__LOGLEVEL__NOTIFYMASTERAPI__SERVICES "Information"

- task: AzureCLI@2
  displayName: 'Wait for Application Startup'
  inputs:
    azureSubscription: '${{ parameters.azureSubscription }}'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      echo "Waiting for application to start..."
      
      # Get app URL
      APP_URL=$(az webapp show --name ${{ parameters.appServiceName }} --resource-group $(resourceGroupName) --query defaultHostName -o tsv)
      echo "App URL: https://$APP_URL"
      
      # Wait for app to be responsive (max 5 minutes)
      for i in {1..30}; do
        echo "Attempt $i/30: Checking if app is responsive..."
        
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$APP_URL/health/live || echo "000")
        
        if [ "$HTTP_STATUS" = "200" ]; then
          echo "✅ Application is responsive!"
          break
        elif [ "$HTTP_STATUS" = "503" ]; then
          echo "⚠️  Application is starting (503)..."
        else
          echo "⏳ Application not ready yet (HTTP: $HTTP_STATUS)..."
        fi
        
        if [ $i -eq 30 ]; then
          echo "❌ Application failed to start within timeout"
          exit 1
        fi
        
        sleep 10
      done

- task: AzureCLI@2
  displayName: 'Run Health Checks'
  inputs:
    azureSubscription: '${{ parameters.azureSubscription }}'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      # Get app URL
      APP_URL=$(az webapp show --name ${{ parameters.appServiceName }} --resource-group $(resourceGroupName) --query defaultHostName -o tsv)
      
      echo "🔍 Running comprehensive health checks..."
      
      # 1. Liveness check
      echo "1. Testing liveness endpoint..."
      LIVE_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$APP_URL/health/live)
      if [ $LIVE_STATUS -eq 200 ]; then
        echo "✅ Liveness check passed"
      else
        echo "❌ Liveness check failed (HTTP: $LIVE_STATUS)"
        exit 1
      fi
      
      # 2. Readiness check
      echo "2. Testing readiness endpoint..."
      READY_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$APP_URL/health/ready)
      if [ $READY_STATUS -eq 200 ]; then
        echo "✅ Readiness check passed"
      else
        echo "⚠️  Readiness check returned: $READY_STATUS (may be using fallbacks)"
      fi
      
      # 3. System status check
      echo "3. Checking system status..."
      SYSTEM_RESPONSE=$(curl -s https://$APP_URL/api/systemstatus)
      
      if [ $? -eq 0 ]; then
        echo "✅ System status endpoint accessible"
        
        # Parse system status
        APP_NAME=$(echo "$SYSTEM_RESPONSE" | jq -r '.application.name // "unknown"')
        APP_VERSION=$(echo "$SYSTEM_RESPONSE" | jq -r '.application.version // "unknown"')
        HEALTH_STATUS=$(echo "$SYSTEM_RESPONSE" | jq -r '.health.status // "unknown"')
        
        echo "   Application: $APP_NAME v$APP_VERSION"
        echo "   Health Status: $HEALTH_STATUS"
        
        # Check for fallbacks
        FALLBACKS_ACTIVE=$(echo "$SYSTEM_RESPONSE" | jq -r '.fallbacks.anyFallbackActive // false')
        
        if [ "$FALLBACKS_ACTIVE" = "true" ]; then
          echo "⚠️  Warning: Fallback systems are active"
          
          # Get fallback details
          REDIS_FALLBACK=$(echo "$SYSTEM_RESPONSE" | jq -r '.fallbacks.redisUsingFallback // false')
          DB_FALLBACK=$(echo "$SYSTEM_RESPONSE" | jq -r '.fallbacks.databaseUsingFallback // false')
          
          if [ "$REDIS_FALLBACK" = "true" ]; then
            echo "   🔄 Redis using in-memory fallback"
          fi
          
          if [ "$DB_FALLBACK" = "true" ]; then
            echo "   🔄 Database using in-memory fallback"
          fi
          
          # Get recommendations
          echo "   📋 Getting fallback recommendations..."
          curl -s https://$APP_URL/api/systemstatus/fallbacks | jq -r '.Summary.Recommendations[]' | while read -r rec; do
            echo "   💡 $rec"
          done
        else
          echo "✅ All systems operational - no fallbacks active"
        fi
      else
        echo "❌ System status endpoint not accessible"
        exit 1
      fi
      
      # 4. API functionality test
      echo "4. Testing API functionality..."
      SWAGGER_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$APP_URL/swagger/index.html)
      if [ $SWAGGER_STATUS -eq 200 ]; then
        echo "✅ Swagger documentation accessible"
      else
        echo "⚠️  Swagger documentation not accessible (HTTP: $SWAGGER_STATUS)"
      fi
      
      # 5. Plugin system check
      echo "5. Checking plugin system..."
      PLUGIN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$APP_URL/api/admin/plugins)
      if [ $PLUGIN_STATUS -eq 200 ]; then
        echo "✅ Plugin system accessible"
        
        # Get plugin count
        PLUGIN_COUNT=$(curl -s https://$APP_URL/api/admin/plugins | jq '. | length')
        echo "   📦 Loaded plugins: $PLUGIN_COUNT"
      else
        echo "⚠️  Plugin system not accessible (HTTP: $PLUGIN_STATUS)"
      fi
      
      echo ""
      echo "🎉 Health checks completed!"
      echo "🌐 Application URL: https://$APP_URL"
      echo "📚 API Documentation: https://$APP_URL/swagger"
      echo "📊 System Status: https://$APP_URL/api/systemstatus"

- task: AzureCLI@2
  displayName: 'Performance Baseline Test'
  condition: eq('${{ parameters.environmentName }}', 'production')
  inputs:
    azureSubscription: '${{ parameters.azureSubscription }}'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      # Get app URL
      APP_URL=$(az webapp show --name ${{ parameters.appServiceName }} --resource-group $(resourceGroupName) --query defaultHostName -o tsv)
      
      echo "🚀 Running performance baseline tests..."
      
      # Test response times
      echo "Testing API response times..."
      
      for endpoint in "health/live" "health/ready" "api/systemstatus"; do
        echo "Testing /$endpoint..."
        
        RESPONSE_TIME=$(curl -s -o /dev/null -w "%{time_total}" https://$APP_URL/$endpoint)
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$APP_URL/$endpoint)
        
        echo "  HTTP Status: $HTTP_STATUS"
        echo "  Response Time: ${RESPONSE_TIME}s"
        
        # Check if response time is acceptable (< 2 seconds)
        if (( $(echo "$RESPONSE_TIME < 2.0" | bc -l) )); then
          echo "  ✅ Response time acceptable"
        else
          echo "  ⚠️  Response time high: ${RESPONSE_TIME}s"
        fi
        
        echo ""
      done

- task: AzureCLI@2
  displayName: 'Setup Application Insights'
  condition: eq('${{ parameters.environmentName }}', 'production')
  inputs:
    azureSubscription: '${{ parameters.azureSubscription }}'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      echo "Setting up Application Insights monitoring..."
      
      # Create Application Insights if it doesn't exist
      AI_NAME="${{ parameters.appServiceName }}-insights"
      
      AI_EXISTS=$(az monitor app-insights component show --app $AI_NAME --resource-group $(resourceGroupName) --query name -o tsv 2>/dev/null || echo "")
      
      if [ -z "$AI_EXISTS" ]; then
        echo "Creating Application Insights: $AI_NAME"
        az monitor app-insights component create \
          --app $AI_NAME \
          --location "East US" \
          --resource-group $(resourceGroupName) \
          --application-type web
      else
        echo "Application Insights already exists: $AI_NAME"
      fi
      
      # Get instrumentation key
      INSTRUMENTATION_KEY=$(az monitor app-insights component show --app $AI_NAME --resource-group $(resourceGroupName) --query instrumentationKey -o tsv)
      
      # Update app service with Application Insights
      az webapp config appsettings set \
        --name ${{ parameters.appServiceName }} \
        --resource-group $(resourceGroupName) \
        --settings APPLICATIONINSIGHTS_CONNECTION_STRING="InstrumentationKey=$INSTRUMENTATION_KEY"
      
      echo "✅ Application Insights configured"
      echo "📊 Instrumentation Key: $INSTRUMENTATION_KEY"

- task: PowerShell@2
  displayName: 'Send Deployment Notification'
  inputs:
    targetType: 'inline'
    script: |
      $environmentName = "${{ parameters.environmentName }}"
      $appServiceName = "${{ parameters.appServiceName }}"
      $buildId = "$(Build.BuildId)"
      $buildNumber = "$(Build.BuildNumber)"
      
      Write-Host "🚀 Deployment completed successfully!"
      Write-Host "Environment: $environmentName"
      Write-Host "App Service: $appServiceName"
      Write-Host "Build ID: $buildId"
      Write-Host "Build Number: $buildNumber"
      Write-Host "Image: ${{ parameters.containerImage }}"
      
      # You can add webhook notifications here
      # Example: Teams, Slack, email notifications
