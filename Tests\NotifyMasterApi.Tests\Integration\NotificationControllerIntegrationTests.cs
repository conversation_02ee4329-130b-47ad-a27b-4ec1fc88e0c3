using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using NotifyMasterApi.Interfaces;
using NotifyMasterApi.Services;
using SmsContract.Models;
using EmailContract.Models;
using PushNotificationContract.Models;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace NotifyMasterApi.Tests.Integration;

public class NotificationControllerIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public NotificationControllerIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Replace the plugin manager with a mock for testing
                services.AddScoped<IPluginManager>(provider =>
                {
                    var mockPluginManager = new Mock<IPluginManager>();
                    var logger = provider.GetRequiredService<ILogger<RuntimePluginManager>>();
                    
                    // Setup mock plugin manager behavior
                    SetupMockPluginManager(mockPluginManager);
                    
                    return mockPluginManager.Object;
                });
            });
        });

        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task SendSms_WithValidRequest_ReturnsSuccess()
    {
        // Arrange
        var request = new SendSmsRequest
        {
            PhoneNumber = "+**********",
            Message = "Test SMS message"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/sms/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<SmsResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        
        result.Should().NotBeNull();
        result!.IsSuccess.Should().BeTrue();
        result.MessageId.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task SendSms_WithInvalidPhoneNumber_ReturnsBadRequest()
    {
        // Arrange
        var request = new SendSmsRequest
        {
            PhoneNumber = "invalid-phone",
            Message = "Test SMS message"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/sms/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SendEmail_WithValidRequest_ReturnsSuccess()
    {
        // Arrange
        var request = new SendEmailRequest
        {
            To = "<EMAIL>",
            Subject = "Test Email",
            Body = "This is a test email",
            From = "<EMAIL>"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<EmailResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        
        result.Should().NotBeNull();
        result!.IsSuccess.Should().BeTrue();
        result.MessageId.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task SendEmail_WithInvalidEmailAddress_ReturnsBadRequest()
    {
        // Arrange
        var request = new SendEmailRequest
        {
            To = "invalid-email",
            Subject = "Test Email",
            Body = "This is a test email",
            From = "<EMAIL>"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SendPush_WithValidRequest_ReturnsSuccess()
    {
        // Arrange
        var request = new SendPushMessageRequest
        {
            DeviceToken = "test-device-token-123",
            Title = "Test Push Notification",
            Message = "This is a test push notification",
            Data = new Dictionary<string, string>
            {
                { "key1", "value1" },
                { "key2", "value2" }
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/push/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<PushNotificationResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        
        result.Should().NotBeNull();
        result!.IsSuccess.Should().BeTrue();
        result.MessageId.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task SendBulkSms_WithValidRequest_ReturnsSuccess()
    {
        // Arrange
        var request = new BulkSmsRequest
        {
            Messages = new List<SmsMessageRequest>
            {
                new() { PhoneNumber = "+**********", Message = "Message 1" },
                new() { PhoneNumber = "+1987654321", Message = "Message 2" }
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/sms/send-bulk", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<BulkSmsResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        
        result.Should().NotBeNull();
        result!.IsSuccess.Should().BeTrue();
        result.SuccessCount.Should().Be(2);
        result.FailureCount.Should().Be(0);
    }

    [Fact]
    public async Task GetSmsStatus_WithValidMessageId_ReturnsStatus()
    {
        // Arrange
        var messageId = "test-message-123";

        // Act
        var response = await _client.GetAsync($"/sms/status/{messageId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<MessageStatusResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        
        result.Should().NotBeNull();
        result!.IsSuccess.Should().BeTrue();
        result.Status.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GetPlugins_ReturnsListOfPlugins()
    {
        // Act
        var response = await _client.GetAsync("/admin/plugins");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<List<PluginContract.Models.PluginInfo>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        
        result.Should().NotBeNull();
        result!.Should().HaveCountGreaterThan(0);
    }

    [Fact]
    public async Task LoadPlugin_WithValidPath_ReturnsSuccess()
    {
        // Arrange
        var request = new { PluginPath = "/test/path/plugin.dll" };

        // Act
        var response = await _client.PostAsJsonAsync("/admin/plugins/load", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task UnloadPlugin_WithValidPluginName_ReturnsSuccess()
    {
        // Arrange
        var pluginName = "Test Plugin";

        // Act
        var response = await _client.PostAsync($"/admin/plugins/{pluginName}/unload", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task HealthCheck_ReturnsHealthStatus()
    {
        // Act
        var response = await _client.GetAsync("/health");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GetEmailMetrics_ReturnsMetrics()
    {
        // Act
        var response = await _client.GetAsync("/metrics/email");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GetSmsMetrics_ReturnsMetrics()
    {
        // Act
        var response = await _client.GetAsync("/metrics/sms");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task SendSms_WithPreferredProvider_UsesSpecifiedProvider()
    {
        // Arrange
        var request = new SendSmsRequest
        {
            PhoneNumber = "+**********",
            Message = "Test SMS message"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/sms/send?provider=Twilio", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<SmsResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        
        result.Should().NotBeNull();
        result!.IsSuccess.Should().BeTrue();
    }

    private static void SetupMockPluginManager(Mock<IPluginManager> mockPluginManager)
    {
        // Setup GetLoadedPluginsAsync
        mockPluginManager.Setup(x => x.GetLoadedPluginsAsync())
            .ReturnsAsync(new List<PluginContract.Models.PluginInfo>
            {
                new("Twilio SMS Plugin", "1.0.0", "SMS plugin for Twilio", PluginContract.Enums.PluginType.Sms, "Test Author", true),
                new("SendGrid Email Plugin", "1.0.0", "Email plugin for SendGrid", PluginContract.Enums.PluginType.Email, "Test Author", true),
                new("Firebase Push Plugin", "1.0.0", "Push plugin for Firebase", PluginContract.Enums.PluginType.PushNotification, "Test Author", true)
            });

        // Setup LoadPluginAsync
        mockPluginManager.Setup(x => x.LoadPluginAsync(It.IsAny<string>()))
            .ReturnsAsync(true);

        // Setup UnloadPluginAsync
        mockPluginManager.Setup(x => x.UnloadPluginAsync(It.IsAny<string>()))
            .ReturnsAsync(true);

        // Setup EnablePluginAsync
        mockPluginManager.Setup(x => x.EnablePluginAsync(It.IsAny<string>()))
            .ReturnsAsync(true);

        // Setup DisablePluginAsync
        mockPluginManager.Setup(x => x.DisablePluginAsync(It.IsAny<string>()))
            .ReturnsAsync(true);

        // Setup GetPluginAsync
        mockPluginManager.Setup(x => x.GetPluginAsync(It.IsAny<string>()))
            .ReturnsAsync(new PluginContract.Models.PluginInfo("Test Plugin", "1.0.0", "Test plugin", PluginContract.Enums.PluginType.Sms, "Test Author", true));
    }
}
