###############################################################################
# 🛡️ Ultimate .gitignore for C# / .NET Projects
# Optimized for: .NET SDKs, ASP.NET Core, Xamarin, Blazor, MAUI, WinForms/WPF,
#                Visual Studio, Rider, VS Code, Azure, Docker, CI/CD pipelines.
###############################################################################

###############
# 🔧 Build Outputs
###############
[Bb]in/
[Oo]bj/
[Ll]og/
artifacts/
*.dll
*.exe
*.app
*.pdb
*.mdb
*.cache
*.tlb
*.tlh
*.tmp
*.log
*.bak
*.old

###############
# 🧑‍💻 IDE & Editor Config
###############
.vscode/
.idea/
*.code-workspace
*.sln.iml
*.user
*.suo
*.vssettings
*.userosscache
*.sln.docstates

###############
# 🧠 Visual Studio / Rider / ReSharper
###############
.vs/
_ReSharper*/
*.ReSharper
*.DotSettings.user
.idea/
.idea_modules/

###############
# 📦 NuGet & SDK
###############
*.nupkg
*.snupkg
*.nuspec
*.deps.json
*.runtimeconfig.json
.nuget/
packages/
project.lock.json
project.assets.json
*.props
*.targets

###############
# 🌍 Web Assets
###############
node_modules/
bower_components/
wwwroot/dist/
wwwroot/lib/
wwwroot/node_modules/
ClientApp/node_modules/

###############
# ⚙️ Config & Secrets
###############
*.env
.env.*
*.secrets.json
*.user.config
secrets.*
appsettings.Development.json
appsettings.Local.json
appsettings.*.json.user
*.key
*.pem
*.pfx
*.p12
*.crt
*.jwk

###############
# ☁️ Azure & Publish
###############
*.azurePubxml
*.azureProj
*.publish.xml
*.pubxml
*.publishproj
*.deploy.cmd
*.deploy-readme.txt
.publish/
PublishProfiles/

###############
# 📚 Entity Framework / DB / ORM
###############
*.edmx*
*.designer.cs
Migrations/
DatabaseGenerated/
*.db
*.sqlite*
*.sdf
*.mdf
*.ldf
*.bak

###############
# 🔬 Test Artifacts & Coverage
###############
TestResults/
coverage/
*.coverage
*.coveragexml
*.trx
*.testsettings
*.vsmdi
*.vspx
*.vsp
*.nunit.*
*.cobertura.xml
.nyc_output/

###############
# 🐳 Docker / Containerization
###############
docker-compose.override.*
docker-compose.local.*
.dockerignore
Dockerfile*
.docker/
*.pid
*.seed
*.sock

###############
# ✨ Xamarin / MAUI / Mobile
###############
*.apk
*.ap_*
*.aab
*.ipa
*.xcuserstate
*.xcworkspace
*.xcodeproj/
*.plist
*.dsym/
*.xcassets/

###############
# 🎨 Designer Generated Files
###############
*.g.cs
*.g.i.cs
*.AssemblyInfo.cs
Generated_Code/
*.designer.cs
*.resx
*.resources

###############
# 🧪 Build/Test Engines
###############
*.ncrunch*
*.vshost.*
*.psess
*.vsp
*.vspx

###############
# 🗂️ Miscellaneous
###############
*.tmp
*.temp
*.orig
*.swp
*.swo
*.err
*.ipch
*.pidb
*.sdf
*.dbmdl

###############
# 🖥️ OS & Filesystem Junk
###############
.DS_Store
Thumbs.db
ehthumbs.db
desktop.ini
Icon?
$RECYCLE.BIN/
._*

###############
# ✅ Explicit Keeps (Un-ignore)
###############
!**/.gitkeep
!**/README.md
!**/LICENSE
!**/*.example
!appsettings.json
