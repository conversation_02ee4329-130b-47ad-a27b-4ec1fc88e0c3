# NotificationService Test Runner Script
# This script runs the complete test suite for the NotificationService

param(
    [string]$TestType = "all",
    [string]$Configuration = "Debug",
    [switch]$Coverage = $false,
    [switch]$Performance = $false,
    [switch]$Verbose = $false
)

Write-Host "🧪 NotificationService Test Runner" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$RootDir = Split-Path -Parent $ScriptDir

# Test project paths
$TestProjects = @(
    "$RootDir/Tests/NotifyMasterApi.Tests/NotifyMasterApi.Tests.csproj"
    "$RootDir/Tests/Libraries/EmailService.Library.Tests/EmailService.Library.Tests.csproj"
    "$RootDir/Tests/Libraries/SmsService.Library.Tests/SmsService.Library.Tests.csproj"
)

# Function to run tests with optional coverage
function Run-Tests {
    param(
        [string]$ProjectPath,
        [string]$TestFilter = "",
        [bool]$CollectCoverage = $false
    )
    
    $ProjectName = Split-Path -Leaf (Split-Path -Parent $ProjectPath)
    Write-Host "🔍 Running tests for $ProjectName..." -ForegroundColor Yellow
    
    $TestArgs = @(
        "test"
        $ProjectPath
        "--configuration", $Configuration
        "--logger", "console;verbosity=normal"
    )
    
    if ($TestFilter) {
        $TestArgs += "--filter", $TestFilter
    }
    
    if ($Verbose) {
        $TestArgs += "--verbosity", "detailed"
    }
    
    if ($CollectCoverage) {
        $TestArgs += "--collect", "XPlat Code Coverage"
        $TestArgs += "--results-directory", "$RootDir/TestResults"
    }
    
    try {
        & dotnet @TestArgs
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $ProjectName tests passed" -ForegroundColor Green
        } else {
            Write-Host "❌ $ProjectName tests failed" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error running $ProjectName tests: $_" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# Function to run performance tests
function Run-PerformanceTests {
    Write-Host "🚀 Running performance tests..." -ForegroundColor Yellow
    
    # Start the API in background for performance testing
    Write-Host "Starting NotificationService API..." -ForegroundColor Gray
    $ApiProcess = Start-Process -FilePath "dotnet" -ArgumentList @(
        "run",
        "--project", "$RootDir/src/Services/NotifyMasterApi/NotifyMasterApi.csproj",
        "--configuration", $Configuration
    ) -PassThru -WindowStyle Hidden
    
    # Wait for API to start
    Start-Sleep -Seconds 10
    
    try {
        # Run performance tests
        $PerfTestFilter = "Category=Performance"
        $Success = Run-Tests -ProjectPath $TestProjects[0] -TestFilter $PerfTestFilter -CollectCoverage $Coverage
        
        return $Success
    }
    finally {
        # Stop the API
        if ($ApiProcess -and !$ApiProcess.HasExited) {
            Write-Host "Stopping NotificationService API..." -ForegroundColor Gray
            Stop-Process -Id $ApiProcess.Id -Force
        }
    }
}

# Function to generate coverage report
function Generate-CoverageReport {
    Write-Host "📊 Generating coverage report..." -ForegroundColor Yellow
    
    $CoverageFiles = Get-ChildItem -Path "$RootDir/TestResults" -Filter "coverage.cobertura.xml" -Recurse
    
    if ($CoverageFiles.Count -eq 0) {
        Write-Host "⚠️ No coverage files found" -ForegroundColor Yellow
        return
    }
    
    # Install ReportGenerator if not present
    $ReportGenerator = Get-Command "reportgenerator" -ErrorAction SilentlyContinue
    if (-not $ReportGenerator) {
        Write-Host "Installing ReportGenerator..." -ForegroundColor Gray
        dotnet tool install -g dotnet-reportgenerator-globaltool
    }
    
    # Generate HTML report
    $CoverageArgs = @(
        "-reports:$($CoverageFiles[0].FullName)"
        "-targetdir:$RootDir/TestResults/CoverageReport"
        "-reporttypes:Html;Badges"
    )
    
    & reportgenerator @CoverageArgs
    
    Write-Host "✅ Coverage report generated at: $RootDir/TestResults/CoverageReport/index.html" -ForegroundColor Green
}

# Main execution
try {
    # Clean previous test results
    if (Test-Path "$RootDir/TestResults") {
        Remove-Item -Path "$RootDir/TestResults" -Recurse -Force
    }
    New-Item -Path "$RootDir/TestResults" -ItemType Directory -Force | Out-Null
    
    # Build solution first
    Write-Host "🔨 Building solution..." -ForegroundColor Yellow
    dotnet build "$RootDir/src/NotificationService.sln" --configuration $Configuration
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Host "✅ Build successful" -ForegroundColor Green
    
    $AllTestsPassed = $true
    
    # Run tests based on type
    switch ($TestType.ToLower()) {
        "unit" {
            Write-Host "🧪 Running unit tests only..." -ForegroundColor Cyan
            foreach ($Project in $TestProjects) {
                $Success = Run-Tests -ProjectPath $Project -TestFilter "Category!=Integration&Category!=Performance" -CollectCoverage $Coverage
                $AllTestsPassed = $AllTestsPassed -and $Success
            }
        }
        
        "integration" {
            Write-Host "🔗 Running integration tests only..." -ForegroundColor Cyan
            $Success = Run-Tests -ProjectPath $TestProjects[0] -TestFilter "Category=Integration" -CollectCoverage $Coverage
            $AllTestsPassed = $AllTestsPassed -and $Success
        }
        
        "performance" {
            Write-Host "🚀 Running performance tests only..." -ForegroundColor Cyan
            $Success = Run-PerformanceTests
            $AllTestsPassed = $AllTestsPassed -and $Success
        }
        
        "all" {
            Write-Host "🎯 Running all tests..." -ForegroundColor Cyan
            
            # Unit tests
            Write-Host "`n📋 Phase 1: Unit Tests" -ForegroundColor Magenta
            foreach ($Project in $TestProjects) {
                $Success = Run-Tests -ProjectPath $Project -TestFilter "Category!=Integration&Category!=Performance" -CollectCoverage $Coverage
                $AllTestsPassed = $AllTestsPassed -and $Success
            }
            
            # Integration tests
            Write-Host "`n📋 Phase 2: Integration Tests" -ForegroundColor Magenta
            $Success = Run-Tests -ProjectPath $TestProjects[0] -TestFilter "Category=Integration" -CollectCoverage $Coverage
            $AllTestsPassed = $AllTestsPassed -and $Success
            
            # Performance tests (if requested)
            if ($Performance) {
                Write-Host "`n📋 Phase 3: Performance Tests" -ForegroundColor Magenta
                $Success = Run-PerformanceTests
                $AllTestsPassed = $AllTestsPassed -and $Success
            }
        }
        
        default {
            throw "Invalid test type: $TestType. Valid options: unit, integration, performance, all"
        }
    }
    
    # Generate coverage report if requested
    if ($Coverage) {
        Generate-CoverageReport
    }
    
    # Final results
    Write-Host "`n🎯 Test Results Summary" -ForegroundColor Cyan
    Write-Host "======================" -ForegroundColor Cyan
    
    if ($AllTestsPassed) {
        Write-Host "✅ All tests passed successfully!" -ForegroundColor Green
        exit 0
    } else {
        Write-Host "❌ Some tests failed!" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "❌ Test execution failed: $_" -ForegroundColor Red
    exit 1
}

# Usage examples:
# .\run-tests.ps1                          # Run all tests except performance
# .\run-tests.ps1 -TestType unit           # Run only unit tests
# .\run-tests.ps1 -TestType integration    # Run only integration tests
# .\run-tests.ps1 -TestType performance    # Run only performance tests
# .\run-tests.ps1 -Coverage                # Run tests with coverage
# .\run-tests.ps1 -Performance             # Include performance tests in "all"
# .\run-tests.ps1 -Verbose                 # Detailed test output
