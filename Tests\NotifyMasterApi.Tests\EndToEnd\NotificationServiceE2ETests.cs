using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using SmsContract.Models;
using EmailContract.Models;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Testcontainers.PostgreSql;
using Testcontainers.Redis;
using Xunit;
using WireMock.Server;
using WireMock.RequestBuilders;
using WireMock.ResponseBuilders;

namespace NotifyMasterApi.Tests.EndToEnd;

public class NotificationServiceE2ETests : IAsyncLifetime
{
    private readonly PostgreSqlContainer _postgresContainer;
    private readonly RedisContainer _redisContainer;
    private readonly WireMockServer _mockServer;
    private WebApplicationFactory<Program> _factory = null!;
    private HttpClient _client = null!;

    public NotificationServiceE2ETests()
    {
        // Setup PostgreSQL container
        _postgresContainer = new PostgreSqlBuilder()
            .WithImage("postgres:15")
            .WithDatabase("notificationservice_test")
            .WithUsername("test")
            .WithPassword("test123")
            .WithPortBinding(5432, true)
            .Build();

        // Setup Redis container
        _redisContainer = new RedisBuilder()
            .WithImage("redis:7")
            .WithPortBinding(6379, true)
            .Build();

        // Setup WireMock server for external API mocking
        _mockServer = WireMockServer.Start();
    }

    public async Task InitializeAsync()
    {
        // Start containers
        await _postgresContainer.StartAsync();
        await _redisContainer.StartAsync();

        // Setup mock external APIs
        SetupMockExternalApis();

        // Create web application factory with test configuration
        _factory = new WebApplicationFactory<Program>()
            .WithWebHostBuilder(builder =>
            {
                builder.ConfigureAppConfiguration((context, config) =>
                {
                    config.AddInMemoryCollection(new Dictionary<string, string?>
                    {
                        ["ConnectionStrings:DefaultConnection"] = _postgresContainer.GetConnectionString(),
                        ["Redis:ConnectionString"] = _redisContainer.GetConnectionString(),
                        ["Twilio:BaseUrl"] = _mockServer.Url,
                        ["SendGrid:BaseUrl"] = _mockServer.Url,
                        ["Firebase:BaseUrl"] = _mockServer.Url,
                        ["Plugins:Directory"] = Path.Combine(Path.GetTempPath(), "test-plugins")
                    });
                });

                builder.UseEnvironment("Testing");
            });

        _client = _factory.CreateClient();

        // Wait for application to be ready
        await WaitForApplicationReady();
    }

    [Fact]
    public async Task CompleteNotificationWorkflow_SMS_Success()
    {
        // Arrange
        var smsRequest = new SendSmsRequest
        {
            PhoneNumber = "+**********",
            Message = "Integration test SMS message"
        };

        // Act & Assert

        // 1. Send SMS
        var sendResponse = await _client.PostAsJsonAsync("/sms/send", smsRequest);
        sendResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var sendResult = await DeserializeResponse<SmsResponse>(sendResponse);
        sendResult.IsSuccess.Should().BeTrue();
        sendResult.MessageId.Should().NotBeNullOrEmpty();

        // 2. Check SMS status
        var statusResponse = await _client.GetAsync($"/sms/status/{sendResult.MessageId}");
        statusResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var statusResult = await DeserializeResponse<MessageStatusResponse>(statusResponse);
        statusResult.IsSuccess.Should().BeTrue();
        statusResult.Status.Should().NotBeNullOrEmpty();

        // 3. Get SMS metrics
        var metricsResponse = await _client.GetAsync("/metrics/sms");
        metricsResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // 4. Verify database logging
        await VerifyDatabaseLogging("SMS", sendResult.MessageId);
    }

    [Fact]
    public async Task CompleteNotificationWorkflow_Email_Success()
    {
        // Arrange
        var emailRequest = new SendEmailRequest
        {
            To = "<EMAIL>",
            Subject = "Integration Test Email",
            Body = "This is an integration test email",
            From = "<EMAIL>"
        };

        // Act & Assert

        // 1. Send Email
        var sendResponse = await _client.PostAsJsonAsync("/email/send", emailRequest);
        sendResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var sendResult = await DeserializeResponse<EmailResponse>(sendResponse);
        sendResult.IsSuccess.Should().BeTrue();
        sendResult.MessageId.Should().NotBeNullOrEmpty();

        // 2. Check Email status
        var statusResponse = await _client.GetAsync($"/email/status/{sendResult.MessageId}");
        statusResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // 3. Get Email metrics
        var metricsResponse = await _client.GetAsync("/metrics/email");
        metricsResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // 4. Verify database logging
        await VerifyDatabaseLogging("Email", sendResult.MessageId);
    }

    [Fact]
    public async Task PluginManagement_LoadUnloadCycle_Success()
    {
        // Act & Assert

        // 1. Get initial plugin list
        var initialPluginsResponse = await _client.GetAsync("/admin/plugins");
        initialPluginsResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var initialPlugins = await DeserializeResponse<List<PluginContract.Models.PluginInfo>>(initialPluginsResponse);
        var initialCount = initialPlugins.Count;

        // 2. Load a new plugin (mock)
        var loadRequest = new { PluginPath = "/test/mock-plugin.dll" };
        var loadResponse = await _client.PostAsJsonAsync("/admin/plugins/load", loadRequest);
        loadResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // 3. Verify plugin was loaded
        var afterLoadResponse = await _client.GetAsync("/admin/plugins");
        var afterLoadPlugins = await DeserializeResponse<List<PluginContract.Models.PluginInfo>>(afterLoadResponse);
        afterLoadPlugins.Count.Should().BeGreaterThan(initialCount);

        // 4. Disable plugin
        var pluginName = afterLoadPlugins.First().Name;
        var disableResponse = await _client.PostAsync($"/admin/plugins/{pluginName}/disable", null);
        disableResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // 5. Enable plugin
        var enableResponse = await _client.PostAsync($"/admin/plugins/{pluginName}/enable", null);
        enableResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // 6. Unload plugin
        var unloadResponse = await _client.PostAsync($"/admin/plugins/{pluginName}/unload", null);
        unloadResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task BulkNotifications_HighVolume_Success()
    {
        // Arrange
        var bulkSmsRequest = new BulkSmsRequest
        {
            Messages = Enumerable.Range(1, 100).Select(i => new SmsMessageRequest
            {
                PhoneNumber = $"+123456789{i:D2}",
                Message = $"Bulk test message {i}"
            }).ToList()
        };

        // Act
        var response = await _client.PostAsJsonAsync("/sms/send-bulk", bulkSmsRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await DeserializeResponse<BulkSmsResponse>(response);
        result.IsSuccess.Should().BeTrue();
        result.SuccessCount.Should().Be(100);
        result.FailureCount.Should().Be(0);
        result.Results.Should().HaveCount(100);
    }

    [Fact]
    public async Task HealthMonitoring_AllEndpoints_Success()
    {
        // Act & Assert

        // 1. Overall health check
        var healthResponse = await _client.GetAsync("/health");
        healthResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // 2. Detailed health check
        var detailedHealthResponse = await _client.GetAsync("/health/detailed");
        detailedHealthResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // 3. Plugin health check
        var pluginHealthResponse = await _client.GetAsync("/health/plugins");
        pluginHealthResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task ErrorHandling_InvalidRequests_ProperErrorResponses()
    {
        // Test invalid SMS request
        var invalidSmsRequest = new SendSmsRequest
        {
            PhoneNumber = "invalid-phone",
            Message = ""
        };

        var smsResponse = await _client.PostAsJsonAsync("/sms/send", invalidSmsRequest);
        smsResponse.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        // Test invalid email request
        var invalidEmailRequest = new SendEmailRequest
        {
            To = "invalid-email",
            Subject = "",
            Body = "",
            From = "invalid-sender"
        };

        var emailResponse = await _client.PostAsJsonAsync("/email/send", invalidEmailRequest);
        emailResponse.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        // Test non-existent plugin operation
        var nonExistentPluginResponse = await _client.PostAsync("/admin/plugins/NonExistentPlugin/unload", null);
        nonExistentPluginResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task ProviderFailover_WhenPrimaryFails_UsesSecondary()
    {
        // Arrange - Setup primary provider to fail
        SetupProviderFailure("Twilio");

        var smsRequest = new SendSmsRequest
        {
            PhoneNumber = "+**********",
            Message = "Failover test message"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/sms/send", smsRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await DeserializeResponse<SmsResponse>(response);
        result.IsSuccess.Should().BeTrue();
        result.Provider.Should().NotBe("Twilio"); // Should use fallback provider
    }

    private void SetupMockExternalApis()
    {
        // Mock Twilio SMS API
        _mockServer
            .Given(Request.Create().WithPath("/v1/Accounts/*/Messages").UsingPost())
            .RespondWith(Response.Create()
                .WithStatusCode(200)
                .WithHeader("Content-Type", "application/json")
                .WithBody(JsonSerializer.Serialize(new { sid = "test_message_id", status = "sent" })));

        // Mock SendGrid Email API
        _mockServer
            .Given(Request.Create().WithPath("/v3/mail/send").UsingPost())
            .RespondWith(Response.Create()
                .WithStatusCode(202)
                .WithHeader("X-Message-Id", "test_email_id"));

        // Mock Firebase Push API
        _mockServer
            .Given(Request.Create().WithPath("/v1/projects/*/messages:send").UsingPost())
            .RespondWith(Response.Create()
                .WithStatusCode(200)
                .WithHeader("Content-Type", "application/json")
                .WithBody(JsonSerializer.Serialize(new { name = "test_push_id" })));
    }

    private void SetupProviderFailure(string provider)
    {
        // Setup specific provider to return error responses
        if (provider == "Twilio")
        {
            _mockServer
                .Given(Request.Create().WithPath("/v1/Accounts/*/Messages").UsingPost())
                .RespondWith(Response.Create()
                    .WithStatusCode(500)
                    .WithBody("Internal Server Error"));
        }
    }

    private async Task<T> DeserializeResponse<T>(HttpResponseMessage response)
    {
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<T>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        })!;
    }

    private async Task WaitForApplicationReady()
    {
        var maxAttempts = 30;
        var attempt = 0;

        while (attempt < maxAttempts)
        {
            try
            {
                var response = await _client.GetAsync("/health");
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    return;
                }
            }
            catch
            {
                // Ignore exceptions during startup
            }

            await Task.Delay(1000);
            attempt++;
        }

        throw new TimeoutException("Application did not become ready within the expected time");
    }

    private async Task VerifyDatabaseLogging(string notificationType, string messageId)
    {
        // In a real implementation, this would query the database to verify logging
        // For now, we'll just verify the endpoint exists
        var logsResponse = await _client.GetAsync($"/admin/logs?type={notificationType}&messageId={messageId}");
        logsResponse.StatusCode.Should().BeOneOf(HttpStatusCode.OK, HttpStatusCode.NotFound);
    }

    public async Task DisposeAsync()
    {
        _client?.Dispose();
        _factory?.Dispose();
        _mockServer?.Stop();
        _mockServer?.Dispose();
        
        await _postgresContainer.DisposeAsync();
        await _redisContainer.DisposeAsync();
    }
}
