using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace NotifyMasterApi.Tests;

/// <summary>
/// Integration tests for fallback mechanisms in real application scenarios
/// </summary>
public class FallbackIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly ITestOutputHelper _output;

    public FallbackIntegrationTests(WebApplicationFactory<Program> factory, ITestOutputHelper output)
    {
        _factory = factory;
        _output = output;
    }

    [Fact]
    public async Task Application_ShouldStartSuccessfully_WithAllFallbacksActive()
    {
        // Arrange & Act
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999", // Invalid Redis
                    ["ConnectionStrings:DefaultConnection"] = "invalid_connection_string" // Invalid DB
                });
            });
            builder.ConfigureLogging(logging =>
            {
                logging.AddXUnit(_output);
            });
        }).CreateClient();

        // Assert - Application should start and respond
        var response = await client.GetAsync("/health/ready");
        Assert.True(response.IsSuccessStatusCode || response.StatusCode == System.Net.HttpStatusCode.ServiceUnavailable);
    }

    [Fact]
    public async Task HealthChecks_ShouldWork_WithFallbacksActive()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999",
                    ["ConnectionStrings:DefaultConnection"] = ""
                });
            });
        }).CreateClient();

        // Act
        var readyResponse = await client.GetAsync("/health/ready");
        var liveResponse = await client.GetAsync("/health/live");

        // Assert
        // Health checks should respond (may be unhealthy but should not crash)
        Assert.NotNull(readyResponse);
        Assert.NotNull(liveResponse);
    }

    [Fact]
    public async Task SystemStatus_ShouldReportAllFallbacks_WhenInfrastructureUnavailable()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999",
                    ["ConnectionStrings:DefaultConnection"] = ""
                });
            });
        }).CreateClient();

        // Act
        var response = await client.GetAsync("/api/systemstatus/fallbacks");
        var content = await response.Content.ReadAsStringAsync();

        // Assert
        response.EnsureSuccessStatusCode();
        
        var jsonDoc = JsonDocument.Parse(content);
        var summary = jsonDoc.RootElement.GetProperty("Summary");
        var anyFallbackActive = summary.GetProperty("AnyFallbackActive").GetBoolean();
        
        Assert.True(anyFallbackActive);
        
        var message = summary.GetProperty("Message").GetString();
        Assert.Contains("fallback", message?.ToLower() ?? "");
    }

    [Fact]
    public async Task RedisOperations_ShouldWork_InFallbackMode()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999"
                });
            });
        }).CreateClient();

        // Act - Test Redis connection
        var testResponse = await client.PostAsync("/api/systemstatus/redis/test", null);
        var testContent = await testResponse.Content.ReadAsStringAsync();

        // Assert
        testResponse.EnsureSuccessStatusCode();
        
        var jsonDoc = JsonDocument.Parse(testContent);
        var testResult = jsonDoc.RootElement.GetProperty("TestResult").GetString();
        var usingFallback = jsonDoc.RootElement.GetProperty("UsingFallback").GetBoolean();
        
        Assert.Equal("Success", testResult);
        Assert.True(usingFallback);
    }

    [Fact]
    public async Task Application_ShouldProvideDetailedFallbackInformation()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999",
                    ["ConnectionStrings:DefaultConnection"] = ""
                });
            });
        }).CreateClient();

        // Act
        var response = await client.GetAsync("/api/systemstatus");
        var content = await response.Content.ReadAsStringAsync();

        // Assert
        response.EnsureSuccessStatusCode();
        
        var jsonDoc = JsonDocument.Parse(content);
        
        // Check application info
        var application = jsonDoc.RootElement.GetProperty("Application");
        Assert.Equal("NotifyMasterApi", application.GetProperty("Name").GetString());
        
        // Check fallback status
        var fallbacks = jsonDoc.RootElement.GetProperty("Fallbacks");
        var anyFallbackActive = fallbacks.GetProperty("AnyFallbackActive").GetBoolean();
        Assert.True(anyFallbackActive);
        
        // Check health status
        var health = jsonDoc.RootElement.GetProperty("Health");
        var status = health.GetProperty("Status").GetString();
        Assert.NotNull(status);
    }

    [Fact]
    public async Task FallbackData_ShouldBeClearable()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999",
                    ["ConnectionStrings:DefaultConnection"] = ""
                });
            });
        }).CreateClient();

        // Act - First, perform some operations to generate data
        await client.PostAsync("/api/systemstatus/redis/test", null);
        
        // Then clear the data
        var clearResponse = await client.PostAsync("/api/systemstatus/fallbacks/clear", null);
        var clearContent = await clearResponse.Content.ReadAsStringAsync();

        // Assert
        clearResponse.EnsureSuccessStatusCode();
        
        var jsonDoc = JsonDocument.Parse(clearContent);
        var message = jsonDoc.RootElement.GetProperty("Message").GetString();
        Assert.Contains("cleared successfully", message);
    }

    [Fact]
    public async Task Application_ShouldHandleMultipleConcurrentRequests_WithFallbacks()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999",
                    ["ConnectionStrings:DefaultConnection"] = ""
                });
            });
        }).CreateClient();

        // Act - Make multiple concurrent requests
        var tasks = new List<Task<HttpResponseMessage>>();
        for (int i = 0; i < 10; i++)
        {
            tasks.Add(client.GetAsync("/api/systemstatus"));
            tasks.Add(client.GetAsync("/api/systemstatus/redis"));
            tasks.Add(client.PostAsync("/api/systemstatus/redis/test", null));
        }

        var responses = await Task.WhenAll(tasks);

        // Assert - All requests should succeed
        foreach (var response in responses)
        {
            Assert.True(response.IsSuccessStatusCode, 
                $"Request failed with status: {response.StatusCode}");
        }
    }

    [Fact]
    public async Task Swagger_ShouldBeAccessible_WithFallbacksActive()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999",
                    ["ConnectionStrings:DefaultConnection"] = ""
                });
            });
        }).CreateClient();

        // Act
        var swaggerResponse = await client.GetAsync("/swagger/index.html");

        // Assert
        // Swagger should be accessible even with fallbacks active
        Assert.True(swaggerResponse.IsSuccessStatusCode || 
                   swaggerResponse.StatusCode == System.Net.HttpStatusCode.NotFound); // Depending on environment
    }

    [Fact]
    public async Task Application_ShouldProvideRecommendations_ForFallbackResolution()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:Redis"] = "localhost:9999",
                    ["ConnectionStrings:DefaultConnection"] = ""
                });
            });
        }).CreateClient();

        // Act
        var response = await client.GetAsync("/api/systemstatus/fallbacks");
        var content = await response.Content.ReadAsStringAsync();

        // Assert
        response.EnsureSuccessStatusCode();
        
        var jsonDoc = JsonDocument.Parse(content);
        var summary = jsonDoc.RootElement.GetProperty("Summary");
        var recommendations = summary.GetProperty("Recommendations");
        
        Assert.True(recommendations.GetArrayLength() > 0);
        
        var recommendationTexts = recommendations.EnumerateArray()
            .Select(r => r.GetString()?.ToLower())
            .ToList();
        
        Assert.Contains(recommendationTexts, r => r?.Contains("redis") == true);
        Assert.Contains(recommendationTexts, r => r?.Contains("database") == true);
    }
}
