using PushNotificationContract.Models;
using PluginContract.Models;
using PluginContract.Interfaces;

namespace PluginCore.Interfaces;
/// <summary>
///  Interface for push notification plugins
/// </summary>
public interface IPushPlugin : INotificationPlugin
{
    /// <summary>
    ///  Sends a push notification
    /// </summary>
    /// <param name="request"> Push notification request </param>
    /// <returns> Push notification response </returns>
    Task<NotificationResponse> SendAsync(SendPushMessageRequest request);
    /// <summary>
    ///  Sends multiple push notifications
    /// </summary>
    /// <param name="requests"> Push notification requests </param>
    /// <returns> Push notification response </returns>
    Task<NotificationResponse> SendBulkAsync(IEnumerable<SendPushMessageRequest> requests);
    /// <summary>
    ///  Gets the status of a push notification message
    /// </summary>
    /// <param name="messageId"> Message ID </param>
    /// <returns> Push notification response </returns>
    Task<NotificationResponse> GetMessageStatusAsync(string messageId);
    /// <summary>
    ///  Gets the history of push notification messages for a given device token
    /// </summary>
    /// <param name="deviceToken"> Device token </param>
    /// <returns> Push notification response </returns>
    Task<NotificationResponse> GetMessageHistoryAsync(string deviceToken);
    /// <summary>
    ///  Resends a push notification message
    /// </summary>
    /// <param name="messageId"> Message ID </param>
    /// <returns> Push notification response </returns>
    Task<NotificationResponse> ResendMessageAsync(string messageId);
    /// <summary>
    ///  Validates the configuration for the plugin
    /// </summary>
    /// <returns> True if the configuration is valid, false otherwise </returns>
    Task<bool> ValidateConfigurationAsync();
}
