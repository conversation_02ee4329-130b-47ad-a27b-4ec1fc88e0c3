using PushNotificationContract.Models;
using PluginContract.Models;
using PluginContract.Interfaces;

namespace PluginCore.Interfaces;

public interface IPushPlugin : INotificationPlugin
{
    Task<NotificationResponse> SendAsync(SendPushMessageRequest request);
    Task<NotificationResponse> SendBulkAsync(IEnumerable<SendPushMessageRequest> requests);
    Task<NotificationResponse> GetMessageStatusAsync(string messageId);
    Task<NotificationResponse> GetMessageHistoryAsync(string deviceToken);
    Task<NotificationResponse> ResendMessageAsync(string messageId);
    Task<bool> ValidateConfigurationAsync();
}
