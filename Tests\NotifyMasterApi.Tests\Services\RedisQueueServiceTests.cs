using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using NotifyMasterApi.Services;
using StackExchange.Redis;
using Testcontainers.Redis;
using Xunit;

namespace NotifyMasterApi.Tests.Services;

public class RedisQueueServiceTests : IAsyncLifetime
{
    private readonly RedisContainer _redisContainer;
    private IConnectionMultiplexer _redis = null!;
    private RedisQueueService _queueService = null!;
    private Mock<ILogger<RedisQueueService>> _loggerMock = null!;

    public RedisQueueServiceTests()
    {
        _redisContainer = new RedisBuilder()
            .WithImage("redis:7")
            .WithPortBinding(6379, true)
            .Build();
    }

    public async Task InitializeAsync()
    {
        await _redisContainer.StartAsync();
        
        var connectionString = _redisContainer.GetConnectionString();
        _redis = await ConnectionMultiplexer.ConnectAsync(connectionString);
        
        _loggerMock = new Mock<ILogger<RedisQueueService>>();
        _queueService = new RedisQueueService(_redis, _loggerMock.Object);
    }

    public async Task DisposeAsync()
    {
        _queueService?.Dispose();
        _redis?.Dispose();
        await _redisContainer.DisposeAsync();
    }

    [Fact]
    public async Task PublishAsync_WithValidMessage_ShouldAddToQueue()
    {
        // Arrange
        var queueName = "test-queue";
        var message = new { Id = 1, Text = "Test message" };

        // Act
        await _queueService.PublishAsync(queueName, message);

        // Assert
        var queueLength = await _queueService.GetQueueLengthAsync(queueName);
        queueLength.Should().Be(1);
    }

    [Fact]
    public async Task ConsumeAsync_WithMessageInQueue_ShouldReturnMessage()
    {
        // Arrange
        var queueName = "test-queue-consume";
        var originalMessage = new { Id = 2, Text = "Test consume message" };
        await _queueService.PublishAsync(queueName, originalMessage);

        // Act
        var consumedMessage = await _queueService.ConsumeAsync<dynamic>(queueName);

        // Assert
        consumedMessage.Should().NotBeNull();
        var queueLength = await _queueService.GetQueueLengthAsync(queueName);
        queueLength.Should().Be(0);
    }

    [Fact]
    public async Task ConsumeAsync_WithEmptyQueue_ShouldReturnNull()
    {
        // Arrange
        var queueName = "empty-queue";

        // Act
        var consumedMessage = await _queueService.ConsumeAsync<dynamic>(queueName);

        // Assert
        consumedMessage.Should().BeNull();
    }

    [Fact]
    public async Task ConsumeBatchAsync_WithMultipleMessages_ShouldReturnBatch()
    {
        // Arrange
        var queueName = "batch-queue";
        var messages = new[]
        {
            new { Id = 1, Text = "Message 1" },
            new { Id = 2, Text = "Message 2" },
            new { Id = 3, Text = "Message 3" }
        };

        foreach (var message in messages)
        {
            await _queueService.PublishAsync(queueName, message);
        }

        // Act
        var consumedMessages = await _queueService.ConsumeBatchAsync<dynamic>(queueName, 2);

        // Assert
        consumedMessages.Should().HaveCount(2);
        var remainingLength = await _queueService.GetQueueLengthAsync(queueName);
        remainingLength.Should().Be(1);
    }

    [Fact]
    public async Task GetQueueLengthAsync_WithMessages_ShouldReturnCorrectCount()
    {
        // Arrange
        var queueName = "length-queue";
        var messageCount = 5;

        for (int i = 0; i < messageCount; i++)
        {
            await _queueService.PublishAsync(queueName, new { Id = i, Text = $"Message {i}" });
        }

        // Act
        var length = await _queueService.GetQueueLengthAsync(queueName);

        // Assert
        length.Should().Be(messageCount);
    }

    [Fact]
    public async Task DeleteQueueAsync_WithExistingQueue_ShouldDeleteQueue()
    {
        // Arrange
        var queueName = "delete-queue";
        await _queueService.PublishAsync(queueName, new { Id = 1, Text = "Test" });

        // Act
        var deleted = await _queueService.DeleteQueueAsync(queueName);

        // Assert
        deleted.Should().BeTrue();
        var length = await _queueService.GetQueueLengthAsync(queueName);
        length.Should().Be(0);
    }

    [Fact]
    public async Task PublishEventAsync_ShouldPublishToChannel()
    {
        // Arrange
        var channel = "test-channel";
        var eventData = new { Type = "TestEvent", Data = "Test data" };
        var receivedEvents = new List<dynamic>();

        // Subscribe to channel
        await _queueService.SubscribeAsync<dynamic>(channel, async (data) =>
        {
            receivedEvents.Add(data);
            await Task.CompletedTask;
        });

        // Wait a moment for subscription to be established
        await Task.Delay(100);

        // Act
        await _queueService.PublishEventAsync(channel, eventData);

        // Wait for message to be received
        await Task.Delay(100);

        // Assert
        receivedEvents.Should().HaveCount(1);
    }

    [Fact]
    public async Task QueuedNotification_ShouldSerializeAndDeserializeCorrectly()
    {
        // Arrange
        var queueName = "notification-queue";
        var notification = new QueuedNotification(
            Id: "test-123",
            Type: "SMS",
            Payload: new { PhoneNumber = "+1234567890", Message = "Test SMS" },
            QueuedAt: DateTime.UtcNow,
            RetryCount: 0,
            ScheduledFor: DateTime.UtcNow.AddMinutes(5)
        );

        // Act
        await _queueService.PublishAsync(queueName, notification);
        var consumedNotification = await _queueService.ConsumeAsync<QueuedNotification>(queueName);

        // Assert
        consumedNotification.Should().NotBeNull();
        consumedNotification!.Id.Should().Be(notification.Id);
        consumedNotification.Type.Should().Be(notification.Type);
        consumedNotification.RetryCount.Should().Be(notification.RetryCount);
    }

    [Fact]
    public async Task NotificationEvent_ShouldPublishAndReceiveCorrectly()
    {
        // Arrange
        var channel = ChannelNames.NotificationSent;
        var notificationEvent = new NotificationEvent(
            NotificationId: "test-456",
            Type: "Email",
            Status: "Sent",
            Provider: "SendGrid",
            Timestamp: DateTime.UtcNow
        );

        var receivedEvents = new List<NotificationEvent>();

        // Subscribe to channel
        await _queueService.SubscribeAsync<NotificationEvent>(channel, async (eventData) =>
        {
            receivedEvents.Add(eventData);
            await Task.CompletedTask;
        });

        await Task.Delay(100);

        // Act
        await _queueService.PublishEventAsync(channel, notificationEvent);
        await Task.Delay(100);

        // Assert
        receivedEvents.Should().HaveCount(1);
        var receivedEvent = receivedEvents.First();
        receivedEvent.NotificationId.Should().Be(notificationEvent.NotificationId);
        receivedEvent.Type.Should().Be(notificationEvent.Type);
        receivedEvent.Status.Should().Be(notificationEvent.Status);
        receivedEvent.Provider.Should().Be(notificationEvent.Provider);
    }

    [Fact]
    public async Task PluginEvent_ShouldHandlePluginLifecycleEvents()
    {
        // Arrange
        var channel = ChannelNames.PluginLoaded;
        var pluginEvent = new PluginEvent(
            PluginName: "Twilio SMS Plugin",
            Action: "Loaded",
            Success: true,
            Timestamp: DateTime.UtcNow
        );

        var receivedEvents = new List<PluginEvent>();

        // Subscribe to channel
        await _queueService.SubscribeAsync<PluginEvent>(channel, async (eventData) =>
        {
            receivedEvents.Add(eventData);
            await Task.CompletedTask;
        });

        await Task.Delay(100);

        // Act
        await _queueService.PublishEventAsync(channel, pluginEvent);
        await Task.Delay(100);

        // Assert
        receivedEvents.Should().HaveCount(1);
        var receivedEvent = receivedEvents.First();
        receivedEvent.PluginName.Should().Be(pluginEvent.PluginName);
        receivedEvent.Action.Should().Be(pluginEvent.Action);
        receivedEvent.Success.Should().Be(pluginEvent.Success);
    }

    [Fact]
    public async Task HealthStatusEvent_ShouldPublishHealthUpdates()
    {
        // Arrange
        var channel = ChannelNames.HealthStatusChanged;
        var healthEvent = new HealthStatusEvent(
            ComponentName: "SMS Service",
            Status: "Healthy",
            Timestamp: DateTime.UtcNow,
            Details: new Dictionary<string, object>
            {
                { "ResponseTime", 150 },
                { "SuccessRate", 99.5 },
                { "LastCheck", DateTime.UtcNow }
            }
        );

        var receivedEvents = new List<HealthStatusEvent>();

        // Subscribe to channel
        await _queueService.SubscribeAsync<HealthStatusEvent>(channel, async (eventData) =>
        {
            receivedEvents.Add(eventData);
            await Task.CompletedTask;
        });

        await Task.Delay(100);

        // Act
        await _queueService.PublishEventAsync(channel, healthEvent);
        await Task.Delay(100);

        // Assert
        receivedEvents.Should().HaveCount(1);
        var receivedEvent = receivedEvents.First();
        receivedEvent.ComponentName.Should().Be(healthEvent.ComponentName);
        receivedEvent.Status.Should().Be(healthEvent.Status);
        receivedEvent.Details.Should().NotBeNull();
        receivedEvent.Details!.Should().ContainKey("ResponseTime");
    }

    [Fact]
    public async Task MetricsEvent_ShouldPublishMetricsUpdates()
    {
        // Arrange
        var channel = ChannelNames.MetricsUpdated;
        var metricsEvent = new MetricsEvent(
            MetricType: "SMS",
            Metrics: new Dictionary<string, object>
            {
                { "TotalSent", 1500 },
                { "Delivered", 1450 },
                { "Failed", 50 },
                { "DeliveryRate", 96.7 }
            },
            Timestamp: DateTime.UtcNow
        );

        var receivedEvents = new List<MetricsEvent>();

        // Subscribe to channel
        await _queueService.SubscribeAsync<MetricsEvent>(channel, async (eventData) =>
        {
            receivedEvents.Add(eventData);
            await Task.CompletedTask;
        });

        await Task.Delay(100);

        // Act
        await _queueService.PublishEventAsync(channel, metricsEvent);
        await Task.Delay(100);

        // Assert
        receivedEvents.Should().HaveCount(1);
        var receivedEvent = receivedEvents.First();
        receivedEvent.MetricType.Should().Be(metricsEvent.MetricType);
        receivedEvent.Metrics.Should().ContainKey("TotalSent");
        receivedEvent.Metrics.Should().ContainKey("DeliveryRate");
    }

    [Theory]
    [InlineData(QueueNames.SmsQueue)]
    [InlineData(QueueNames.EmailQueue)]
    [InlineData(QueueNames.PushQueue)]
    [InlineData(QueueNames.BulkSmsQueue)]
    [InlineData(QueueNames.BulkEmailQueue)]
    [InlineData(QueueNames.DeadLetterQueue)]
    public async Task PredefinedQueueNames_ShouldWorkCorrectly(string queueName)
    {
        // Arrange
        var message = new { Id = 1, Type = queueName, Data = "Test data" };

        // Act
        await _queueService.PublishAsync(queueName, message);
        var consumedMessage = await _queueService.ConsumeAsync<dynamic>(queueName);

        // Assert
        consumedMessage.Should().NotBeNull();
    }

    [Theory]
    [InlineData(ChannelNames.NotificationSent)]
    [InlineData(ChannelNames.NotificationFailed)]
    [InlineData(ChannelNames.PluginLoaded)]
    [InlineData(ChannelNames.PluginUnloaded)]
    [InlineData(ChannelNames.HealthStatusChanged)]
    [InlineData(ChannelNames.MetricsUpdated)]
    public async Task PredefinedChannelNames_ShouldWorkCorrectly(string channelName)
    {
        // Arrange
        var eventData = new { Type = channelName, Data = "Test event" };
        var receivedEvents = new List<dynamic>();

        // Subscribe to channel
        await _queueService.SubscribeAsync<dynamic>(channelName, async (data) =>
        {
            receivedEvents.Add(data);
            await Task.CompletedTask;
        });

        await Task.Delay(100);

        // Act
        await _queueService.PublishEventAsync(channelName, eventData);
        await Task.Delay(100);

        // Assert
        receivedEvents.Should().HaveCount(1);
    }
}
