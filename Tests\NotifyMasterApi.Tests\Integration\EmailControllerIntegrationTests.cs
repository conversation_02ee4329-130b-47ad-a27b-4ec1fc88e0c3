using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using FluentAssertions;
using AutoFixture;
using EmailContract.Models;
using Moq;
using NotifyMasterApi.Interfaces;

namespace NotifyMasterApi.Tests.Integration;

/// <summary>
/// Integration tests for the Email API controller with comprehensive validation and error handling.
/// </summary>
public class EmailControllerIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly IFixture _fixture;

    public EmailControllerIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _fixture = new Fixture();
        
        // Configure the test client with mocked dependencies
        _client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Replace real services with mocks for testing
                services.AddScoped(_ => Mock.Of<INotificationQueueService>());
                services.AddScoped(_ => Mock.Of<INotificationLoggingService>());
            });
        }).CreateClient();
    }

    [Fact]
    public async Task SendEmail_WithValidRequest_ShouldReturnOk()
    {
        // Arrange
        var request = new SendEmailRequest
        {
            To = "<EMAIL>",
            Subject = "Test Subject",
            Body = "Test email body content",
            IsHtml = false
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().NotBeNullOrEmpty();
        
        var result = JsonSerializer.Deserialize<dynamic>(content);
        result.Should().NotBeNull();
    }

    [Fact]
    public async Task SendEmail_WithInvalidEmailAddress_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new SendEmailRequest
        {
            To = "invalid-email",
            Subject = "Test Subject",
            Body = "Test email body content",
            IsHtml = false
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("email");
    }

    [Fact]
    public async Task SendEmail_WithEmptySubject_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new SendEmailRequest
        {
            To = "<EMAIL>",
            Subject = "",
            Body = "Test email body content",
            IsHtml = false
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Subject");
    }

    [Fact]
    public async Task SendEmail_WithEmptyBody_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new SendEmailRequest
        {
            To = "<EMAIL>",
            Subject = "Test Subject",
            Body = "",
            IsHtml = false
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Body");
    }

    [Fact]
    public async Task SendEmail_WithNullRequest_ShouldReturnBadRequest()
    {
        // Arrange
        SendEmailRequest? request = null;

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Theory]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("invalid-email", false)]
    [InlineData("@example.com", false)]
    [InlineData("test@", false)]
    [InlineData("", false)]
    public async Task SendEmail_WithVariousEmailFormats_ShouldValidateCorrectly(string email, bool shouldBeValid)
    {
        // Arrange
        var request = new SendEmailRequest
        {
            To = email,
            Subject = "Test Subject",
            Body = "Test email body content",
            IsHtml = false
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/email/send", request);

        // Assert
        if (shouldBeValid)
        {
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }
        else
        {
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }
    }

    [Fact]
    public async Task SendEmail_WithHtmlContent_ShouldReturnOk()
    {
        // Arrange
        var request = new SendEmailRequest
        {
            To = "<EMAIL>",
            Subject = "HTML Test Subject",
            Body = "<html><body><h1>Test HTML Content</h1><p>This is a test email with HTML content.</p></body></html>",
            IsHtml = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task SendEmail_WithAttachments_ShouldReturnOk()
    {
        // Arrange
        var request = new SendEmailRequest
        {
            To = "<EMAIL>",
            Subject = "Test with Attachments",
            Body = "This email has attachments",
            IsHtml = false,
            Attachments = new List<EmailAttachment>
            {
                new EmailAttachment
                {
                    FileName = "test.txt",
                    Content = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("Test file content")),
                    ContentType = "text/plain"
                }
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task SendEmail_WithMultipleRecipients_ShouldReturnOk()
    {
        // Arrange
        var request = new SendEmailRequest
        {
            To = "<EMAIL>",
            Cc = "<EMAIL>",
            Bcc = "<EMAIL>",
            Subject = "Multiple Recipients Test",
            Body = "This email is sent to multiple recipients",
            IsHtml = false
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task SendEmail_WithLongSubject_ShouldReturnBadRequest()
    {
        // Arrange
        var longSubject = new string('A', 1001); // Exceeds typical email subject length limits
        var request = new SendEmailRequest
        {
            To = "<EMAIL>",
            Subject = longSubject,
            Body = "Test email body content",
            IsHtml = false
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SendEmail_WithSpecialCharacters_ShouldReturnOk()
    {
        // Arrange
        var request = new SendEmailRequest
        {
            To = "<EMAIL>",
            Subject = "Test with Special Characters: àáâãäåæçèéêë",
            Body = "Email body with special characters: ñóôõöøùúûüý and emojis: 🚀📧✨",
            IsHtml = false
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task SendEmail_WithCustomHeaders_ShouldReturnOk()
    {
        // Arrange
        var request = new SendEmailRequest
        {
            To = "<EMAIL>",
            Subject = "Test with Custom Headers",
            Body = "This email has custom headers",
            IsHtml = false,
            CustomHeaders = new Dictionary<string, string>
            {
                { "X-Custom-Header", "CustomValue" },
                { "X-Priority", "High" }
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/email/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task SendEmail_ConcurrentRequests_ShouldHandleAllRequests()
    {
        // Arrange
        var tasks = new List<Task<HttpResponseMessage>>();
        var requestCount = 10;

        for (int i = 0; i < requestCount; i++)
        {
            var request = new SendEmailRequest
            {
                To = $"test{i}@example.com",
                Subject = $"Concurrent Test {i}",
                Body = $"This is concurrent email test number {i}",
                IsHtml = false
            };

            tasks.Add(_client.PostAsJsonAsync("/api/v1/email/send", request));
        }

        // Act
        var responses = await Task.WhenAll(tasks);

        // Assert
        responses.Should().HaveCount(requestCount);
        responses.Should().OnlyContain(r => r.StatusCode == HttpStatusCode.OK);
    }
}
