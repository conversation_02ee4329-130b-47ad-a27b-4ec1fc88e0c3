# NotificationService Plugin Architecture

## Overview

The NotificationService has been refactored from a microservices architecture to a plugin-based architecture. This allows for better modularity, easier maintenance, and dynamic loading of notification providers.

## Architecture Components

### Core Components

1. **PluginContract** (`src/Contracts/PluginContract/`)
   - Contains all plugin interfaces and models
   - Defines the contract that all plugins must implement
   - Includes base interfaces: `INotificationPlugin`, `IEmailPlugin`, `ISmsPlugin`, `IPushNotificationPlugin`

2. **PluginCore** (`src/Core/PluginCore/`)
   - Contains the plugin management system
   - Includes `PluginManager` for discovering and managing plugins
   - Provides `NotificationService` that orchestrates plugin calls
   - Contains base classes for plugin development

### ✅ **Comprehensive Fallback Systems**
- **🔄 Port Auto-Discovery** - Automatic port fallback when preferred ports are occupied
- **🔄 Redis Fallback** - In-memory Redis operations when Redis server unavailable
- **🔄 Database Fallback** - In-memory database when PostgreSQL unavailable
- **📊 Fallback Monitoring** - Real-time status and recommendations
- **🚀 Zero-Downtime Startup** - Application starts regardless of infrastructure availability

### ✅ **Comprehensive Fallback Systems**
- **🔄 Port Auto-Discovery** - Automatic port fallback when preferred ports are occupied
- **🔄 Redis Fallback** - In-memory Redis operations when Redis server unavailable
- **🔄 Database Fallback** - In-memory database when PostgreSQL unavailable
- **📊 Fallback Monitoring** - Real-time status and recommendations
- **🚀 Zero-Downtime Startup** - Application starts regardless of infrastructure availability

## 🏗️ Architecture

### Plugin Projects

1. **Kavenegar SMS Plugin** (`src/Plugins/NotificationService.Plugins.Kavenegar/`)
   - Implements SMS functionality using Kavenegar API
   - Supports configuration validation and health checks

2. **SMTP Email Plugin** (`src/Plugins/NotificationService.Plugins.Smtp/`)
   - Implements email functionality using SMTP
   - Supports HTML/text emails, attachments, CC/BCC

3. **Firebase Push Plugin** (`src/Plugins/NotificationService.Plugins.Firebase/`)
   - Implements push notifications using Firebase Cloud Messaging
   - Supports custom data and dry-run mode

## Plugin Development

### Creating a New Plugin

1. Create a new project in `src/Plugins/`
2. Reference `PluginContract` and `PluginCore`
3. Implement the appropriate plugin interface (`IEmailPlugin`, `ISmsPlugin`, or `IPushNotificationPlugin`)
4. Add the `[NotificationPlugin]` attribute to your plugin class
5. Inherit from `BaseNotificationPlugin` for common functionality

### Plugin Interface

```csharp
[NotificationPlugin("PluginName", "1.0.0", "Description", PluginType.Email, "Author")]
public class MyPlugin : BaseNotificationPlugin, IEmailPlugin
{
    public override PluginInfo PluginInfo => new(
        "PluginName", "1.0.0", "Description", 
        PluginType.Email, "Author", true);

    public override async Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        // Validate plugin configuration
        return true;
    }

    public override async Task InitializeAsync(IConfiguration configuration)
    {
        await base.InitializeAsync(configuration);
        // Initialize plugin resources
    }

    public async Task<NotificationResponse> SendEmailAsync(EmailRequest request, CancellationToken cancellationToken = default)
    {
        // Implement email sending logic
        return new EmailResponse(true);
    }

    public override async Task<bool> HealthCheckAsync()
    {
        // Implement health check logic
        return true;
    }
}
```

### Plugin Configuration Structure

```json
{
  "Plugins": {
    "PluginsDirectory": "plugins",
    "PluginName": {
      "IsEnabled": true,
      "ConfigProperty1": "value1",
      "ConfigProperty2": "value2"
    }
  }
}
```

### Example Configurations

#### Kavenegar SMS Plugin
```json
{
  "Plugins": {
    "Kavenegar": {
      "ApiKey": "your-api-key",
      "SenderNumber": "your-sender-number",
      "IsEnabled": true,
      "TimeoutSeconds": 30,
      "RetryCount": 3
    }
  }
}
```

#### SMTP Email Plugin
```json
{
  "Plugins": {
    "SMTP": {
      "Host": "smtp.gmail.com",
      "Port": 587,
      "UseSsl": true,
      "Username": "<EMAIL>",
      "Password": "your-app-password",
      "FromEmail": "<EMAIL>",
      "FromName": "Notification Service",
      "IsEnabled": true,
      "TimeoutSeconds": 30
    }
  }
}
```

## API Endpoints

### Send Notifications
- **POST** `/api/notifications/send`
- Supports multiple notification types in a single request

### Plugin Management
- **GET** `/api/notifications/plugins` - List all plugins
- **GET** `/api/notifications/plugins/health` - Get plugin health status
- **POST** `/api/notifications/plugins/{pluginName}/toggle` - Enable/disable plugin

## Plugin Discovery

Plugins are automatically discovered at startup by:
1. Scanning the configured plugins directory
2. Loading assemblies and searching for classes with `[NotificationPlugin]` attribute
3. Validating plugin configuration
4. Initializing enabled plugins

## Benefits

1. **Modularity**: Each notification provider is a separate project
2. **Extensibility**: Easy to add new notification providers
3. **Configuration**: Runtime plugin enable/disable
4. **Health Monitoring**: Built-in health checks for all plugins
5. **Fallback Support**: Automatic fallback to other plugins if one fails
6. **Hot Swapping**: Plugins can be updated without restarting the service

## Migration from Microservices

The original microservices (EmailService, SmsService, PushNotificationService) can still be used alongside the plugin system, or completely replaced by deploying plugins to the NotificationService.
=======
### 🔌 **Plugin Architecture Benefits**
- **📈 Scalability** - Add new providers without code changes
- **🔧 Maintainability** - Plugins are completely isolated
- **⚡ Performance** - Unload unused plugins to free memory
- **🛡️ Reliability** - Plugin failures don't affect the main service
- **🔄 Flexibility** - Enable/disable providers at runtime

## 🚀 Quick Start (2 Minutes)

### Option 1: Zero Infrastructure Setup
```bash
# Clone and run immediately - no dependencies required!
git clone https://github.com/bloodchild8906/NotificationService-master.git
cd NotificationService-master
dotnet run --project src/Services/NotifyMasterApi

# ✅ Runs with automatic fallback systems
# ✅ No Redis, PostgreSQL, or specific ports required
# ✅ Perfect for development and testing
```

### Option 2: Full Infrastructure Setup
```bash
# Start infrastructure
docker run -d --name redis -p 6379:6379 redis:7-alpine
docker run -d --name postgres -e POSTGRES_PASSWORD=password -p 5432:5432 postgres:15

# Clone and run
git clone https://github.com/bloodchild8906/NotificationService-master.git
cd NotificationService-master
dotnet run --project src/Services/NotifyMasterApi

# ✅ Runs with full Redis and PostgreSQL support
```

### Verify It's Working
```bash
# Check health
curl http://localhost:5120/health/ready

# View system status
curl http://localhost:5120/api/systemstatus

# Access Swagger UI
open http://localhost:5120/swagger
```

**🎉 The service is now running! See [Setup Guide](docs/SETUP_GUIDE.md) for detailed configuration.**

## 📚 Documentation

### 🚀 Getting Started
- **[Quick Start Guide](docs/QUICK_START.md)** - Get running in 5 minutes
- **[Complete Setup Guide](docs/SETUP_GUIDE.md)** - Step-by-step installation
- **[Configuration Reference](docs/CONFIGURATION_REFERENCE.md)** - All configuration options
- **[Deployment Guide](docs/DEPLOYMENT_GUIDE.md)** - Production deployment

### 🏗️ Architecture & Design
- **[Architecture Diagrams](docs/ARCHITECTURE_DIAGRAMS.md)** - Visual system overview
- **[Fallback Systems](docs/FALLBACK_SYSTEMS.md)** - Understanding fallback mechanisms
- **[Plugin Development](docs/PLUGIN_DEVELOPMENT.md)** - Creating custom plugins
- **[API Documentation](docs/API_DOCUMENTATION.md)** - Complete API reference

### 🧪 Testing & Development
- **[Testing Guide](docs/TESTING_GUIDE.md)** - Running and writing tests
- **[Development Setup](docs/DEVELOPMENT_SETUP.md)** - Development environment
- **[Contributing Guide](docs/CONTRIBUTING.md)** - How to contribute

## 🎯 Key Features Showcase

### 🔄 Fallback Systems in Action
```bash
# Start without any infrastructure
dotnet run --project src/Services/NotifyMasterApi

# Expected output:
# ⚠️  Port 5120 is in use. Falling back to port 5121
# ⚠️  Redis connection failed - using fallback
# 🔄 Database fallback activated - using in-memory storage
# ✅ Application started successfully!
```

### 🔌 Plugin System Demo
```bash
# Check plugin status
curl http://localhost:5120/api/admin/plugins

# Load a plugin at runtime
curl -X POST http://localhost:5120/api/admin/plugins/load \
  -H "Content-Type: application/json" \
  -d '{"pluginPath": "./plugins/Plugin.Email.SendGrid.dll"}'

# Unload a plugin
curl -X POST http://localhost:5120/api/admin/plugins/unload \
  -H "Content-Type: application/json" \
  -d '{"pluginName": "SendGrid"}'
```

### 📊 System Monitoring
```bash
# Overall system status
curl http://localhost:5120/api/systemstatus | jq

# Fallback status with recommendations
curl http://localhost:5120/api/systemstatus/fallbacks | jq

# Test Redis operations (works with fallback)
curl -X POST http://localhost:5120/api/systemstatus/redis/test | jq

