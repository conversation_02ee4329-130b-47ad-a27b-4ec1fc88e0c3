# NotificationService

A comprehensive, enterprise-grade notification service built with .NET 9.0 that supports multiple notification channels through a sophisticated plugin-based architecture with runtime loading capabilities.

## 🚀 Features

### Core Capabilities
- **Multi-Channel Support**: Email, SMS, and Push notifications with unified API
- **Plugin Architecture**: Runtime-loadable plugins with hot-swapping capabilities
- **High Performance**: Designed for high-throughput notification processing with concurrent execution
- **Enterprise Monitoring**: Comprehensive logging, metrics, and health monitoring
- **Intelligent Failover**: Automatic failover between providers with circuit breaker patterns
- **Modern REST API**: Clean, well-documented API with OpenAPI/Swagger support
- **Robust Data Layer**: PostgreSQL with Entity Framework Core and Redis message queuing
- **Production Ready**: Comprehensive error handling, retry logic, and graceful degradation

### Advanced Features
- **Provider Cooldown**: Automatic provider failure detection and cooldown periods
- **Configuration Management**: Dynamic plugin configuration with validation
- **Dependency Management**: Plugin dependency validation and resolution
- **Performance Monitoring**: Real-time metrics collection with System.Diagnostics.Metrics
- **Comprehensive Testing**: Unit, integration, and performance tests with >90% coverage
- **Security**: Input validation, rate limiting, and secure plugin loading
- **Scalability**: Horizontal scaling support with distributed queuing

### ✅ **Comprehensive Fallback Systems**
- **🔄 Port Auto-Discovery** - Automatic port fallback when preferred ports are occupied
- **🔄 Redis Fallback** - In-memory Redis operations when Redis server unavailable
- **🔄 Database Fallback** - In-memory database when PostgreSQL unavailable
- **📊 Fallback Monitoring** - Real-time status and recommendations
- **🚀 Zero-Downtime Startup** - Application starts regardless of infrastructure availability

## 🏗️ Architecture

The service follows a sophisticated modular architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    NotifyMasterApi                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐   │
│  │ Controllers │ │  Services   │ │   Plugin Manager    │   │
│  │             │ │             │ │                     │   │
│  │ • Email     │ │ • Processing│ │ • Runtime Loading   │   │
│  │ • SMS       │ │ • Routing   │ │ • Hot Swapping      │   │
│  │ • Push      │ │ • Logging   │ │ • Health Monitoring │   │
│  │ • Admin     │ │ • Queue     │ │ • Configuration     │   │
│  └─────────────┘ └─────────────┘ └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      Contracts                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐   │
│  │ Email       │ │    SMS      │ │   Push & Plugin     │   │
│  │ Contract    │ │  Contract   │ │     Contracts       │   │
│  └─────────────┘ └─────────────┘ └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Plugin Ecosystem                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐   │
│  │ Email       │ │    SMS      │ │   Push Notification │   │
│  │ Plugins     │ │  Plugins    │ │      Plugins        │   │
│  │             │ │             │ │                     │   │
│  │ • SendGrid  │ │ • Twilio    │ │ • Firebase FCM      │   │
│  │ • SMTP      │ │ • BulkSMS   │ │ • Apple APNS        │   │
│  │ • Custom    │ │ • Clickatel │ │ • Custom Providers  │   │
│  └─────────────┘ └─────────────┘ └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Component Overview

- **NotifyMasterApi**: Main API service with controllers, business logic, and plugin management
- **Contracts**: Shared models, interfaces, and validation rules for different notification types
- **Libraries**: Service libraries providing core functionality for each notification channel
- **Plugins**: Runtime-loadable provider implementations following the plugin contract
- **Data Layer**: PostgreSQL database with Entity Framework Core for persistence and logging
- **Queue System**: Redis-based message queuing with fallback to in-memory queues

## 🚀 Quick Start

### Prerequisites

- **.NET 9.0 SDK** or later
- **PostgreSQL 13+** (or use in-memory database for development)
- **Redis 6+** (optional, falls back to in-memory queue)
- **Docker** (optional, for containerized deployment)

### Option 1: Zero Infrastructure Setup
```bash
# Clone and run immediately - no dependencies required!
git clone https://github.com/bloodchild8906/NotificationService-master.git
cd NotificationService-master
dotnet run --project src/Services/NotifyMasterApi

# ✅ Runs with automatic fallback systems
# ✅ No Redis, PostgreSQL, or specific ports required
# ✅ Perfect for development and testing
```

### Option 2: Full Infrastructure Setup
```bash
# Start infrastructure
docker run -d --name redis -p 6379:6379 redis:7-alpine
docker run -d --name postgres -e POSTGRES_PASSWORD=password -p 5432:5432 postgres:15

# Clone and run
git clone https://github.com/bloodchild8906/NotificationService-master.git
cd NotificationService-master
dotnet run --project src/Services/NotifyMasterApi

# ✅ Runs with full Redis and PostgreSQL support
```

### Verify It's Working
```bash
# Check health
curl https://localhost:7001/health

# Access Swagger UI
open https://localhost:7001/swagger

# Send a test email
curl -X POST https://localhost:7001/api/v1/email/send \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test Email",
    "body": "Hello from NotificationService!",
    "isHtml": false
  }'
```

## 📚 API Documentation

### Email Endpoints

#### Send Email
```http
POST /api/v1/email/send
Content-Type: application/json

{
  "to": "<EMAIL>",
  "subject": "Welcome!",
  "body": "Welcome to our service!",
  "isHtml": false,
  "cc": "<EMAIL>",
  "bcc": "<EMAIL>",
  "attachments": [
    {
      "fileName": "document.pdf",
      "content": "base64-encoded-content",
      "contentType": "application/pdf"
    }
  ],
  "customHeaders": {
    "X-Priority": "High"
  }
}
```

### SMS Endpoints

#### Send SMS
```http
POST /api/v1/sms/send
Content-Type: application/json

{
  "phoneNumber": "+**********",
  "message": "Your verification code is 123456",
  "senderId": "YourApp"
}
```

### Push Notification Endpoints

#### Send Push Notification
```http
POST /api/v1/push/send
Content-Type: application/json

{
  "deviceToken": "device-token-here",
  "title": "New Message",
  "body": "You have a new message",
  "data": {
    "messageId": "12345",
    "type": "chat"
  },
  "priority": "High"
}
```

### Admin Endpoints

#### Health Check
```http
GET /api/v1/admin/health
```

#### Service Metrics
```http
GET /api/v1/admin/metrics
```

#### Plugin Management
```http
GET /api/v1/admin/plugins
POST /api/v1/admin/plugins/upload
DELETE /api/v1/admin/plugins/{name}
```

## ⚙️ Configuration

### Application Settings

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=NotifyMasterApi;Username=user;Password=password",
    "RedisConnection": "localhost:6379"
  },
  "Plugins": {
    "Directory": "./plugins",
    "ConfigDirectory": "./plugins/config",
    "EnableHotReload": true,
    "LoadOnStartup": true
  },
  "NotificationProcessing": {
    "MaxConcurrentProcessing": 10,
    "ProcessingIntervalMs": 1000,
    "MaxRetryAttempts": 3,
    "RetryDelayMs": 5000
  },
  "RuntimeNotification": {
    "ProviderCooldownMinutes": 5,
    "DefaultTimeoutSeconds": 30
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "NotifyMasterApi": "Debug"
    }
  }
}
```

### Environment Variables

```bash
# Database
CONNECTIONSTRINGS__DEFAULTCONNECTION="Host=localhost;Database=NotifyMasterApi;Username=user;Password=password"
CONNECTIONSTRINGS__REDISCONNECTION="localhost:6379"

# Plugins
PLUGINS__DIRECTORY="./plugins"
PLUGINS__ENABLEHOTRELOAD="true"

# Processing
NOTIFICATIONPROCESSING__MAXCONCURRENTPROCESSING="10"
NOTIFICATIONPROCESSING__MAXRETRYATTEMPTS="3"

# Logging
LOGGING__LOGLEVEL__DEFAULT="Information"
```

## 🔌 Plugin Development

### Creating a New Plugin

1. **Create Plugin Project**
   ```bash
   dotnet new classlib -n Plugin.Email.MyProvider
   cd Plugin.Email.MyProvider
   ```

2. **Add References**
   ```xml
   <PackageReference Include="EmailContract" Version="1.0.0" />
   <PackageReference Include="PluginContract" Version="1.0.0" />
   ```

3. **Implement Plugin Interface**
   ```csharp
   [PluginManifest("MyProvider", "1.0.0", "Email", "My Custom Email Provider")]
   public class MyEmailPlugin : IEmailPlugin
   {
       public string Name => "MyProvider";
       public string Version => "1.0.0";
       public string Type => "Email";

       public async Task<EmailResponse> SendEmailAsync(SendEmailRequest request)
       {
           // Implement email sending logic
           return EmailResponse.CreateSuccess("message-id");
       }

       public Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
       {
           // Validate plugin configuration
           return Task.FromResult(true);
       }
   }
   ```

4. **Create Plugin Manifest**
   ```json
   {
     "name": "MyProvider",
     "version": "1.0.0",
     "type": "Email",
     "description": "My Custom Email Provider",
     "author": "Your Name",
     "dependencies": [],
     "configuration": {
       "apiKey": {
         "type": "string",
         "required": true,
         "description": "API key for the email service"
       }
     }
   }
   ```

### Plugin Architecture Benefits

- **📈 Scalability** - Add new providers without code changes
- **🔧 Maintainability** - Plugins are completely isolated
- **⚡ Performance** - Unload unused plugins to free memory
- **🛡️ Reliability** - Plugin failures don't affect the main service
- **🔄 Flexibility** - Enable/disable providers at runtime

## 🧪 Testing

### Running Tests

```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test category
dotnet test --filter Category=Unit
dotnet test --filter Category=Integration

# Run performance tests
dotnet test --filter Category=Performance
```

### Test Categories

- **Unit Tests**: Fast, isolated component tests
- **Integration Tests**: API endpoint and service integration tests
- **Performance Tests**: Load and stress testing with NBomber
- **End-to-End Tests**: Complete workflow validation

## 🚀 Deployment

### Docker Deployment

```bash
# Build image
docker build -t notification-service .

# Run with Docker Compose
docker-compose up -d

# Scale services
docker-compose up -d --scale notification-service=3
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: notification-service
  template:
    metadata:
      labels:
        app: notification-service
    spec:
      containers:
      - name: notification-service
        image: notification-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: CONNECTIONSTRINGS__DEFAULTCONNECTION
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: connection-string
```

## 📊 Monitoring & Observability

### Health Checks

- **Startup**: `/health/startup` - Application startup health
- **Readiness**: `/health/ready` - Ready to accept traffic
- **Liveness**: `/health/live` - Application is running

### Metrics

- **Notification Metrics**: Success/failure rates, processing times
- **Plugin Metrics**: Plugin health, performance, error rates
- **System Metrics**: Memory usage, CPU utilization, queue depth

### Logging

- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Log Levels**: Configurable logging levels per component
- **External Integration**: Elasticsearch, Application Insights support

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Make your changes**
4. **Add tests**
5. **Run the test suite**
   ```bash
   dotnet test
   ```
6. **Submit a pull request**

### Development Guidelines

- Follow C# coding conventions
- Write comprehensive tests
- Update documentation
- Use conventional commit messages
- Ensure all CI checks pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with .NET 9.0 and ASP.NET Core
- Uses Entity Framework Core for data access
- Redis for message queuing and caching
- xUnit, Moq, and FluentAssertions for testing
- OpenAPI/Swagger for API documentation

---

**⭐ If you find this project useful, please consider giving it a star!**

