using Microsoft.AspNetCore.Mvc;
using NotificationContract.Models;
using NotifyMasterApi.Gateways;

namespace NotifyMasterApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SimpleEmailController : ControllerBase
{
    private readonly IEmailGateway _emailGateway;
    private readonly ILogger<SimpleEmailController> _logger;

    public SimpleEmailController(IEmailGateway emailGateway, ILogger<SimpleEmailController> logger)
    {
        _emailGateway = emailGateway;
        _logger = logger;
    }

    [HttpPost("send")]
    public async Task<IActionResult> SendEmail([FromBody] EmailMessageRequest request)
    {
        try
        {
            _logger.LogInformation("Sending email to {To}", request.To);
            
            var result = await _emailGateway.SendAsync(request);
            
            if (result.IsSuccess)
            {
                return Ok(new { success = true, messageId = result.MessageId });
            }
            
            return BadRequest(new { success = false, error = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending email");
            return StatusCode(500, new { success = false, error = "Internal server error" });
        }
    }

    [HttpPost("send-bulk")]
    public async Task<IActionResult> SendBulkEmail([FromBody] BulkEmailRequest request)
    {
        try
        {
            _logger.LogInformation("Sending bulk email to {Count} recipients", request.Messages.Count());
            
            var result = await _emailGateway.SendBulkAsync(request);
            
            return Ok(new { 
                success = result.IsSuccess, 
                totalSent = result.Results.Count(r => r.IsSuccess),
                totalFailed = result.Results.Count(r => !r.IsSuccess),
                results = result.Results
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk email");
            return StatusCode(500, new { success = false, error = "Internal server error" });
        }
    }

    [HttpGet("providers")]
    public async Task<IActionResult> GetProviders()
    {
        try
        {
            var providers = await _emailGateway.GetProvidersAsync();
            return Ok(providers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email providers");
            return StatusCode(500, new { success = false, error = "Internal server error" });
        }
    }

    [HttpPost("test/{provider}")]
    public async Task<IActionResult> TestProvider(string provider, [FromQuery] string? testEmail = null)
    {
        try
        {
            var result = await _emailGateway.TestProviderAsync(provider, testEmail);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing email provider {Provider}", provider);
            return StatusCode(500, new { success = false, error = "Internal server error" });
        }
    }
}
