using Microsoft.AspNetCore.Mvc;
using NotificationContract.Models;
using NotifyMasterApi.Gateways;
using System.ComponentModel.DataAnnotations;
using System.Net;

namespace NotifyMasterApi.Controllers;

/// <summary>
/// Provides email notification endpoints for sending single and bulk emails through various providers.
/// </summary>
/// <remarks>
/// This controller handles email operations including sending individual emails, bulk email operations,
/// provider management, and testing functionality. All operations support multiple email providers
/// through the plugin architecture.
/// </remarks>
[ApiController]
[Route("api/v1/email")]
[Produces("application/json")]
[Tags("Email")]
public class SimpleEmailController : ControllerBase
{
    private readonly IEmailGateway _emailGateway;
    private readonly ILogger<SimpleEmailController> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="SimpleEmailController"/> class.
    /// </summary>
    /// <param name="emailGateway">The email gateway service for handling email operations</param>
    /// <param name="logger">The logger instance for this controller</param>
    /// <exception cref="ArgumentNullException">Thrown when emailGateway or logger is null</exception>
    public SimpleEmailController(IEmailGateway emailGateway, ILogger<SimpleEmailController> logger)
    {
        _emailGateway = emailGateway ?? throw new ArgumentNullException(nameof(emailGateway));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Sends a single email message through the configured email provider.
    /// </summary>
    /// <param name="request">The email message request containing recipient, subject, and content information</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A response indicating the success status and message ID if successful</returns>
    /// <response code="200">Email was successfully sent or queued for delivery</response>
    /// <response code="400">Invalid request data or email sending failed</response>
    /// <response code="422">Request validation failed</response>
    /// <response code="500">Internal server error occurred</response>
    /// <example>
    /// POST /api/v1/email/send
    /// {
    ///   "to": "<EMAIL>",
    ///   "subject": "Welcome to our service",
    ///   "body": "Thank you for signing up!",
    ///   "isHtml": true
    /// }
    /// </example>
    [HttpPost("send")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ValidationProblemDetails), (int)HttpStatusCode.UnprocessableEntity)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> SendEmail(
        [FromBody, Required] EmailMessageRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the request
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid email request received: {ValidationErrors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return UnprocessableEntity(ModelState);
            }

            _logger.LogInformation("Sending email to {To} with subject '{Subject}'",
                request.To, request.Subject);

            var result = await _emailGateway.SendAsync(request);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Email successfully sent to {To}, MessageId: {MessageId}",
                    request.To, result.MessageId);

                return Ok(new {
                    success = true,
                    messageId = result.MessageId,
                    timestamp = DateTime.UtcNow,
                    recipient = request.To
                });
            }

            _logger.LogWarning("Email sending failed for {To}: {Error}", request.To, result.ErrorMessage);
            return BadRequest(new {
                success = false,
                error = result.ErrorMessage,
                timestamp = DateTime.UtcNow,
                recipient = request.To
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument provided for email sending to {To}", request?.To);
            return BadRequest(new { success = false, error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending email to {To}", request?.To);
            return StatusCode(500, new {
                success = false,
                error = "An internal server error occurred while sending the email",
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Sends multiple email messages in a single batch operation.
    /// </summary>
    /// <param name="request">The bulk email request containing multiple email messages</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A response with overall status and individual results for each email</returns>
    /// <response code="200">Bulk operation completed (check individual results for per-email status)</response>
    /// <response code="400">Invalid request data</response>
    /// <response code="422">Request validation failed</response>
    /// <response code="500">Internal server error occurred</response>
    /// <example>
    /// POST /api/v1/email/send-bulk
    /// {
    ///   "messages": [
    ///     {
    ///       "to": "<EMAIL>",
    ///       "subject": "Welcome",
    ///       "body": "Welcome to our service!"
    ///     },
    ///     {
    ///       "to": "<EMAIL>",
    ///       "subject": "Welcome",
    ///       "body": "Welcome to our service!"
    ///     }
    ///   ]
    /// }
    /// </example>
    [HttpPost("send-bulk")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ValidationProblemDetails), (int)HttpStatusCode.UnprocessableEntity)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> SendBulkEmail(
        [FromBody, Required] BulkEmailRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the request
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid bulk email request received: {ValidationErrors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return UnprocessableEntity(ModelState);
            }

            var messageCount = request.Messages?.Count() ?? 0;
            if (messageCount == 0)
            {
                return BadRequest(new { success = false, error = "At least one email message is required" });
            }

            _logger.LogInformation("Sending bulk email to {Count} recipients", messageCount);

            var result = await _emailGateway.SendBulkAsync(request);

            var successCount = result.Results.Count(r => r.IsSuccess);
            var failureCount = result.Results.Count(r => !r.IsSuccess);

            _logger.LogInformation("Bulk email operation completed: {SuccessCount} successful, {FailureCount} failed",
                successCount, failureCount);

            return Ok(new {
                success = result.IsSuccess,
                totalSent = successCount,
                totalFailed = failureCount,
                totalRequested = messageCount,
                timestamp = DateTime.UtcNow,
                results = result.Results.Select(r => new {
                    success = r.IsSuccess,
                    messageId = r.MessageId,
                    error = r.ErrorMessage
                })
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument provided for bulk email sending");
            return BadRequest(new { success = false, error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending bulk email");
            return StatusCode(500, new {
                success = false,
                error = "An internal server error occurred while sending bulk emails",
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Retrieves a list of available email providers and their status.
    /// </summary>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A list of available email providers with their configuration status</returns>
    /// <response code="200">Successfully retrieved provider list</response>
    /// <response code="500">Internal server error occurred</response>
    /// <example>
    /// GET /api/v1/email/providers
    /// </example>
    [HttpGet("providers")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> GetProviders(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Retrieving available email providers");

            var providers = await _emailGateway.GetProvidersAsync();

            _logger.LogInformation("Retrieved {Count} email providers", providers?.Count() ?? 0);

            return Ok(new {
                success = true,
                providers = providers,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error retrieving email providers");
            return StatusCode(500, new {
                success = false,
                error = "An internal server error occurred while retrieving email providers",
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Tests the configuration and connectivity of a specific email provider.
    /// </summary>
    /// <param name="provider">The name of the email provider to test</param>
    /// <param name="testEmail">Optional test email address to send a test message to</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>The result of the provider test including connectivity and configuration status</returns>
    /// <response code="200">Provider test completed (check result for success/failure details)</response>
    /// <response code="400">Invalid provider name or test parameters</response>
    /// <response code="404">Provider not found</response>
    /// <response code="500">Internal server error occurred</response>
    /// <example>
    /// POST /api/v1/email/test/sendgrid?testEmail=<EMAIL>
    /// </example>
    [HttpPost("test/{provider}")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.NotFound)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> TestProvider(
        [FromRoute, Required] string provider,
        [FromQuery] string? testEmail = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate provider parameter
            if (string.IsNullOrWhiteSpace(provider))
            {
                return BadRequest(new { success = false, error = "Provider name is required" });
            }

            // Validate test email if provided
            if (!string.IsNullOrWhiteSpace(testEmail) && !IsValidEmail(testEmail))
            {
                return BadRequest(new { success = false, error = "Invalid test email address format" });
            }

            _logger.LogInformation("Testing email provider '{Provider}' with test email '{TestEmail}'",
                provider, testEmail ?? "none");

            var result = await _emailGateway.TestProviderAsync(provider, testEmail);

            _logger.LogInformation("Email provider test completed for '{Provider}': {Success}",
                provider, result?.IsSuccess ?? false);

            return Ok(new {
                success = true,
                provider = provider,
                testEmail = testEmail,
                result = result,
                timestamp = DateTime.UtcNow
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument for testing email provider '{Provider}'", provider);
            return BadRequest(new { success = false, error = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Provider '{Provider}' not found or not available", provider);
            return NotFound(new {
                success = false,
                error = $"Email provider '{provider}' not found or not available",
                provider = provider
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error testing email provider '{Provider}'", provider);
            return StatusCode(500, new {
                success = false,
                error = "An internal server error occurred while testing the email provider",
                provider = provider,
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Validates if the provided string is a valid email address format.
    /// </summary>
    /// <param name="email">The email address to validate</param>
    /// <returns>True if the email format is valid, false otherwise</returns>
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
