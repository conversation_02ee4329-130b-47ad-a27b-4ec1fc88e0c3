using Microsoft.AspNetCore.Mvc;
using NotificationContract.Models;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Documentation;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net;
using Microsoft.AspNetCore.Authorization;

namespace NotifyMasterApi.Controllers;

#region Email Response Models

/// <summary>
/// Email send operation result.
/// </summary>
public class EmailSendResult
{
    /// <summary>
    /// Unique message identifier
    /// </summary>
    [Description("Unique identifier for the sent email")]
    public string MessageId { get; set; } = "";

    /// <summary>
    /// Email provider used for sending
    /// </summary>
    [Description("Name of the email provider used")]
    public string Provider { get; set; } = "";

    /// <summary>
    /// Current status of the email
    /// </summary>
    [Description("Current delivery status")]
    public string Status { get; set; } = "";

    /// <summary>
    /// Timestamp when email was sent
    /// </summary>
    [Description("When the email was sent")]
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Recipient email address
    /// </summary>
    [Description("Email address of the recipient")]
    public string Recipient { get; set; } = "";

    /// <summary>
    /// Email subject
    /// </summary>
    [Description("Subject line of the email")]
    public string Subject { get; set; } = "";
}

/// <summary>
/// Bulk email operation result.
/// </summary>
public class BulkEmailResult
{
    /// <summary>
    /// Total number of emails processed
    /// </summary>
    [Description("Total number of emails in the batch")]
    public int TotalEmails { get; set; }

    /// <summary>
    /// Number of successfully sent emails
    /// </summary>
    [Description("Number of emails sent successfully")]
    public int SuccessfulSends { get; set; }

    /// <summary>
    /// Number of failed email sends
    /// </summary>
    [Description("Number of emails that failed to send")]
    public int FailedSends { get; set; }

    /// <summary>
    /// Batch processing identifier
    /// </summary>
    [Description("Unique identifier for the bulk operation")]
    public string BatchId { get; set; } = "";

    /// <summary>
    /// Individual email results
    /// </summary>
    [Description("Results for each individual email")]
    public List<EmailSendResult> Results { get; set; } = new();

    /// <summary>
    /// Processing duration
    /// </summary>
    [Description("Time taken to process the batch")]
    public TimeSpan ProcessingTime { get; set; }
}

/// <summary>
/// Email provider information.
/// </summary>
public class EmailProviderInfo
{
    /// <summary>
    /// Provider unique key
    /// </summary>
    [Description("Unique identifier for the provider")]
    public string Key { get; set; } = "";

    /// <summary>
    /// Provider display name
    /// </summary>
    [Description("Human-readable provider name")]
    public string Name { get; set; } = "";

    /// <summary>
    /// Whether this provider is currently active
    /// </summary>
    [Description("Whether this provider is currently active")]
    public bool IsActive { get; set; }

    /// <summary>
    /// Provider configuration status
    /// </summary>
    [Description("Configuration status of the provider")]
    public string Status { get; set; } = "";

    /// <summary>
    /// Supported features
    /// </summary>
    [Description("List of features supported by this provider")]
    public List<string> Features { get; set; } = new();

    /// <summary>
    /// Rate limits for this provider
    /// </summary>
    [Description("Rate limiting information")]
    public Dictionary<string, object> RateLimits { get; set; } = new();
}

#endregion

/// <summary>
/// 📧 Email Notification Service
/// </summary>
/// <remarks>
/// **Professional email delivery with multiple provider support and advanced features.**
///
/// ### 🎯 Key Features
/// - **Multi-Provider Support**: SendGrid, SMTP, Amazon SES, Mailgun, and more
/// - **Rich Content**: HTML templates, plain text, attachments, inline images
/// - **Bulk Operations**: Send thousands of emails efficiently with batching
/// - **Template Engine**: Dynamic content with variable substitution
/// - **Delivery Tracking**: Real-time status updates and delivery confirmations
/// - **Provider Failover**: Automatic switching on provider failures
///
/// ### 📊 Supported Providers
/// | Provider | Features | Rate Limits |
/// |----------|----------|-------------|
/// | **SendGrid** | Templates, Analytics, Webhooks | 100 emails/second |
/// | **SMTP** | Custom servers, TLS/SSL | Server dependent |
/// | **Amazon SES** | High volume, bounce handling | 14 emails/second |
/// | **Mailgun** | EU/US regions, validation | 10,000 emails/hour |
///
/// ### 🔧 Authentication Required
/// Use one of the following methods:
/// - **JWT Token**: `Authorization: Bearer {token}`
/// - **API Key**: `X-API-Key: {your-api-key}`
/// - **Basic Auth**: `Authorization: Basic {base64-credentials}`
///
/// ### 📈 Performance
/// - **Average Response Time**: < 200ms for single emails
/// - **Bulk Processing**: Up to 1,000 emails per request
/// - **Rate Limiting**: 1,000 requests per minute per user
/// - **Retry Logic**: Automatic retries with exponential backoff
///
/// ### 🔔 Real-time Events
/// Subscribe to WebSocket events at `/events/email` for:
/// - Email sent confirmations
/// - Delivery status updates
/// - Provider switch notifications
/// - Error alerts and warnings
/// </remarks>
[ApiController]
[Route("api/v1/email")]
[Produces("application/json")]
[Tags("📧 Email")]
[Authorize]
public class SimpleEmailController : ControllerBase
{
    private readonly IEmailGateway _emailGateway;
    private readonly ILogger<SimpleEmailController> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="SimpleEmailController"/> class.
    /// </summary>
    /// <param name="emailGateway">The email gateway service for handling email operations</param>
    /// <param name="logger">The logger instance for this controller</param>
    /// <exception cref="ArgumentNullException">Thrown when emailGateway or logger is null</exception>
    public SimpleEmailController(IEmailGateway emailGateway, ILogger<SimpleEmailController> logger)
    {
        _emailGateway = emailGateway ?? throw new ArgumentNullException(nameof(emailGateway));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 📤 Send Single Email
    /// </summary>
    /// <remarks>
    /// **Send a single email through the configured email provider with rich content support.**
    ///
    /// ### 🎯 Features
    /// - **Rich HTML Content**: Full HTML support with CSS styling
    /// - **Plain Text Fallback**: Automatic plain text generation
    /// - **Attachments**: Support for file attachments (Base64 encoded)
    /// - **Custom Headers**: Add custom email headers
    /// - **Template Variables**: Dynamic content substitution
    /// - **Priority Levels**: Normal, High, Low priority delivery
    ///
    /// ### 📝 Request Examples
    ///
    /// **Simple Email:**
    /// ```json
    /// {
    ///   "to": "<EMAIL>",
    ///   "subject": "Welcome to NotifyMaster!",
    ///   "body": "Thank you for joining us!",
    ///   "isHtml": false
    /// }
    /// ```
    ///
    /// **Rich HTML Email:**
    /// ```json
    /// {
    ///   "to": "<EMAIL>",
    ///   "cc": ["<EMAIL>"],
    ///   "subject": "🎉 Welcome to NotifyMaster!",
    ///   "body": "&lt;h1&gt;Welcome!&lt;/h1&gt;&lt;p&gt;Thank you for joining us!&lt;/p&gt;",
    ///   "isHtml": true,
    ///   "priority": "High",
    ///   "attachments": [
    ///     {
    ///       "filename": "welcome.pdf",
    ///       "content": "base64-encoded-content",
    ///       "contentType": "application/pdf"
    ///     }
    ///   ]
    /// }
    /// ```
    ///
    /// ### ✅ Success Response
    /// ```json
    /// {
    ///   "success": true,
    ///   "message": "Email sent successfully",
    ///   "data": {
    ///     "messageId": "msg_12345",
    ///     "provider": "SendGrid",
    ///     "status": "Sent",
    ///     "sentAt": "2024-01-01T12:00:00Z"
    ///   }
    /// }
    /// ```
    ///
    /// ### 🔔 Real-time Events
    /// Listen to `/events/email` WebSocket for delivery updates:
    /// - `email.sent` - Email successfully sent
    /// - `email.delivered` - Email delivered to recipient
    /// - `email.bounced` - Email bounced back
    /// - `email.failed` - Delivery failed
    /// </remarks>
    /// <param name="request">Email message request with recipient, subject, and content</param>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>Email sending result with message ID and status</returns>
    /// <response code="200">✅ Email sent successfully</response>
    /// <response code="400">❌ Invalid request data</response>
    /// <response code="401">🔒 Authentication required</response>
    /// <response code="422">⚠️ Validation failed</response>
    /// <response code="429">🚫 Rate limit exceeded</response>
    /// <response code="500">💥 Internal server error</response>
    ///   "isHtml": true
    /// }
    /// </example>
    [HttpPost("send")]
    [ProducesResponseType(typeof(ApiResponse<EmailSendResult>), 200)]
    [ProducesResponseType(typeof(ApiResponse<object>), 400)]
    [ProducesResponseType(typeof(ApiResponse<object>), 401)]
    [ProducesResponseType(typeof(ValidationProblemDetails), 422)]
    [ProducesResponseType(typeof(ApiResponse<object>), 429)]
    [ProducesResponseType(typeof(ApiResponse<object>), 500)]
    [ApiExample("""
    {
      "to": "<EMAIL>",
      "subject": "🎉 Welcome to NotifyMaster!",
      "body": "<h1>Welcome!</h1><p>Thank you for joining us!</p>",
      "isHtml": true,
      "priority": "Normal"
    }
    """, "Send a welcome email with HTML content")]
    [ApiResponseExample(200, """
    {
      "success": true,
      "message": "Email sent successfully",
      "data": {
        "messageId": "msg_12345",
        "provider": "SendGrid",
        "status": "Sent",
        "sentAt": "2024-01-01T12:00:00Z"
      },
      "requestId": "req_67890",
      "timestamp": "2024-01-01T12:00:00Z"
    }
    """, "Successful email send response")]
    [ApiPerformance("< 200ms", "1,000 requests/minute")]
    public async Task<IActionResult> SendEmail(
        [FromBody, Required] EmailMessageRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the request
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid email request received: {ValidationErrors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return UnprocessableEntity(ModelState);
            }

            _logger.LogInformation("Sending email to {To} with subject '{Subject}'",
                request.To, request.Subject);

            var result = await _emailGateway.SendAsync(request);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Email successfully sent to {To}, MessageId: {MessageId}",
                    request.To, result.MessageId);

                return Ok(new {
                    success = true,
                    messageId = result.MessageId,
                    timestamp = DateTime.UtcNow,
                    recipient = request.To
                });
            }

            _logger.LogWarning("Email sending failed for {To}: {Error}", request.To, result.ErrorMessage);
            return BadRequest(new {
                success = false,
                error = result.ErrorMessage,
                timestamp = DateTime.UtcNow,
                recipient = request.To
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument provided for email sending to {To}", request?.To);
            return BadRequest(new { success = false, error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending email to {To}", request?.To);
            return StatusCode(500, new {
                success = false,
                error = "An internal server error occurred while sending the email",
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Sends multiple email messages in a single batch operation.
    /// </summary>
    /// <param name="request">The bulk email request containing multiple email messages</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A response with overall status and individual results for each email</returns>
    /// <response code="200">Bulk operation completed (check individual results for per-email status)</response>
    /// <response code="400">Invalid request data</response>
    /// <response code="422">Request validation failed</response>
    /// <response code="500">Internal server error occurred</response>
    /// <example>
    /// POST /api/v1/email/send-bulk
    /// {
    ///   "messages": [
    ///     {
    ///       "to": "<EMAIL>",
    ///       "subject": "Welcome",
    ///       "body": "Welcome to our service!"
    ///     },
    ///     {
    ///       "to": "<EMAIL>",
    ///       "subject": "Welcome",
    ///       "body": "Welcome to our service!"
    ///     }
    ///   ]
    /// }
    /// </example>
    [HttpPost("send-bulk")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ValidationProblemDetails), (int)HttpStatusCode.UnprocessableEntity)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> SendBulkEmail(
        [FromBody, Required] BulkEmailRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the request
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid bulk email request received: {ValidationErrors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return UnprocessableEntity(ModelState);
            }

            var messageCount = request.Messages?.Count() ?? 0;
            if (messageCount == 0)
            {
                return BadRequest(new { success = false, error = "At least one email message is required" });
            }

            _logger.LogInformation("Sending bulk email to {Count} recipients", messageCount);

            var result = await _emailGateway.SendBulkAsync(request);

            var successCount = result.Results.Count(r => r.IsSuccess);
            var failureCount = result.Results.Count(r => !r.IsSuccess);

            _logger.LogInformation("Bulk email operation completed: {SuccessCount} successful, {FailureCount} failed",
                successCount, failureCount);

            return Ok(new {
                success = result.IsSuccess,
                totalSent = successCount,
                totalFailed = failureCount,
                totalRequested = messageCount,
                timestamp = DateTime.UtcNow,
                results = result.Results.Select(r => new {
                    success = r.IsSuccess,
                    messageId = r.MessageId,
                    error = r.ErrorMessage
                })
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument provided for bulk email sending");
            return BadRequest(new { success = false, error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending bulk email");
            return StatusCode(500, new {
                success = false,
                error = "An internal server error occurred while sending bulk emails",
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Retrieves a list of available email providers and their status.
    /// </summary>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A list of available email providers with their configuration status</returns>
    /// <response code="200">Successfully retrieved provider list</response>
    /// <response code="500">Internal server error occurred</response>
    /// <example>
    /// GET /api/v1/email/providers
    /// </example>
    [HttpGet("providers")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> GetProviders(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Retrieving available email providers");

            var providers = await _emailGateway.GetProvidersAsync();

            _logger.LogInformation("Retrieved {Count} email providers", providers?.Count() ?? 0);

            return Ok(new {
                success = true,
                providers = providers,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error retrieving email providers");
            return StatusCode(500, new {
                success = false,
                error = "An internal server error occurred while retrieving email providers",
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Tests the configuration and connectivity of a specific email provider.
    /// </summary>
    /// <param name="provider">The name of the email provider to test</param>
    /// <param name="testEmail">Optional test email address to send a test message to</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>The result of the provider test including connectivity and configuration status</returns>
    /// <response code="200">Provider test completed (check result for success/failure details)</response>
    /// <response code="400">Invalid provider name or test parameters</response>
    /// <response code="404">Provider not found</response>
    /// <response code="500">Internal server error occurred</response>
    /// <example>
    /// POST /api/v1/email/test/sendgrid?testEmail=<EMAIL>
    /// </example>
    [HttpPost("test/{provider}")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.NotFound)]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> TestProvider(
        [FromRoute, Required] string provider,
        [FromQuery] string? testEmail = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate provider parameter
            if (string.IsNullOrWhiteSpace(provider))
            {
                return BadRequest(new { success = false, error = "Provider name is required" });
            }

            // Validate test email if provided
            if (!string.IsNullOrWhiteSpace(testEmail) && !IsValidEmail(testEmail))
            {
                return BadRequest(new { success = false, error = "Invalid test email address format" });
            }

            _logger.LogInformation("Testing email provider '{Provider}' with test email '{TestEmail}'",
                provider, testEmail ?? "none");

            var result = await _emailGateway.TestProviderAsync(provider, testEmail);

            _logger.LogInformation("Email provider test completed for '{Provider}': {Success}",
                provider, result?.IsSuccess ?? false);

            return Ok(new {
                success = true,
                provider = provider,
                testEmail = testEmail,
                result = result,
                timestamp = DateTime.UtcNow
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument for testing email provider '{Provider}'", provider);
            return BadRequest(new { success = false, error = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Provider '{Provider}' not found or not available", provider);
            return NotFound(new {
                success = false,
                error = $"Email provider '{provider}' not found or not available",
                provider = provider
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error testing email provider '{Provider}'", provider);
            return StatusCode(500, new {
                success = false,
                error = "An internal server error occurred while testing the email provider",
                provider = provider,
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Validates if the provided string is a valid email address format.
    /// </summary>
    /// <param name="email">The email address to validate</param>
    /// <returns>True if the email format is valid, false otherwise</returns>
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
