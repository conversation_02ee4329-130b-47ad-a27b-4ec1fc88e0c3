# Plugin Development Guide

This guide provides comprehensive information for developing custom notification plugins for the NotificationService.

## Overview

The NotificationService uses a plugin-based architecture that allows you to create custom notification providers without modifying the core service. Plugins are dynamically loaded at runtime and can be hot-swapped without service interruption.

## Plugin Types

The system supports three types of notification plugins:

- **Email Plugins**: Implement `IEmailPlugin` for email providers
- **SMS Plugins**: Implement `ISmsPlugin` for SMS providers  
- **Push Notification Plugins**: Implement `IPushNotificationPlugin` for push providers

## Getting Started

### 1. Create a New Plugin Project

```bash
# Create a new class library project
dotnet new classlib -n Plugin.Email.MyProvider
cd Plugin.Email.MyProvider

# Add required package references
dotnet add package EmailContract
dotnet add package PluginContract
dotnet add package Microsoft.Extensions.Configuration
dotnet add package Microsoft.Extensions.Logging
```

### 2. Project Structure

```
Plugin.Email.MyProvider/
├── Plugin.Email.MyProvider.csproj
├── MyEmailPlugin.cs
├── Configuration/
│   ├── MyProviderConfiguration.cs
│   └── ConfigurationValidator.cs
├── Services/
│   └── MyProviderService.cs
├── Models/
│   └── MyProviderModels.cs
├── manifest.json
└── README.md
```

### 3. Implement the Plugin Interface

```csharp
using EmailContract.Interfaces;
using EmailContract.Models;
using PluginContract.Attributes;
using PluginContract.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

[PluginManifest("MyProvider", "1.0.0", "Email", "My Custom Email Provider")]
public class MyEmailPlugin : IEmailPlugin
{
    private readonly ILogger<MyEmailPlugin> _logger;
    private readonly IConfiguration _configuration;
    private MyProviderConfiguration _config;

    public string Name => "MyProvider";
    public string Version => "1.0.0";
    public string Type => "Email";
    public string Description => "My Custom Email Provider";
    public string Author => "Your Name";

    public MyEmailPlugin(ILogger<MyEmailPlugin> logger, IConfiguration configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }

    public async Task<bool> InitializeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _config = _configuration.GetSection($"Plugins:{Name}").Get<MyProviderConfiguration>();
            
            if (_config == null)
            {
                _logger.LogError("Configuration not found for plugin {PluginName}", Name);
                return false;
            }

            // Validate configuration
            var validationResult = await ValidateConfigurationAsync(cancellationToken);
            if (!validationResult)
            {
                _logger.LogError("Configuration validation failed for plugin {PluginName}", Name);
                return false;
            }

            // Initialize provider-specific resources
            await InitializeProviderAsync(cancellationToken);

            _logger.LogInformation("Plugin {PluginName} initialized successfully", Name);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize plugin {PluginName}", Name);
            return false;
        }
    }

    public async Task<EmailResponse> SendEmailAsync(SendEmailRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Sending email via {PluginName} to {Recipient}", Name, request.To);

            // Validate request
            var validationResult = ValidateRequest(request);
            if (!validationResult.IsValid)
            {
                return EmailResponse.CreateFailure(validationResult.ErrorMessage);
            }

            // Send email using your provider's API
            var result = await SendEmailInternalAsync(request, cancellationToken);

            _logger.LogInformation("Email sent successfully via {PluginName}. MessageId: {MessageId}", 
                Name, result.MessageId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email via {PluginName}", Name);
            return EmailResponse.CreateFailure($"Failed to send email: {ex.Message}");
        }
    }

    public async Task<bool> ValidateConfigurationAsync(CancellationToken cancellationToken = default)
    {
        if (_config == null) return false;

        // Validate required configuration properties
        if (string.IsNullOrEmpty(_config.ApiKey))
        {
            _logger.LogError("ApiKey is required for {PluginName}", Name);
            return false;
        }

        if (string.IsNullOrEmpty(_config.FromEmail))
        {
            _logger.LogError("FromEmail is required for {PluginName}", Name);
            return false;
        }

        // Test connection to provider
        try
        {
            await TestConnectionAsync(cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Connection test failed for {PluginName}", Name);
            return false;
        }
    }

    public async Task<PluginHealthStatus> GetHealthStatusAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Perform health checks
            var isHealthy = await TestConnectionAsync(cancellationToken);
            
            return new PluginHealthStatus
            {
                IsHealthy = isHealthy,
                Status = isHealthy ? "Healthy" : "Unhealthy",
                LastChecked = DateTime.UtcNow,
                Details = new Dictionary<string, object>
                {
                    ["provider"] = Name,
                    ["version"] = Version,
                    ["configurationValid"] = _config != null
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed for {PluginName}", Name);
            
            return new PluginHealthStatus
            {
                IsHealthy = false,
                Status = "Unhealthy",
                LastChecked = DateTime.UtcNow,
                ErrorMessage = ex.Message
            };
        }
    }

    public Task DisposeAsync()
    {
        // Clean up resources
        return Task.CompletedTask;
    }

    private async Task InitializeProviderAsync(CancellationToken cancellationToken)
    {
        // Initialize provider-specific resources (HTTP clients, etc.)
    }

    private async Task<EmailResponse> SendEmailInternalAsync(SendEmailRequest request, CancellationToken cancellationToken)
    {
        // Implement your provider's email sending logic here
        // This is where you would call your email provider's API
        
        // Example implementation:
        using var httpClient = new HttpClient();
        
        var payload = new
        {
            to = request.To,
            subject = request.Subject,
            html = request.IsHtml ? request.Body : null,
            text = !request.IsHtml ? request.Body : null,
            from = _config.FromEmail
        };

        var response = await httpClient.PostAsJsonAsync(
            $"{_config.ApiEndpoint}/send", 
            payload, 
            cancellationToken);

        if (response.IsSuccessStatusCode)
        {
            var responseData = await response.Content.ReadFromJsonAsync<dynamic>(cancellationToken);
            return EmailResponse.CreateSuccess(responseData?.id?.ToString() ?? Guid.NewGuid().ToString());
        }
        else
        {
            var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
            return EmailResponse.CreateFailure($"Provider error: {errorContent}");
        }
    }

    private ValidationResult ValidateRequest(SendEmailRequest request)
    {
        if (string.IsNullOrEmpty(request.To))
            return new ValidationResult(false, "Recipient email is required");

        if (string.IsNullOrEmpty(request.Subject))
            return new ValidationResult(false, "Email subject is required");

        if (string.IsNullOrEmpty(request.Body))
            return new ValidationResult(false, "Email body is required");

        // Add more validation as needed
        return new ValidationResult(true);
    }

    private async Task<bool> TestConnectionAsync(CancellationToken cancellationToken)
    {
        // Test connection to your provider's API
        try
        {
            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_config.ApiKey}");
            
            var response = await httpClient.GetAsync($"{_config.ApiEndpoint}/health", cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }
}
```

### 4. Configuration Model

```csharp
public class MyProviderConfiguration
{
    public string ApiKey { get; set; } = string.Empty;
    public string ApiEndpoint { get; set; } = string.Empty;
    public string FromEmail { get; set; } = string.Empty;
    public string FromName { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; } = 30;
    public int MaxRetries { get; set; } = 3;
    public bool IsEnabled { get; set; } = true;
}
```

### 5. Plugin Manifest

Create a `manifest.json` file in your plugin project:

```json
{
  "name": "MyProvider",
  "version": "1.0.0",
  "type": "Email",
  "description": "My Custom Email Provider",
  "author": "Your Name",
  "website": "https://your-website.com",
  "license": "MIT",
  "dependencies": [
    {
      "name": "EmailContract",
      "version": "1.0.0"
    },
    {
      "name": "PluginContract", 
      "version": "1.0.0"
    }
  ],
  "configuration": {
    "apiKey": {
      "type": "string",
      "required": true,
      "description": "API key for the email service",
      "sensitive": true
    },
    "apiEndpoint": {
      "type": "string",
      "required": true,
      "description": "API endpoint URL",
      "default": "https://api.myprovider.com/v1"
    },
    "fromEmail": {
      "type": "string",
      "required": true,
      "description": "Default sender email address",
      "validation": "email"
    },
    "fromName": {
      "type": "string",
      "required": false,
      "description": "Default sender name"
    },
    "timeoutSeconds": {
      "type": "integer",
      "required": false,
      "description": "Request timeout in seconds",
      "default": 30,
      "minimum": 1,
      "maximum": 300
    }
  },
  "capabilities": [
    "html",
    "attachments",
    "cc",
    "bcc",
    "customHeaders"
  ]
}
```

## Best Practices

### Error Handling

- Always wrap operations in try-catch blocks
- Log errors with appropriate log levels
- Return meaningful error messages
- Don't throw exceptions from plugin methods

### Configuration

- Validate all configuration values
- Provide sensible defaults
- Support environment variable overrides
- Mark sensitive values in the manifest

### Performance

- Use async/await for all I/O operations
- Implement proper timeout handling
- Cache expensive operations when possible
- Dispose of resources properly

### Logging

- Use structured logging with correlation IDs
- Log at appropriate levels (Debug, Info, Warning, Error)
- Include relevant context in log messages
- Don't log sensitive information

### Testing

- Write comprehensive unit tests
- Mock external dependencies
- Test error scenarios
- Validate configuration handling

## Plugin Lifecycle

1. **Discovery**: Plugin assemblies are scanned for `[PluginManifest]` attributes
2. **Validation**: Plugin manifest and dependencies are validated
3. **Loading**: Plugin assembly is loaded into an isolated context
4. **Initialization**: `InitializeAsync()` is called with configuration
5. **Health Check**: Regular health checks via `GetHealthStatusAsync()`
6. **Usage**: Plugin methods are called to send notifications
7. **Unloading**: Plugin is gracefully disposed when unloaded

## Deployment

### Build and Package

```bash
# Build the plugin
dotnet build -c Release

# Create plugin package
dotnet pack -c Release

# Copy to plugins directory
cp bin/Release/net9.0/Plugin.Email.MyProvider.dll /path/to/plugins/
cp manifest.json /path/to/plugins/
```

### Configuration

Add your plugin configuration to `appsettings.json`:

```json
{
  "Plugins": {
    "MyProvider": {
      "ApiKey": "your-api-key",
      "ApiEndpoint": "https://api.myprovider.com/v1",
      "FromEmail": "<EMAIL>",
      "FromName": "Your Company",
      "IsEnabled": true
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **Plugin Not Loading**
   - Check manifest.json syntax
   - Verify all dependencies are available
   - Check plugin directory permissions

2. **Configuration Errors**
   - Validate configuration schema
   - Check for missing required values
   - Verify environment variable names

3. **Runtime Errors**
   - Check plugin logs for exceptions
   - Verify external service connectivity
   - Test configuration values

### Debugging

- Enable debug logging for your plugin
- Use the plugin health check endpoint
- Monitor plugin metrics and status
- Check the main service logs for plugin-related errors

## Examples

See the following example plugins in the repository:
- `Plugin.Email.SendGrid` - SendGrid email provider
- `Plugin.SMS.Twilio` - Twilio SMS provider
- `Plugin.Push.Firebase` - Firebase push notification provider

## Support

For plugin development support:
- Review existing plugin implementations
- Check the plugin development documentation
- Open an issue on GitHub for specific questions
- Join the developer community discussions
