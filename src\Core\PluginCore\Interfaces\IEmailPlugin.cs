using EmailContract.Models;
using PluginContract.Models;
using PluginContract.Interfaces;

namespace PluginCore.Interfaces;
/// <summary>
///  Interface for email plugins
/// </summary>
public interface IEmailPlugin : INotificationPlugin
{
    /// <summary>
    ///  Sends an email
    /// </summary>
    /// <param name="request"> Email request </param>
    /// <returns> Email response </returns>
    Task<NotificationResponse> SendAsync(SendEmailRequest request);
    /// <summary>
    ///  Sends multiple emails
    /// </summary>
    /// <param name="requests"> Email requests </param>
    /// <returns> Email response </returns>
    Task<NotificationResponse> SendBulkAsync(IEnumerable<SendEmailRequest> requests);
    /// <summary>
    ///  Gets the status of an email message
    /// </summary>
    /// <param name="messageId"> Message ID </param>
    /// <returns> Email response </returns>
    Task<NotificationResponse> GetMessageStatusAsync(string messageId);
    /// <summary>
    ///  Gets the history of email messages for a given email address
    /// </summary>
    /// <param name="emailAddress"> Email address </param>
    /// <returns> Email response </returns>
    Task<NotificationResponse> GetMessageHistoryAsync(string emailAddress);
    /// <summary>
    ///  Resends an email message
    /// </summary>
    /// <param name="messageId"> Message ID </param>
    /// <returns> Email response </returns>
    Task<NotificationResponse> ResendMessageAsync(string messageId);
    /// <summary>
    ///  Validates the configuration for the plugin
    /// </summary>
    /// <returns> True if the configuration is valid, false otherwise </returns>
    Task<bool> ValidateConfigurationAsync();
}
