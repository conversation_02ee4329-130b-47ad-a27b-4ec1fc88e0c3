using EmailContract.Models;
using PluginContract.Models;
using PluginContract.Interfaces;

namespace PluginCore.Interfaces;

public interface IEmailPlugin : INotificationPlugin
{
    Task<NotificationResponse> SendAsync(SendEmailRequest request);
    Task<NotificationResponse> SendBulkAsync(IEnumerable<SendEmailRequest> requests);
    Task<NotificationResponse> GetMessageStatusAsync(string messageId);
    Task<NotificationResponse> GetMessageHistoryAsync(string emailAddress);
    Task<NotificationResponse> ResendMessageAsync(string messageId);
    Task<bool> ValidateConfigurationAsync();
}
