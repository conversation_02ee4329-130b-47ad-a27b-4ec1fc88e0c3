using EmailContract.Models;
using EmailService.Library.Configuration;
using EmailService.Library.Interfaces;
using EmailService.Library.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace EmailService.Library.Tests.Services;

public class EmailSenderServiceTests
{
    private readonly Mock<ISmtpClient> _mockSmtpClient;
    private readonly Mock<IOptions<EmailServiceSettings>> _mockOptions;
    private readonly Mock<ILogger<EmailSenderService>> _mockLogger;
    private readonly EmailSenderService _emailSenderService;
    private readonly EmailServiceSettings _settings;

    public EmailSenderServiceTests()
    {
        _mockSmtpClient = new Mock<ISmtpClient>();
        _mockOptions = new Mock<IOptions<EmailServiceSettings>>();
        _mockLogger = new Mock<ILogger<EmailSenderService>>();

        _settings = new EmailServiceSettings
        {
            SmtpHost = "smtp.example.com",
            SmtpPort = 587,
            SmtpUsername = "<EMAIL>",
            SmtpPassword = "password",
            EnableSsl = true,
            FromEmail = "<EMAIL>",
            FromName = "Test Service"
        };

        _mockOptions.Setup(x => x.Value).Returns(_settings);
        _emailSenderService = new EmailSenderService(_mockSmtpClient.Object, _mockOptions.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task SendAsync_ValidRequest_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new SendEmailRequest(
            to: "<EMAIL>",
            subject: "Test Subject",
            body: "Test Body"
        );

        _mockSmtpClient.Setup(x => x.SendAsync(It.IsAny<object>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _emailSenderService.SendAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.MessageId);
        _mockSmtpClient.Verify(x => x.SendAsync(It.IsAny<object>()), Times.Once);
    }

    [Fact]
    public async Task SendAsync_WithAttachments_ReturnsSuccessResponse()
    {
        // Arrange
        var attachments = new List<EmailAttachment>
        {
            new EmailAttachment("test.txt", System.Text.Encoding.UTF8.GetBytes("Test content"), "text/plain")
        };

        var request = new SendEmailRequest(
            to: "<EMAIL>",
            subject: "Test Subject",
            body: "Test Body",
            attachments: attachments
        );

        _mockSmtpClient.Setup(x => x.SendAsync(It.IsAny<object>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _emailSenderService.SendAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.MessageId);
        _mockSmtpClient.Verify(x => x.SendAsync(It.IsAny<object>()), Times.Once);
    }

    [Fact]
    public async Task SendAsync_WithCcAndBcc_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new SendEmailRequest(
            to: "<EMAIL>",
            subject: "Test Subject",
            body: "Test Body",
            cc: new List<string> { "<EMAIL>" },
            bcc: new List<string> { "<EMAIL>" }
        );

        _mockSmtpClient.Setup(x => x.SendAsync(It.IsAny<object>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _emailSenderService.SendAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.MessageId);
        _mockSmtpClient.Verify(x => x.SendAsync(It.IsAny<object>()), Times.Once);
    }

    [Fact]
    public async Task SendAsync_SmtpException_ReturnsFailureResponse()
    {
        // Arrange
        var request = new SendEmailRequest(
            to: "<EMAIL>",
            subject: "Test Subject",
            body: "Test Body"
        );

        _mockSmtpClient.Setup(x => x.SendAsync(It.IsAny<object>()))
            .ThrowsAsync(new Exception("SMTP connection failed"));

        // Act
        var result = await _emailSenderService.SendAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("SMTP connection failed", result.ErrorMessage);
    }

    [Fact]
    public async Task SendAsync_InvalidEmailAddress_ReturnsFailureResponse()
    {
        // Arrange
        var request = new SendEmailRequest(
            to: "invalid-email",
            subject: "Test Subject",
            body: "Test Body"
        );

        _mockSmtpClient.Setup(x => x.SendAsync(It.IsAny<object>()))
            .ThrowsAsync(new FormatException("Invalid email address"));

        // Act
        var result = await _emailSenderService.SendAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Invalid email address", result.ErrorMessage);
    }

    [Fact]
    public async Task SendAsync_HtmlBody_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new SendEmailRequest(
            to: "<EMAIL>",
            subject: "Test Subject",
            body: "<html><body><h1>Test HTML Body</h1></body></html>",
            isHtml: true
        );

        _mockSmtpClient.Setup(x => x.SendAsync(It.IsAny<object>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _emailSenderService.SendAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.MessageId);
        _mockSmtpClient.Verify(x => x.SendAsync(It.IsAny<object>()), Times.Once);
    }

    [Fact]
    public async Task SendAsync_CustomFromAddress_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new SendEmailRequest(
            to: "<EMAIL>",
            subject: "Test Subject",
            body: "Test Body",
            from: "<EMAIL>"
        );

        _mockSmtpClient.Setup(x => x.SendAsync(It.IsAny<object>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _emailSenderService.SendAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.MessageId);
        _mockSmtpClient.Verify(x => x.SendAsync(It.IsAny<object>()), Times.Once);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task SendAsync_EmptyToAddress_ReturnsFailureResponse(string toAddress)
    {
        // Arrange
        var request = new SendEmailRequest(
            to: toAddress,
            subject: "Test Subject",
            body: "Test Body"
        );

        // Act
        var result = await _emailSenderService.SendAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("To address is required", result.ErrorMessage);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task SendAsync_EmptySubject_ReturnsFailureResponse(string subject)
    {
        // Arrange
        var request = new SendEmailRequest(
            to: "<EMAIL>",
            subject: subject,
            body: "Test Body"
        );

        // Act
        var result = await _emailSenderService.SendAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Subject is required", result.ErrorMessage);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task SendAsync_EmptyBody_ReturnsFailureResponse(string body)
    {
        // Arrange
        var request = new SendEmailRequest(
            to: "<EMAIL>",
            subject: "Test Subject",
            body: body
        );

        // Act
        var result = await _emailSenderService.SendAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Body is required", result.ErrorMessage);
    }
}
