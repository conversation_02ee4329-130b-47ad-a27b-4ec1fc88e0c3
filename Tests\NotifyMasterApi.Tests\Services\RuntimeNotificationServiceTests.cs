using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using NotifyMasterApi.Services;
using NotifyMasterApi.Interfaces;
using SmsContract.Models;
using EmailContract.Models;
using PushNotificationContract.Models;
using Xunit;
using FluentAssertions;
using AutoFixture;
using AutoFixture.Xunit2;

namespace NotifyMasterApi.Tests.Services;

/// <summary>
/// Comprehensive unit tests for RuntimeNotificationService with failover and monitoring capabilities.
/// </summary>
public class RuntimeNotificationServiceTests : IDisposable
{
    private readonly Mock<ILogger<RuntimeNotificationService>> _mockLogger;
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<IServiceScope> _mockServiceScope;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<RuntimePluginManager> _mockPluginManager;
    private readonly IFixture _fixture;
    private readonly RuntimeNotificationService _service;

    public RuntimeNotificationServiceTests()
    {
        _mockLogger = new Mock<ILogger<RuntimeNotificationService>>();
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockServiceScope = new Mock<IServiceScope>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockPluginManager = new Mock<RuntimePluginManager>(
            Mock.Of<ILogger<RuntimePluginManager>>(),
            Mock.Of<IConfiguration>());
        _fixture = new Fixture();

        // Setup configuration defaults
        _mockConfiguration.Setup(c => c.GetValue<int>("RuntimeNotification:ProviderCooldownMinutes", 5))
            .Returns(5);

        // Setup service provider and scope
        _mockServiceProvider.Setup(sp => sp.CreateScope())
            .Returns(_mockServiceScope.Object);
        _mockServiceScope.Setup(s => s.ServiceProvider)
            .Returns(_mockServiceProvider.Object);

        // Setup plugin manager resolution
        _mockServiceProvider.Setup(sp => sp.GetRequiredService<IPluginManager>())
            .Returns(_mockPluginManager.Object);

        _service = new RuntimeNotificationService(
            _mockLogger.Object,
            _mockServiceProvider.Object,
            _mockConfiguration.Object);
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeSuccessfully()
    {
        // Arrange & Act
        var service = new RuntimeNotificationService(
            _mockLogger.Object,
            _mockServiceProvider.Object,
            _mockConfiguration.Object);

        // Assert
        service.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        Assert.Throws<ArgumentNullException>(() => new RuntimeNotificationService(
            null!,
            _mockServiceProvider.Object,
            _mockConfiguration.Object));
    }

    [Fact]
    public void Constructor_WithNullServiceProvider_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        Assert.Throws<ArgumentNullException>(() => new RuntimeNotificationService(
            _mockLogger.Object,
            null!,
            _mockConfiguration.Object));
    }

    [Fact]
    public void Constructor_WithNullConfiguration_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        Assert.Throws<ArgumentNullException>(() => new RuntimeNotificationService(
            _mockLogger.Object,
            _mockServiceProvider.Object,
            null!));
    }

    [Theory]
    [AutoData]
    public async Task SendSmsAsync_WithNullRequest_ShouldThrowArgumentNullException(string preferredProvider)
    {
        // Arrange, Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => 
            _service.SendSmsAsync(null!, preferredProvider));
    }

    [Theory]
    [AutoData]
    public async Task SendSmsAsync_WithValidRequest_ShouldReturnSuccessResponse(SendSmsRequest request)
    {
        // Arrange
        var mockPlugin = new Mock<RuntimePluginInstance>();
        mockPlugin.Setup(p => p.Provider).Returns("TestProvider");
        mockPlugin.Setup(p => p.Name).Returns("TestSmsPlugin");
        mockPlugin.Setup(p => p.SendNotificationAsync(request))
            .ReturnsAsync(new { IsSuccess = true, MessageId = "test-123" });

        var plugins = new List<RuntimePluginInstance> { mockPlugin.Object };
        _mockPluginManager.Setup(pm => pm.GetPluginsByTypeAsync("SMS"))
            .ReturnsAsync(plugins);

        // Act
        var result = await _service.SendSmsAsync(request);

        // Assert
        result.Should().NotBeNull();
        mockPlugin.Verify(p => p.SendNotificationAsync(request), Times.Once);
    }

    [Theory]
    [AutoData]
    public async Task SendSmsAsync_WithPreferredProvider_ShouldUsePreferredProviderFirst(SendSmsRequest request)
    {
        // Arrange
        var preferredProvider = "PreferredProvider";
        var mockPreferredPlugin = new Mock<RuntimePluginInstance>();
        mockPreferredPlugin.Setup(p => p.Provider).Returns(preferredProvider);
        mockPreferredPlugin.Setup(p => p.Name).Returns("PreferredSmsPlugin");
        mockPreferredPlugin.Setup(p => p.SendNotificationAsync(request))
            .ReturnsAsync(new { IsSuccess = true, MessageId = "preferred-123" });

        var mockFallbackPlugin = new Mock<RuntimePluginInstance>();
        mockFallbackPlugin.Setup(p => p.Provider).Returns("FallbackProvider");
        mockFallbackPlugin.Setup(p => p.Name).Returns("FallbackSmsPlugin");

        var plugins = new List<RuntimePluginInstance> { mockFallbackPlugin.Object, mockPreferredPlugin.Object };
        _mockPluginManager.Setup(pm => pm.GetPluginsByTypeAsync("SMS"))
            .ReturnsAsync(plugins);

        // Act
        var result = await _service.SendSmsAsync(request, preferredProvider);

        // Assert
        result.Should().NotBeNull();
        mockPreferredPlugin.Verify(p => p.SendNotificationAsync(request), Times.Once);
        mockFallbackPlugin.Verify(p => p.SendNotificationAsync(request), Times.Never);
    }

    [Theory]
    [AutoData]
    public async Task SendSmsAsync_WithFailingPreferredProvider_ShouldFailoverToAlternative(SendSmsRequest request)
    {
        // Arrange
        var preferredProvider = "PreferredProvider";
        var mockPreferredPlugin = new Mock<RuntimePluginInstance>();
        mockPreferredPlugin.Setup(p => p.Provider).Returns(preferredProvider);
        mockPreferredPlugin.Setup(p => p.Name).Returns("PreferredSmsPlugin");
        mockPreferredPlugin.Setup(p => p.SendNotificationAsync(request))
            .ThrowsAsync(new InvalidOperationException("Provider failed"));

        var mockFallbackPlugin = new Mock<RuntimePluginInstance>();
        mockFallbackPlugin.Setup(p => p.Provider).Returns("FallbackProvider");
        mockFallbackPlugin.Setup(p => p.Name).Returns("FallbackSmsPlugin");
        mockFallbackPlugin.Setup(p => p.SendNotificationAsync(request))
            .ReturnsAsync(new { IsSuccess = true, MessageId = "fallback-123" });

        var plugins = new List<RuntimePluginInstance> { mockPreferredPlugin.Object, mockFallbackPlugin.Object };
        _mockPluginManager.Setup(pm => pm.GetPluginsByTypeAsync("SMS"))
            .ReturnsAsync(plugins);

        // Act
        var result = await _service.SendSmsAsync(request, preferredProvider);

        // Assert
        result.Should().NotBeNull();
        mockPreferredPlugin.Verify(p => p.SendNotificationAsync(request), Times.Once);
        mockFallbackPlugin.Verify(p => p.SendNotificationAsync(request), Times.Once);
    }

    [Theory]
    [AutoData]
    public async Task SendSmsAsync_WithNoAvailablePlugins_ShouldReturnErrorResponse(SendSmsRequest request)
    {
        // Arrange
        var plugins = new List<RuntimePluginInstance>();
        _mockPluginManager.Setup(pm => pm.GetPluginsByTypeAsync("SMS"))
            .ReturnsAsync(plugins);

        // Act
        var result = await _service.SendSmsAsync(request);

        // Assert
        result.Should().NotBeNull();
        var errorResponse = result as dynamic;
        errorResponse?.IsSuccess.Should().Be(false);
        errorResponse?.ErrorMessage.Should().Contain("No SMS plugins available");
    }

    [Theory]
    [AutoData]
    public async Task SendSmsAsync_WithPluginManagerNotAvailable_ShouldReturnErrorResponse(SendSmsRequest request)
    {
        // Arrange
        _mockServiceProvider.Setup(sp => sp.GetRequiredService<IPluginManager>())
            .Returns(Mock.Of<IPluginManager>()); // Not a RuntimePluginManager

        // Act
        var result = await _service.SendSmsAsync(request);

        // Assert
        result.Should().NotBeNull();
        var errorResponse = result as dynamic;
        errorResponse?.IsSuccess.Should().Be(false);
        errorResponse?.ErrorMessage.Should().Contain("Plugin manager not available");
    }

    [Theory]
    [AutoData]
    public async Task SendSmsAsync_WithTimeout_ShouldFailoverToNextProvider(SendSmsRequest request)
    {
        // Arrange
        var mockSlowPlugin = new Mock<RuntimePluginInstance>();
        mockSlowPlugin.Setup(p => p.Provider).Returns("SlowProvider");
        mockSlowPlugin.Setup(p => p.Name).Returns("SlowSmsPlugin");
        mockSlowPlugin.Setup(p => p.SendNotificationAsync(request))
            .Returns(async () =>
            {
                await Task.Delay(TimeSpan.FromMinutes(1)); // Simulate timeout
                return new { IsSuccess = true };
            });

        var mockFastPlugin = new Mock<RuntimePluginInstance>();
        mockFastPlugin.Setup(p => p.Provider).Returns("FastProvider");
        mockFastPlugin.Setup(p => p.Name).Returns("FastSmsPlugin");
        mockFastPlugin.Setup(p => p.SendNotificationAsync(request))
            .ReturnsAsync(new { IsSuccess = true, MessageId = "fast-123" });

        var plugins = new List<RuntimePluginInstance> { mockSlowPlugin.Object, mockFastPlugin.Object };
        _mockPluginManager.Setup(pm => pm.GetPluginsByTypeAsync("SMS"))
            .ReturnsAsync(plugins);

        // Act
        var result = await _service.SendSmsAsync(request);

        // Assert
        result.Should().NotBeNull();
        mockSlowPlugin.Verify(p => p.SendNotificationAsync(request), Times.Once);
        mockFastPlugin.Verify(p => p.SendNotificationAsync(request), Times.Once);
    }

    [Theory]
    [AutoData]
    public async Task SendSmsAsync_WithAllProvidersFailingTimeout_ShouldReturnErrorResponse(SendSmsRequest request)
    {
        // Arrange
        var mockPlugin1 = new Mock<RuntimePluginInstance>();
        mockPlugin1.Setup(p => p.Provider).Returns("Provider1");
        mockPlugin1.Setup(p => p.Name).Returns("Plugin1");
        mockPlugin1.Setup(p => p.SendNotificationAsync(request))
            .Returns(async () =>
            {
                await Task.Delay(TimeSpan.FromMinutes(1)); // Simulate timeout
                return new { IsSuccess = true };
            });

        var mockPlugin2 = new Mock<RuntimePluginInstance>();
        mockPlugin2.Setup(p => p.Provider).Returns("Provider2");
        mockPlugin2.Setup(p => p.Name).Returns("Plugin2");
        mockPlugin2.Setup(p => p.SendNotificationAsync(request))
            .ThrowsAsync(new InvalidOperationException("Provider failed"));

        var plugins = new List<RuntimePluginInstance> { mockPlugin1.Object, mockPlugin2.Object };
        _mockPluginManager.Setup(pm => pm.GetPluginsByTypeAsync("SMS"))
            .ReturnsAsync(plugins);

        // Act
        var result = await _service.SendSmsAsync(request);

        // Assert
        result.Should().NotBeNull();
        var errorResponse = result as dynamic;
        errorResponse?.IsSuccess.Should().Be(false);
        errorResponse?.ErrorMessage.Should().Contain("All SMS providers failed");
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
