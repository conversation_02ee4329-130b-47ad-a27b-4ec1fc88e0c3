using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using NotifyMasterApi.Services;
using System.Reflection;
using Xunit;

namespace NotifyMasterApi.Tests.Services;

public class RuntimePluginManagerTests : IDisposable
{
    private readonly Mock<ILogger<RuntimePluginManager>> _loggerMock;
    private readonly Mock<IConfiguration> _configurationMock;
    private readonly RuntimePluginManager _pluginManager;
    private readonly string _testPluginsDirectory;

    public RuntimePluginManagerTests()
    {
        _loggerMock = new Mock<ILogger<RuntimePluginManager>>();
        _configurationMock = new Mock<IConfiguration>();
        
        // Create temporary test directory
        _testPluginsDirectory = Path.Combine(Path.GetTempPath(), "test-plugins", Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testPluginsDirectory);
        
        _configurationMock.Setup(c => c["Plugins:Directory"]).Returns(_testPluginsDirectory);
        
        _pluginManager = new RuntimePluginManager(_loggerMock.Object, _configurationMock.Object);
    }

    [Fact]
    public async Task GetLoadedPluginsAsync_WhenNoPluginsLoaded_ReturnsEmptyList()
    {
        // Act
        var plugins = await _pluginManager.GetLoadedPluginsAsync();

        // Assert
        plugins.Should().BeEmpty();
    }

    [Fact]
    public async Task LoadPluginAsync_WhenFileDoesNotExist_ReturnsFalse()
    {
        // Arrange
        var nonExistentPath = Path.Combine(_testPluginsDirectory, "nonexistent.dll");

        // Act
        var result = await _pluginManager.LoadPluginAsync(nonExistentPath);

        // Assert
        result.Should().BeFalse();
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Plugin file not found")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task LoadPluginAsync_WhenValidPlugin_ReturnsTrue()
    {
        // Arrange
        var testPluginPath = await CreateTestPluginAsync();

        // Act
        var result = await _pluginManager.LoadPluginAsync(testPluginPath);

        // Assert
        result.Should().BeTrue();
        
        var loadedPlugins = await _pluginManager.GetLoadedPluginsAsync();
        loadedPlugins.Should().HaveCount(1);
        
        var plugin = loadedPlugins.First();
        plugin.Name.Should().Be("Test Plugin");
        plugin.Version.Should().Be("1.0.0");
        plugin.Type.Should().Be(PluginContract.Enums.PluginType.Sms);
    }

    [Fact]
    public async Task UnloadPluginAsync_WhenPluginExists_ReturnsTrue()
    {
        // Arrange
        var testPluginPath = await CreateTestPluginAsync();
        await _pluginManager.LoadPluginAsync(testPluginPath);

        // Act
        var result = await _pluginManager.UnloadPluginAsync("Test Plugin");

        // Assert
        result.Should().BeTrue();
        
        var loadedPlugins = await _pluginManager.GetLoadedPluginsAsync();
        loadedPlugins.Should().BeEmpty();
    }

    [Fact]
    public async Task UnloadPluginAsync_WhenPluginDoesNotExist_ReturnsFalse()
    {
        // Act
        var result = await _pluginManager.UnloadPluginAsync("NonExistent Plugin");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task EnablePluginAsync_WhenPluginExists_ReturnsTrue()
    {
        // Arrange
        var testPluginPath = await CreateTestPluginAsync();
        await _pluginManager.LoadPluginAsync(testPluginPath);

        // Act
        var result = await _pluginManager.EnablePluginAsync("Test Plugin");

        // Assert
        result.Should().BeTrue();
        
        var plugin = await _pluginManager.GetPluginAsync("Test Plugin");
        plugin.Should().NotBeNull();
        plugin!.IsEnabled.Should().BeTrue();
    }

    [Fact]
    public async Task DisablePluginAsync_WhenPluginExists_ReturnsTrue()
    {
        // Arrange
        var testPluginPath = await CreateTestPluginAsync();
        await _pluginManager.LoadPluginAsync(testPluginPath);

        // Act
        var result = await _pluginManager.DisablePluginAsync("Test Plugin");

        // Assert
        result.Should().BeTrue();
        
        var plugin = await _pluginManager.GetPluginAsync("Test Plugin");
        plugin.Should().NotBeNull();
        plugin!.IsEnabled.Should().BeFalse();
    }

    [Fact]
    public async Task GetPluginAsync_WhenPluginExists_ReturnsPlugin()
    {
        // Arrange
        var testPluginPath = await CreateTestPluginAsync();
        await _pluginManager.LoadPluginAsync(testPluginPath);

        // Act
        var plugin = await _pluginManager.GetPluginAsync("Test Plugin");

        // Assert
        plugin.Should().NotBeNull();
        plugin!.Name.Should().Be("Test Plugin");
        plugin.Version.Should().Be("1.0.0");
    }

    [Fact]
    public async Task GetPluginAsync_WhenPluginDoesNotExist_ReturnsNull()
    {
        // Act
        var plugin = await _pluginManager.GetPluginAsync("NonExistent Plugin");

        // Assert
        plugin.Should().BeNull();
    }

    [Fact]
    public async Task LoadAllPluginsAsync_WhenMultiplePluginsExist_LoadsAllValidPlugins()
    {
        // Arrange
        var plugin1Path = await CreateTestPluginAsync("Plugin1", "1.0.0");
        var plugin2Path = await CreateTestPluginAsync("Plugin2", "2.0.0");
        
        // Create an invalid plugin file (not a valid assembly)
        var invalidPluginPath = Path.Combine(_testPluginsDirectory, "invalid.dll");
        await File.WriteAllTextAsync(invalidPluginPath, "invalid content");

        // Act
        var loadedCount = await _pluginManager.LoadAllPluginsAsync();

        // Assert
        loadedCount.Should().Be(2); // Only valid plugins should be loaded
        
        var loadedPlugins = await _pluginManager.GetLoadedPluginsAsync();
        loadedPlugins.Should().HaveCount(2);
        loadedPlugins.Should().Contain(p => p.Name == "Plugin1");
        loadedPlugins.Should().Contain(p => p.Name == "Plugin2");
    }

    [Fact]
    public async Task GetPluginsHealthStatusAsync_ReturnsHealthStatusForAllPlugins()
    {
        // Arrange
        var testPluginPath = await CreateTestPluginAsync();
        await _pluginManager.LoadPluginAsync(testPluginPath);

        // Act
        var healthStatus = await _pluginManager.GetPluginsHealthStatusAsync();

        // Assert
        healthStatus.Should().ContainKey("Test Plugin");
        healthStatus["Test Plugin"].Should().BeTrue(); // Mock plugin should be healthy
    }

    [Fact]
    public async Task GetPluginsByTypeAsync_ReturnsOnlyPluginsOfSpecifiedType()
    {
        // Arrange
        var smsPluginPath = await CreateTestPluginAsync("SMS Plugin", "1.0.0", "SMS");
        var emailPluginPath = await CreateTestPluginAsync("Email Plugin", "1.0.0", "Email");
        
        await _pluginManager.LoadPluginAsync(smsPluginPath);
        await _pluginManager.LoadPluginAsync(emailPluginPath);

        // Act
        var smsPlugins = await _pluginManager.GetPluginsByTypeAsync("SMS");

        // Assert
        smsPlugins.Should().HaveCount(1);
        smsPlugins.First().Name.Should().Be("SMS Plugin");
    }

    [Fact]
    public async Task SendNotificationAsync_WhenPluginExists_CallsPluginMethod()
    {
        // Arrange
        var testPluginPath = await CreateTestPluginAsync();
        await _pluginManager.LoadPluginAsync(testPluginPath);
        
        var request = new { Message = "Test message", PhoneNumber = "+**********" };

        // Act
        var result = await _pluginManager.SendNotificationAsync("Test Plugin", request);

        // Assert
        result.Should().NotBeNull();
        // The mock plugin should return a success response
    }

    [Fact]
    public async Task SendNotificationAsync_WhenPluginDoesNotExist_ReturnsNull()
    {
        // Arrange
        var request = new { Message = "Test message" };

        // Act
        var result = await _pluginManager.SendNotificationAsync("NonExistent Plugin", request);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task InvokePluginMethodAsync_WhenMethodExists_ReturnsResult()
    {
        // Arrange
        var testPluginPath = await CreateTestPluginAsync();
        await _pluginManager.LoadPluginAsync(testPluginPath);

        // Act
        var result = await _pluginManager.InvokePluginMethodAsync("Test Plugin", "ValidateConfigurationAsync", _configurationMock.Object);

        // Assert
        result.Should().NotBeNull();
    }

    private async Task<string> CreateTestPluginAsync(string name = "Test Plugin", string version = "1.0.0", string type = "SMS")
    {
        // Create a simple test assembly with embedded manifest
        var assemblyPath = Path.Combine(_testPluginsDirectory, $"{name.Replace(" ", "")}.dll");
        
        // For testing purposes, we'll copy the current test assembly and modify its manifest
        var currentAssembly = Assembly.GetExecutingAssembly();
        var currentAssemblyPath = currentAssembly.Location;
        
        // Copy the current assembly as a test plugin
        File.Copy(currentAssemblyPath, assemblyPath, true);
        
        // Create a manifest file that would be embedded in a real plugin
        var manifest = new
        {
            name = name,
            version = version,
            description = $"Test {type} plugin",
            type = type,
            provider = "Test",
            entryPoint = "NotifyMasterApi.Tests.TestPluginForManager",
            isEnabled = true,
            supportedFeatures = new[] { "Send", "BulkSend" }
        };
        
        // In a real scenario, this would be embedded in the assembly
        // For testing, we'll store it separately and mock the loading
        var manifestPath = Path.ChangeExtension(assemblyPath, ".manifest.json");
        await File.WriteAllTextAsync(manifestPath, System.Text.Json.JsonSerializer.Serialize(manifest));
        
        return assemblyPath;
    }

    public void Dispose()
    {
        // Clean up test directory
        if (Directory.Exists(_testPluginsDirectory))
        {
            Directory.Delete(_testPluginsDirectory, true);
        }
        
        _pluginManager?.Dispose();
    }
}

// Mock plugin class for testing
public class TestPluginForManager
{
    private readonly ILogger _logger;

    public TestPluginForManager(ILogger logger)
    {
        _logger = logger;
    }

    public async Task InitializeAsync(IConfiguration configuration)
    {
        await Task.CompletedTask;
    }

    public async Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        return await Task.FromResult(true);
    }

    public async Task<object> SendAsync(object request)
    {
        return await Task.FromResult(new { IsSuccess = true, MessageId = "test_123" });
    }

    public async Task<bool> HealthCheckAsync()
    {
        return await Task.FromResult(true);
    }
}
